{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "GP (debug)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "program": "lib/main_gp.dart",
      "preLaunchTask": "Setup skin: GP",
      "args": [
        "--flavor",
        "gp",
        "-t",
        "lib/main_gp.dart"
      ],
    },
    {
      "name": "Pre (debug)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "program": "lib/main_pre.dart",
      "preLaunchTask": "Setup skin: PRE",
      "args": [
        "--flavor",
        "pre",
        "-t",
        "lib/main_pre.dart"
      ]
    },
    {
      "name": "RSYP (debug)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "program": "lib/main_rsyp.dart",
      "preLaunchTask": "Setup skin: RSYP",
      "args": [
        "--flavor",
        "rsyp",
        "-t",
        "lib/main_rsyp.dart"
      ]
    },
    {
      "name": "YHXT (debug)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "program": "lib/main_yhxt.dart",
      "preLaunchTask": "Setup skin: YHXT",
      "args": [
        "--flavor",
        "yhxt",
        "-t",
        "lib/main_yhxt.dart"
      ]
    },
    {
      "name": "TempA (debug)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "program": "lib/main_tempa.dart",
      "preLaunchTask": "Setup skin: TEMPA",
      "args": [
        "--flavor",
        "tempa",
        "-t",
        "lib/main_tempa.dart"
      ]
    },
    {
      "name": "GP (release)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release",
      "program": "lib/main_gp.dart",
      "preLaunchTask": "Setup skin: GP",
      "args": [
        "--flavor",
        "gp",
        "-t",
        "lib/main_gp.dart"
      ]
    },
  ]
}