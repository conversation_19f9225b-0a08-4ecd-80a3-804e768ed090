{
  "cSpell.words": ["Akzidenz", "Grotesk", "Lucide", "watchlist"],
  "dart.previewFlutterUiGuides": true, // Visual guides for Flutter UI code
  "dart.previewFlutterUiGuidesCustomTracking": true,
  "dart.showInspectorNotificationsForWidgetErrors": true,
  "editor.bracketPairColorization.enabled": true,
  "editor.formatOnSave": true,
  "editor.formatOnType": true,
  "editor.guides.bracketPairs": true,
  "dart.lineLength": 120,
  "files.exclude": {
    "**/.DS_Store": true,
    "**/.git": true,
    "**/.githooks": false,
    "**/*.freezed.dart": true, // Set to true to hide generated freezed files
    "**/*.g.dart": true, // Set to true to hide generated g.dart files
    "**/*.gr.dart": true, // Set to true to hide generated router files
    "**/*.mocks.dart": true // Set to true to hide generated mock files
  },
  "search.exclude": {
    "**/.dart_tool": true,
    "**/.fvm": true,
    "**/.idea": true,
    "**/*.freezed.dart": true,
    "**/*.g.dart": true,
    "**/*.gr.dart": true,
    "**/*.mocks.dart": true,
    "**/build": true,
    "**/ios/Pods": true
  }
}
