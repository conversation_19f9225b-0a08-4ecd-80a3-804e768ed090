import 'package:flutter/material.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/tab_indicator/tc_underline_tab_indicator.dart';

class CommonTabBar extends StatefulWidget {
  final List<String> data;
  final int currentIndex; // 新增：外部传入
  final EdgeInsets? padding;
  final EdgeInsets? labelPadding;
  final ValueChanged<int>? onTap;
  final Color backgroundColor;
  final double height;
  final bool isScrollable;
  final TabAlignment? tabAlignment;
  final AppSkinStyle? skin;
  const CommonTabBar({
    super.key,
    required this.data,
    this.currentIndex = 0,
    this.padding,
    this.labelPadding,
    this.onTap,
    this.backgroundColor = Colors.white,
    this.height = 40,
    this.isScrollable = true,
    this.tabAlignment,
    this.skin,
  }) : assert(currentIndex >= 0 && currentIndex < data.length);

  @override
  State<CommonTabBar> createState() => _CommonTabBarState();
}

class _CommonTabBarState extends State<CommonTabBar> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.data.length,
      vsync: this,
      initialIndex: widget.currentIndex,
    );
    _tabController.addListener(_handleTabChange);
  }

  @override
  void didUpdateWidget(covariant CommonTabBar old) {
    super.didUpdateWidget(old);

    // 1. 如果 data 长度变化，重新创建 controller
    if (old.data.length != widget.data.length) {
      _tabController
        ..removeListener(_handleTabChange)
        ..dispose();
      _tabController = TabController(
        length: widget.data.length,
        vsync: this,
        initialIndex: widget.currentIndex.clamp(0, widget.data.length - 1),
      )..addListener(_handleTabChange);
    }

    // 2. 如果外部 currentIndex 变化，同步到 TabController
    if (old.currentIndex != widget.currentIndex && widget.currentIndex != _tabController.index) {
      _tabController.animateTo(widget.currentIndex);
    }
  }

  void _handleTabChange() {
    // 只有用户手指拖动/点击才触发，避免外部 animateTo 再反吹
    if (_tabController.indexIsChanging) {
      widget.onTap?.call(_tabController.index);
    }
  }

  @override
  void dispose() {
    _tabController
      ..removeListener(_handleTabChange)
      ..dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final skin = widget.skin ?? AppConfig.instance.skinStyle;
    final skinConfig = skin.config(context, currentIndex: _tabController.index, onTap: widget.onTap, data: widget.data);
    return Container(
      padding: widget.padding,
      color: widget.backgroundColor,
      height: widget.height,
      child: skinConfig.indicatorWidget ??
          TabBar(
            controller: _tabController,
            isScrollable: skinConfig.isScrollable,
            tabAlignment: widget.tabAlignment ?? skinConfig.tabAlignment,
            overlayColor: WidgetStateProperty.all(Colors.transparent),
            dividerColor: const Color.fromARGB(0, 189, 154, 154),
            labelPadding: widget.labelPadding ?? skinConfig.labelPadding,
            labelStyle: skinConfig.selectedStyle,
            unselectedLabelStyle: skinConfig.unselectedStyle,
            indicator: skinConfig.indicator,
            tabs: widget.data.map((str) => Tab(text: str)).toList(),
            indicatorPadding: EdgeInsets.symmetric(horizontal: -32),
          ),
    );
  }
}

class TabBarSkinConfig {
  final TabAlignment tabAlignment;
  final EdgeInsets? labelPadding;
  final Decoration? indicator;
  final TextStyle selectedStyle;
  final TextStyle unselectedStyle;
  final double height;
  final bool isScrollable;
  final Widget? indicatorWidget;
  TabBarSkinConfig({
    required this.tabAlignment,
    this.labelPadding,
    this.indicator,
    required this.selectedStyle,
    required this.unselectedStyle,
    required this.height,
    required this.isScrollable,
    this.indicatorWidget,
  });
}

extension SkinConfiguration on AppSkinStyle {
  TabBarSkinConfig config(BuildContext context, {int currentIndex = 0, Function(int)? onTap, required List<String> data}) {
    switch (this) {
      case AppSkinStyle.kGP:
        return TabBarSkinConfig(
          tabAlignment: TabAlignment.start,
          labelPadding: EdgeInsets.symmetric(horizontal: 15.gw),
          indicator: TCUnderlineTabIndicator(
            indicatorWidth:  data[currentIndex].length / 2 * 12.gw,
            indicatorHeight: 2,
            isRound: true,
            insets: EdgeInsets.fromLTRB(0, 0, 0, 6),
            borderSide: BorderSide(width: 2.0, color: context.theme.primaryColor),
          ),
          selectedStyle: context.textTheme.primary,
          unselectedStyle: context.textTheme.regular,
          height: 40,
          isScrollable: true,
        );
      case AppSkinStyle.kTemplateA || AppSkinStyle.kTemplateC || AppSkinStyle.kTemplateB:
        return TabBarSkinConfig(
          tabAlignment: TabAlignment.start,
          labelPadding: EdgeInsets.symmetric(horizontal: 15.gw),
          indicator: TCUnderlineTabIndicator(
            indicatorWidth: this == AppSkinStyle.kGP ? data[currentIndex].length / 2 * 12.gw : 20,
            indicatorHeight: 4,
            isRound: true,
            indicatorBottom: -6,
            insets: EdgeInsets.fromLTRB(0, 0, 0, 8),
            borderSide: BorderSide(width: 2.0, color: context.theme.primaryColor),
          ),
          selectedStyle: context.textTheme.primary,
          unselectedStyle: context.textTheme.regular,
          height: 40,
          isScrollable: true,
        );
      case AppSkinStyle.kTemplateD:
        final primaryColor = context.theme.primaryColor;
        final backgroundColor = context.theme.inputDecorationTheme.fillColor;
        return TabBarSkinConfig(
          tabAlignment: TabAlignment.start,
          labelPadding: EdgeInsets.symmetric(horizontal: 15.gw),
          selectedStyle: TextStyle(
            fontSize: 16.gsp,
            fontWeight: FontWeight.w600,
            color: context.theme.primaryColor,
          ),
          unselectedStyle: TextStyle( 
            fontSize: 14.gsp,
            fontWeight: FontWeight.w400,
            color: context.colorTheme.textRegular,
          ),
          height: 40,
          isScrollable: true,
          indicatorWidget: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () => onTap?.call(0),
                child: ClipPath(
                  clipper: LeftTabClipper(),
                  child: Container(
                    width: 0.42.gsw,
                    height: 48,
                    color: currentIndex == 0 ? primaryColor : backgroundColor,
                    alignment: Alignment.center,
                    child: Text(
                      data.firstOrNull ?? '',
                      style: TextStyle(
                        color: currentIndex == 0 ? Colors.white : primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () => onTap?.call(1),
                child: ClipPath(
                  clipper: RightTabClipper(),
                  child: Container(
                    width: 0.4.gsw,
                    height: 48,
                    color: currentIndex == 1 ? primaryColor : backgroundColor,
                    alignment: Alignment.center,
                    child: Text(
                      data[1] ?? '',
                      style: TextStyle(
                        color: currentIndex == 1 ? Colors.white : primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
    }
  }
}

class LeftTabClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final double width = size.width;
    final double height = size.height;

    final path_0 = Path();

    path_0.moveTo(0, height * 0.175); // M0 7
    path_0.cubicTo(
      0, height * 0.07835, // C0 3.13401
      width * 0.0212, 0, // 3.13401 0
      width * 0.0473, 0, // 7 0
    );
    path_0.lineTo(width * 0.9466, 0); // L140.09 0
    path_0.cubicTo(
      width * 0.99999, 0, // C144.924 0
      width * 1.002, height * 0.1196, // 148.303 4.78402
      width * 0.9914, height * 0.2335, // 146.687 9.34023
    );
    path_0.lineTo(width * 0.9288, height * 0.8835); // L137.464 35.3402
    path_0.cubicTo(
      width * 0.9225, height * 0.9533, // C136.473 38.1335
      width * 0.9037, height, // 133.83 40
      width * 0.8842, height, // 130.867 40
    );
    path_0.lineTo(width * 0.0473, height); // L6.99999 40
    path_0.cubicTo(
      width * 0, height, // C3.134 40
      0, height * 0.925, // 0 36.866
      0, height * 0.825, // 0 33
    );
    path_0.lineTo(0, height * 0.175); // L0 7
    path_0.close();

    return path_0;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => true;
}

class RightTabClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    double width = size.width;
    double height = size.height;

    return Path()
      ..moveTo(width, height * 0.825) // M148 33
      ..cubicTo(
        width, height * 0.9216, // C148 36.866
        width * 0.978, height, // 144.866 40
        width * 0.952, height, // 141 40
      )
      ..lineTo(width * 0.0535, height) // L7.91048 40
      ..cubicTo(
        width * 0.0208, height, // C3.07608 40
        width * -0.002, height * 0.8804, // -0.302979 35.216
        width * 0.0089, height * 0.7665, // 1.31325 30.6598
      )
      ..lineTo(width * 0.0712, height * 0.1165) // L10.5362 4.65977
      ..cubicTo(
        width * 0.0779, height * 0.0466, // C11.5271 1.86651
        width * 0.0957, 0, // 14.1696 0
        width * 0.1158, 0, // 17.1334 0
      )
      ..lineTo(width * 0.952, 0) // L141 0
      ..cubicTo(
        width * 0.999, 0, // C144.866 0
        width, height * 0.175, // 148 3.13401
        width, height * 0.175, // 148 7
      )
      ..lineTo(width, height * 0.825) // L148 33
      ..close();
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => true;
}
