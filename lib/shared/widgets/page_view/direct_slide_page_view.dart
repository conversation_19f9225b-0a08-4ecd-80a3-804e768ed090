import 'package:flutter/material.dart';

/// 支持手势分页和外部直接跳页动画的组件
class DirectSlideView extends StatefulWidget {
  /// 所有页面
  final List<Widget> pages;

  /// 当前页面索引
  final int pageIndex;

  /// 页面切换时的回调 (手势或动画结束时触发)
  final ValueChanged<int>? onPageChanged;

  /// 跳页动画时长
  final Duration duration;

  final ScrollPhysics? physics;

  DirectSlideView(
      {super.key,
      required this.pages,
      required this.pageIndex,
      this.onPageChanged,
      this.duration = const Duration(milliseconds: 300),
      this.physics})
      : assert(pages.isNotEmpty),
        assert(pageIndex >= 0 && pageIndex < pages.length);

  @override
  State createState() => _DirectSlideViewState();
}

class _DirectSlideViewState extends State<DirectSlideView> with SingleTickerProviderStateMixin {
  late AnimationController _ctrl;
  late PageController _pageController;

  final bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    _ctrl = AnimationController(vsync: this, duration: widget.duration);
    _pageController = PageController(initialPage: widget.pageIndex);
  }

  @override
  void didUpdateWidget(covariant DirectSlideView old) {
    super.didUpdateWidget(old);
    if (old.pageIndex != widget.pageIndex &&
        _pageController.page! % 1 == 0 &&
        _pageController.page != widget.pageIndex) {
      _pageController.jumpToPage(widget.pageIndex);
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _ctrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // if (_isAnimating) {
    //   final bool isForward = widget.pageIndex > _prevIndex;
    //   final int dir = isForward ? 1 : -1;
    //   return LayoutBuilder(builder: (context, box) {
    //     final width = box.maxWidth;
    //     return AnimatedBuilder(
    //       animation: _anim,
    //       builder: (context, child) {
    //         final oldOffset = -dir * width * _anim.value;
    //         final newOffset = dir * width * (1 - _anim.value);
    //         return Stack(
    //           children: [
    //             Transform.translate(
    //               offset: Offset(oldOffset, 0),
    //               child: SizedBox(
    //                 width: width,
    //                 height: box.maxHeight,
    //                 child: widget.pages[_prevIndex],
    //               ),
    //             ),
    //             Transform.translate(
    //               offset: Offset(newOffset, 0),
    //               child: SizedBox(
    //                 width: width,
    //                 height: box.maxHeight,
    //                 child: widget.pages[widget.pageIndex],
    //               ),
    //             ),
    //           ],
    //         );
    //       },
    //     );
    //   });
    // }
    return PageView.builder(
      controller: _pageController,
      onPageChanged: (idx) {
        widget.onPageChanged?.call(idx);
      },
      physics: widget.physics ?? const PageScrollPhysics(),
      itemCount: widget.pages.length,
      itemBuilder: (context, index) => widget.pages[index],
    );
  }
}
