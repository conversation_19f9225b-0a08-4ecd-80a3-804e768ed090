import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/tab/common_tab_bar.dart';

import 'direct_slide_page_view.dart';

class CommonTabPageView extends StatefulWidget {
  /// tab 的标题
  final List<String> tabs;

  /// 每个 tab 对应的页面
  final List<Widget> pages;

  /// 初始选中索引
  final int initialIndex;

  /// tab 切换回调
  final ValueChanged<int>? onTabChanged;

  /// pageView 切换回调
  final ValueChanged<int>? onPageChanged;

  const CommonTabPageView({
    super.key,
    required this.tabs,
    required this.pages,
    this.initialIndex = 0,
    this.onTabChanged,
    this.onPageChanged,
  }) : assert(tabs.length == pages.length);

  @override
  State<CommonTabPageView> createState() => _CommonTabPageViewState();
}

class _CommonTabPageViewState extends State<CommonTabPageView> {
  late int currentIndex;

  @override
  void initState() {
    super.initState();
    currentIndex = widget.initialIndex;
  }

  @override
  void didUpdateWidget(covariant CommonTabPageView oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果 tabs 或 pages 长度变了，或者内容变了，重置 currentIndex
    if (widget.tabs.length != oldWidget.tabs.length ||
        widget.pages.length != oldWidget.pages.length) {
      // 防止越界
      if (currentIndex >= widget.tabs.length) {
        setState(() {
          currentIndex = 0;
        });
      }
    }
    // 你也可以根据实际需求，判断内容变化时是否重置 currentIndex
  }

  /// tab 点击事件
  void _onTabTap(int index) {
    if (currentIndex != index) {
      setState(() {
        currentIndex = index;
      });
      // 通知外部 tab 切换
      widget.onTabChanged?.call(index);
    }
  }

  /// pageView 页面切换事件
  void _onPageChanged(int index) {
    if (currentIndex != index) {
      setState(() {
        currentIndex = index;
      });
      // 通知外部页面切换
      widget.onPageChanged?.call(index);
    }
  }

  @override
  Widget build(BuildContext context) {

    return Column(

      children: [
        CommonTabBar(
          data: widget.tabs,
          currentIndex: currentIndex,
          labelPadding: EdgeInsets.symmetric(horizontal: 20.gw),
          onTap: _onTabTap,
        ),
        Expanded(
          // 用 DirectSlidePageView 替代原生 PageView
          child: DirectSlideView(
            pages: widget.pages,
            pageIndex: currentIndex,
            onPageChanged: _onPageChanged,
          ),
        ),
      ],
    );
  }
}