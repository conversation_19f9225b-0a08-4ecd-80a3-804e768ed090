import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/features/account/domain/models/account_info/account_info_response.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AccountInfoState extends Equatable {
  final DataStatus accountInfoFetchStatus;
  final AccountInfoData? accountInfo;
  final String? error;

  const AccountInfoState({
    this.accountInfoFetchStatus = DataStatus.idle,
    this.accountInfo,
    this.error,
  });

  @override
  List<Object?> get props => [accountInfoFetchStatus, accountInfo, error];

  AccountInfoState copyWith({
    DataStatus? accountInfoFetchStatus,
    AccountInfoData? accountInfo,
    String? error,
  }) {
    return AccountInfoState(
      accountInfoFetchStatus: accountInfoFetchStatus ?? this.accountInfoFetchStatus,
      accountInfo: accountInfo ?? this.accountInfo,
      error: error ?? this.error,
    );
  }
}