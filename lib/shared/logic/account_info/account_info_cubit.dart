import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/features/account/domain/repository/account_repository.dart';
import 'package:gp_stock_app/features/profile/logic/auth_aware_cubit.dart';
import 'package:gp_stock_app/features/sign_in/domain/models/login/login_response.dart';
import 'package:gp_stock_app/features/sign_in/domain/repository/sign_in_repository.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_state.dart';
import 'package:gp_stock_app/shared/services/polling/polling_service.dart';
import 'package:injectable/injectable.dart';

@singleton
class AccountInfoCubit extends AuthAwareCubit<AccountInfoState> {
  final AccountRepository _accountService = getIt<AccountRepository>();
  final PollingService _pollingService = getIt<PollingService>();
  AccountInfoCubit(SignInRepository signInRepo) : super(signInRepo, const AccountInfoState());

  @override
  void onLoggedIn(LoginResponse loginResponse) => startAccountInfoPolling();

  @override
  void onLoggedOut() {
    stopAccountInfoPolling();
    emit(const AccountInfoState());
  }

  Future<void> getAccountInfo({bool isPolling = false}) async {
    if (isClosed) return;
    if (!isPolling) emit(state.copyWith(accountInfoFetchStatus: DataStatus.loading));
    try {
      final result = await _accountService.getAccountInfo();
      if (result.isSuccess) {
        emit(state.copyWith(accountInfoFetchStatus: DataStatus.success, accountInfo: result.data?.data));
      } else {
        emit(state.copyWith(accountInfoFetchStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(accountInfoFetchStatus: DataStatus.failed, error: e.toString()));
    }
  }

  void startAccountInfoPolling() => _pollingService.startPolling(
        'account_info_polling',
        (isActive) => getAccountInfo(isPolling: isActive),
      );

  void stopAccountInfoPolling() => _pollingService.stopPolling('account_info_polling');
}
