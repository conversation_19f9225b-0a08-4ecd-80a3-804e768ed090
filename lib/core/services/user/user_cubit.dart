import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/account.dart';
import 'package:gp_stock_app/core/models/entities/account.dart';
import 'package:gp_stock_app/core/models/entities/user.dart';
import 'package:gp_stock_app/core/utils/secure_storage_helper.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_cubit_v2.dart';
import 'package:gp_stock_app/shared/constants/keys.dart';
import 'package:gp_stock_app/shared/services/polling/polling_sevice_v2.dart';
import 'package:injectable/injectable.dart';

import 'user_state.dart';

/// 用户信息全局状态管理 Cubit
/// Global user info state management Cubit
@singleton
class UserCubit extends Cubit<UserState> {
  /// 构造函数，初始化时自动加载本地用户信息
  /// Constructor, auto load local user info on init
  UserCubit() : super(UserState()) {
    loadUser();
  }

  /// 设置 token，并保存到本地
  /// Set token and save to local storage
  void setToken(String? token) {
    SecureStorageHelper().writeSecureData(LocalStorageKeys.token, token ?? '');
    emit(state.copyWith(token: () => token, isLogin: token != null));
  }

  /// 设置用户信息，保存到本地并启动账户信息轮询
  /// Set user info, save to local and start account info polling
  void setUserInfo(UserModel? entity) {
    emit(state.copyWith(userInfo: () => entity));
    if (entity != null) {
      // 保存用户信息到本地
      // Save user info to local storage
      SecureStorageHelper().writeSecureData(LocalStorageKeys.userV2, jsonEncode(entity));
      startAccountInfoPolling();
      if (getIt.isRegistered<AccountScreenCubitV2>()) {
        getIt<AccountScreenCubitV2>().startDataPolling();
      }
    } else {
      _clearUser();
    }
  }

  /// 设置账户信息
  /// Set account info
  void setAccountInfo(AccountInfo? entity) {
    emit(state.copyWith(accountInfo: entity));
  }

  /// 拉取账户信息（需已登录）
  /// Fetch account info (must be logged in)
  void fetchAccountInfo() async {
    if (!state.isLogin) return;
    final result = await AccountApi.fetchAccountInfo();
    if (result != null && state.isLogin) setAccountInfo(result);
  }

  /// 启动账户信息轮询
  /// Start polling for account info
  void startAccountInfoPolling() {
    getIt<PollingServiceV2>().startPolling(
      id: kGPAccountInfoPolling,
      onPoll: () async {
        fetchAccountInfo();
        return true;
      },
      interval: const Duration(seconds: 5),
      shouldStop: () => !state.isLogin,
    );
  }

  /// 加载本地用户信息和 token
  /// Load user info and token from local storage
  Future<void> loadUser() async {
    final token = await SecureStorageHelper().readSecureData(LocalStorageKeys.token);
    if (token != null) setToken(token);

    final userInfoStr = await SecureStorageHelper().readSecureData(LocalStorageKeys.userV2);
    if (userInfoStr != null) {
      setUserInfo(UserModel.fromJson(jsonDecode(userInfoStr)));
    }
  }

  /// 清除用户信息（登出时调用）
  /// Clear user info (called on logout)
  Future<void> _clearUser() async {
    await SecureStorageHelper().deleteAllExcept();
    NetworkProvider.clearCache();
    getIt<PollingServiceV2>().stopAll();
    emit(UserState());
  }
}
