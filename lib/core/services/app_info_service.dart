import 'package:injectable/injectable.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../shared/constants/constants.dart';

/// Service to get app information like version, build number, etc.
///
@injectable
class AppInfoService {
  /// Get the app version in the format "v{version}+{buildNumber}"
  /// Example: v1.0.0+1
  Future<String> getAppVersion() async {
    try {
      final PackageInfo packageInfo = await PackageInfo.fromPlatform();
      return 'v${packageInfo.version}+${packageInfo.buildNumber}';
    } catch (e) {
      // Fallback to a default version if there's an error
      return kAppVersion;
    }
  }

  /// Get the app version without the build number
  /// Example: v1.0.0
  Future<String> getAppVersionWithoutBuild() async {
    try {
      final PackageInfo packageInfo = await PackageInfo.fromPlatform();
      return 'v${packageInfo.version}';
    } catch (e) {
      // Fallback to a default version if there's an error
      return kAppVersion;
    }
  }

  /// Get the app name
  /// Example: GP Exchange
  Future<String> getAppName() async {
    try {
      final PackageInfo packageInfo = await PackageInfo.fromPlatform();
      return packageInfo.appName;
    } catch (e) {
      // Fallback to a default name if there's an error
      return kAppName;
    }
  }

  /// Get the package name
  /// Example: com.example.app
  Future<String> getPackageName() async {
    try {
      final PackageInfo packageInfo = await PackageInfo.fromPlatform();
      return packageInfo.packageName;
    } catch (e) {
      // Fallback to a default package name if there's an error
      return kAppPackageName;
    }
  }
}
