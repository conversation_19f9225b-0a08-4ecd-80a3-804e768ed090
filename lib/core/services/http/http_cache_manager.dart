import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

enum CacheType {
  temporary,
  local,
}

class CacheEntry {
  final dynamic data;
  final DateTime timestamp;

  CacheEntry(this.data, this.timestamp);

  Map<String, dynamic> toJson() => {
        'data': data,
        'timestamp': timestamp.toIso8601String(),
      };

  static CacheEntry fromJson(Map<String, dynamic> json) {
    return CacheEntry(
      json['data'],
      DateTime.parse(json['timestamp']),
    );
  }
}

class HttpCacheManager {
  static final HttpCacheManager _instance = HttpCacheManager._internal();

  factory HttpCacheManager() => _instance;

  HttpCacheManager._internal();

  final Map<String, CacheEntry> _memoryCache = {};

  /// 写缓存
  Future<void> set(
    String key,
    dynamic data, {
    CacheType type = CacheType.temporary,
  }) async {
    final entry = CacheEntry(data, DateTime.now());

    if (type == CacheType.temporary) {
      _memoryCache[key] = entry;
    } else {
      final prefs = await SharedPreferences.getInstance();
      prefs.setString(key, jsonEncode(entry.toJson()));
    }
  }

  /// 读缓存
  Future<dynamic> get(
    String key, {
    CacheType type = CacheType.temporary,
    Duration? maxAge,
  }) async {
    CacheEntry? entry;

    if (type == CacheType.temporary) {
      entry = _memoryCache[key];
    } else {
      final prefs = await SharedPreferences.getInstance();
      final jsonStr = prefs.getString(key);
      if (jsonStr != null) {
        final jsonMap = jsonDecode(jsonStr) as Map<String, dynamic>;
        entry = CacheEntry.fromJson(jsonMap);
      }
    }

    if (entry == null) return null;

    if (maxAge != null) {
      final isExpired = DateTime.now().difference(entry.timestamp) > maxAge;
      if (isExpired) {
        await remove(key, type: type);
        return null;
      }
    }

    return entry.data;
  }

  /// 删除某个缓存
  Future<void> remove(
    String key, {
    CacheType type = CacheType.temporary,
  }) async {
    if (type == CacheType.temporary) {
      _memoryCache.remove(key);
    } else {
      final prefs = await SharedPreferences.getInstance();
      prefs.remove(key);
    }
  }

  /// 清空所有缓存
  Future<void> clear({
    CacheType? type,
  }) async {
    if (type == null || type == CacheType.temporary) {
      _memoryCache.clear();
    }
    if (type == null || type == CacheType.local) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    }
  }

  static String generateCacheKey(
      String path,
      Map<String, dynamic>? queryParameters,
      Object? params,
      ) {
    String queryPart = '';

    if (queryParameters != null && queryParameters.isNotEmpty) {
      final sortedEntries = queryParameters.entries.toList()
        ..sort((a, b) => a.key.compareTo(b.key));

      queryPart = sortedEntries
          .map((e) => '${e.key}=${e.value}')
          .join('&');
    }

    final paramsPart = params != null
        ? jsonEncode(params)
        : '';

    return '$path|$queryPart|$paramsPart';
  }
}
