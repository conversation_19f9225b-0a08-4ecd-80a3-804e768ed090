/// 网络请求方法枚举
/// Enum for HTTP request methods
enum HttpMethod {
  /// GET 请求：用于获取数据
  /// GET: Retrieve data from server
  get,

  /// POST 请求：用于提交数据
  /// POST: Submit data to server
  post,

  /// PUT 请求：用于更新整个资源
  /// PUT: Update a full resource
  put,

  /// DELETE 请求：用于删除资源
  /// DELETE: Remove a resource
  delete,

  /// PATCH 请求：用于部分更新资源
  /// PATCH: Partially update a resource
  patch,

  /// MOCK 数据请求（用于测试/调试）
  /// MOCK: Simulated request for testing/debug
  mock,
}

/// 网络请求配置
/// Configuration for HTTP requests
class HttpConfig {
  /// 连接超时时间（默认 20 秒）
  /// Connection timeout duration (default: 20 seconds)
  static const Duration connectTimeout = Duration(seconds: 20);

  /// 数据发送超时时间（默认 20 秒）
  /// Send timeout duration (default: 20 seconds)
  static const Duration sendTimeout = Duration(seconds: 20);

  /// 数据接收超时时间（默认 20 秒）
  /// Receive timeout duration (default: 20 seconds)
  static const Duration receiveTimeout = Duration(seconds: 20);
}
