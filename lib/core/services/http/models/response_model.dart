import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';

/** {
    "msg": "success",
    "code": 1,
    "data": [
    {
    "key": "value",
    }
    ],
    } */

/** {
    "msg": "success",
    "code": 1,
    "data": {
    "key": "value",
    },
    } */

///
class ResponseModel<T> {
  T? data;
  int? code;
  String? msg;

  ResponseModel({
    this.data,
    this.code,
    this.msg,
  });

  bool get isSuccess => code == 0;

  ResponseModel.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null && json['data'] != 'null') {
      var processedData = json['data'];

      if (processedData is List) {
        data = JsonConvert.fromJsonAsT<T>({"list": processedData});
      } else {
        data = JsonConvert.fromJsonAsT<T>(processedData);
      }
    }
    code = json['code'];
    msg = json['msg'];
  }
}
