import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/generated/json/account.g.dart';
import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'dart:convert';

@JsonSerializable()
class AccountInfo extends Equatable {
  /// 账户总资产：合约账户
  double accountAmount = 0;

  /// 现货账户总资产：现货持仓市值
  double assetAmount = 0;

  /// 现货id
  int assetId = 0;

  /// 法币类型
  String currency = '';

  /// 现货冻结现金
  double freezeCash = 0;

  /// 利息券
  double interestCash = 0;

  /// 今日收益：废弃
  double todayWinAmount = 0;

  /// 今日收益率：废弃
  double todayWinRate = 0;

  /// 现货可用现金
  double usableCash = 0;

  /// 浮动盈亏
  double profitLoss = 0;

  AccountInfo();

	factory AccountInfo.fromJson(Map<String, dynamic> json) => $AccountInfoFromJson(json);

	Map<String, dynamic> toJson() => $AccountInfoToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}

  @override
  List<Object?> get props => [
		accountAmount,
		assetAmount,
		assetId,
		currency,
		freezeCash,
		interestCash,
		todayWinAmount,
		todayWinRate,
		usableCash,
	];
}



/// 交易详情/持仓信息
@JsonSerializable()
class PositionEntity {
	double appendMargin = 0;
	int availableMargin = 0;
	double buyAvgPrice = 0;
	double buyTotalNum = 0;
	dynamic closeLine;
	double costPrice = 0;
	String createTime = '';
	String currency = '';
	int direction = 0;
	double disableNum = 0;
	double distanceCloseLine = 0;
	double distanceWarningLine = 0;
	double feeAmount = 0;
	double floatingProfitLoss = 0;
	double floatingProfitLossRate = 0;
	int id = 0;
	double marginAmount = 0;
	double marginRatio = 0;
	String market = '';
	double marketValue = 0;
	String orderNo = '';
	int positionDays = 0;
	double positionTotalNum = 0;
	double restNum = 0;
	String securityType = '';
	int stockPrice = 0;
	double stopLossValue = 0;
	String symbol = '';
	String symbolName = '';
	double takeProfitValue = 0;
	int tradeType = 0;
	double tradeUnit = 0;
	int type = 0;
	int warningLine = 0;

	PositionEntity();

	factory PositionEntity.fromJson(Map<String, dynamic> json) => $PositionEntityFromJson(json);

	Map<String, dynamic> toJson() => $PositionEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}