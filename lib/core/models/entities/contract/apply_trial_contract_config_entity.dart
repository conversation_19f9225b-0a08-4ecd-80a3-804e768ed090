import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/apply_trial_contract_config_entity.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/apply_trial_contract_config_entity.g.dart';

@JsonSerializable()
class ApplyTrialContractConfigEntity {
	List<ApplyTrialContractConfigActivityRiskMap> activityRiskMap = [];
	String currency = '';
	double interestCash = 0;
	double useAmount = 0;

	ApplyTrialContractConfigEntity();

	factory ApplyTrialContractConfigEntity.fromJson(Map<String, dynamic> json) => $ApplyTrialContractConfigEntityFromJson(json);

	Map<String, dynamic> toJson() => $ApplyTrialContractConfigEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApplyTrialContractConfigActivityRiskMap {
	int activityId = 0;
	String activityRule = '';
	List<double> applyAmountList = [];
	double closeLossRadio = 0;
	double giveAmount = 0;
	int giveDay = 0;
	double giveRatio = 0;
	double interestRate = 0;
	String marketType = '';
	double minApplyAmount = 0;
	int multiple = 0;
	int riskId = 0;
	double shareRatio = 0;
	double warnLossRadio = 0;

	ApplyTrialContractConfigActivityRiskMap();

	factory ApplyTrialContractConfigActivityRiskMap.fromJson(Map<String, dynamic> json) => $ApplyTrialContractConfigActivityRiskMapFromJson(json);

	Map<String, dynamic> toJson() => $ApplyTrialContractConfigActivityRiskMapToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}