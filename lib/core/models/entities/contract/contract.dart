import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/contract.g.dart';
import 'dart:convert';

import 'package:gp_stock_app/shared/constants/enums.dart';

@JsonSerializable()
class ContractSummaryPageEntity {
	int current = 0;
	bool hasNext = false;
	List<ContractSummaryPageRecord> records = [];
	int total = 0;

	ContractSummaryPageEntity();

	factory ContractSummaryPageEntity.fromJson(Map<String, dynamic> json) => $ContractSummaryPageEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractSummaryPageEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractSummaryPageRecord {
	double accountWinAmount = 0.0;
	double allAsset = 0.0;
	double closeRemindAmount = 0.0;
	double contractAssetAmount = 0.0;
	double coverLossAmount = 0.0;
	String currency = '';
	double expendAmount = 0.0;
	String expireTime = '';
	double freezePower = 0.0;
	double gapCloseRemindAmount = 0.0;
	double gapWarnRemindAmount = 0.0;
	double giveAmount = 0.0;
	int id = 0;
	double initCash = 0.0;
	double interestAmount = 0.0;
	double interestRate = 0.0;
	String marketType = '';
	int multiple = 0;
	double negativeAmount = 0.0;
	String openTime = '';
	int periodType = 0;
	double positionAmount = 0.0;
	double receivableInterest = 0.0;
	int settlementStatus = 0;
	double todayWinAmount = 0.0;
	double todayWinRate = 0.0;
	int totalAccountAmount = 0;
	double totalCash = 0.0;
	double totalFinance = 0.0;
	double totalPower = 0.0;
	int type = 0;
	double useAmount = 0.0;
	double warnRemindAmount = 0.0;
	double winRate = 0.0;
	double winAmount = 0.0;
	double withdrawAmount = 0.0;
	int yesterdayAsset = 0;
	bool isAutoRenew = false;


	String get label => getContractLabelByContractSummaryPageRecord(this);

	ContractType get contractType => ContractType.fromValue(type);

	ContractSummaryPageRecord();

	factory ContractSummaryPageRecord.fromJson(Map<String, dynamic> json) => $ContractSummaryPageRecordFromJson(json);

	Map<String, dynamic> toJson() => $ContractSummaryPageRecordToJson(this);


	@override
	String toString() {
		return jsonEncode(this);
	}
}


@JsonSerializable()
class ContractMarginEntity {
	List<ContractMarginAmountList> amountList = [];
	List<double> bonusAmountList = [];
	double closeAmount = 0;
	double closeValue = 0;
	int giveDay = 0;
	int id = 0;
	double initCash = 0;
	double interestRate = 0.0;
	String marketType = '';
	int multiple = 0;
	int negativeAmount = 0;
	int periodType = 0;
	double totalCash = 0;
	double totalFinance = 0;
	double totalPower = 0;
	int type = 0;
	double warnAmount = 0;
	double warnValue = 0;


	String? get typeText =>  marginCallTypeTranslation[type]?.tr();

	String? get contractTypeText => contractMarketTranslation[marketType];

	String? get periodTypeText =>  'contract.period_$periodType'.tr();

	String? get marketTypeText =>  contractMarketTranslation[marketType]?.tr();

	ContractType get contractType => ContractType.fromValue(type);

	ContractMarginEntity();

	factory ContractMarginEntity.fromJson(Map<String, dynamic> json) => $ContractMarginEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractMarginEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractMarginAmountList {
	int applyAmount = 0;
	int id = 0;

	ContractMarginAmountList();

	factory ContractMarginAmountList.fromJson(Map<String, dynamic> json) => $ContractMarginAmountListFromJson(json);

	Map<String, dynamic> toJson() => $ContractMarginAmountListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}


@JsonSerializable()
class ProfitWithdrawalConfigEntity {
	double amount = 0;
	bool canUse = false;
	double dailyProfitWithdrawalCount = 0;
	String endTime = '';
	double maxProfitWithdrawalAmount = 0;
	double minProfitWithdrawalAmount = 0;
	String startTime = '';

	ProfitWithdrawalConfigEntity();

	factory ProfitWithdrawalConfigEntity.fromJson(Map<String, dynamic> json) => $ProfitWithdrawalConfigEntityFromJson(json);

	Map<String, dynamic> toJson() => $ProfitWithdrawalConfigEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractApplyAmountEntity {
	double canUserCashCNY = 0;
	double deductCanUseCashCNY = 0;
	double deductInterestCashCNY = 0;
	double discountInterestCNY = 0;
	double exchangeRate = 0;
	double interestCashCNY = 0;
	double rate = 0;
	double rateAmount = 0;

	ContractApplyAmountEntity();

	factory ContractApplyAmountEntity.fromJson(Map<String, dynamic> json) => $ContractApplyAmountEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractApplyAmountEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}