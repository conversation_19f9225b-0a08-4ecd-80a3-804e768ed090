import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/apply_normal_contract_config_entity.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/apply_normal_contract_config_entity.g.dart';

@JsonSerializable()
class ApplyNormalContractConfigEntity {
	List<ApplyNormalContractConfigAmountList> amountList = [];
	List<ApplyNormalContractConfigContractConfigMap> contractConfigMap = [];
	String currency = '';
	int interestCash = 0;
	List<ApplyNormalContractConfigRuleMap> ruleMap = [];
	double useAmount = 0;

	ApplyNormalContractConfigEntity();

	factory ApplyNormalContractConfigEntity.fromJson(Map<String, dynamic> json) => $ApplyNormalContractConfigEntityFromJson(json);

	Map<String, dynamic> toJson() => $ApplyNormalContractConfigEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApplyNormalContractConfigAmountList {
	int applyAmount = 0;
	int id = 0;

	ApplyNormalContractConfigAmountList();

	factory ApplyNormalContractConfigAmountList.fromJson(Map<String, dynamic> json) => $ApplyNormalContractConfigAmountListFromJson(json);

	Map<String, dynamic> toJson() => $ApplyNormalContractConfigAmountListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApplyNormalContractConfigContractConfigMap {
	List<ApplyNormalContractConfigContractConfigMapConfigList> configList = [];
	int periodType = 0;

	ApplyNormalContractConfigContractConfigMap();

	factory ApplyNormalContractConfigContractConfigMap.fromJson(Map<String, dynamic> json) => $ApplyNormalContractConfigContractConfigMapFromJson(json);

	Map<String, dynamic> toJson() => $ApplyNormalContractConfigContractConfigMapToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApplyNormalContractConfigContractConfigMapConfigList {
	int id = 0;
	double interestRate =0;
	int multiple = 0;

	ApplyNormalContractConfigContractConfigMapConfigList();

	factory ApplyNormalContractConfigContractConfigMapConfigList.fromJson(Map<String, dynamic> json) => $ApplyNormalContractConfigContractConfigMapConfigListFromJson(json);

	Map<String, dynamic> toJson() => $ApplyNormalContractConfigContractConfigMapConfigListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApplyNormalContractConfigRuleMap {
	int closeLossRadio = 0;
	int id = 0;
	String market = '';
	int warnLossRadio = 0;

	ApplyNormalContractConfigRuleMap();

	factory ApplyNormalContractConfigRuleMap.fromJson(Map<String, dynamic> json) => $ApplyNormalContractConfigRuleMapFromJson(json);

	Map<String, dynamic> toJson() => $ApplyNormalContractConfigRuleMapToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}