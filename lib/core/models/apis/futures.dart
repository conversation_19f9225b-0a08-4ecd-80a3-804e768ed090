import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

/// 期货相关接口
class FutureApi {
  /// 设置期货止盈止损
  static Future<bool> setFStopLine({
    required int positionId,
    double? takeProfitValue,
    double? stopLossValue,
  }) async {
    final res = await Http().request(
      ApiEndpoints.setFutureStopLine,
      params: {
        'positionId': positionId,
        if (takeProfitValue != null) 'takeProfitValue': takeProfitValue,
        if (stopLossValue != null) 'stopLossValue': stopLossValue,
      },
    );
    return res.data == true;
  }

  /// 期货 追加保证金
  static Future<bool> addMargin({
    required int positionId,
    required double amount,
  }) async {
    final res = await Http().request(
      ApiEndpoints.addFutureMargin,
      params: {
        'positionId': positionId,
        'addMarginAmount': amount,
      },
    );
    return res.data == true;
  }
}
