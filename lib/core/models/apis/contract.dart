import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/account.dart';
import 'package:gp_stock_app/core/models/entities/contract/apply_trial_contract_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/core/services/http/http_config.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';

class ContractApi {
  /// 获取合约列表 isSettlement：是否已结算
  static Future<ContractSummaryPageEntity?> fetchContractSummary({int page = 1, bool isSettlement = false}) async {
    final res = await Http().request<ContractSummaryPageEntity>(
      ApiEndpoints.getContractSummaryPage,
      method: HttpMethod.get,
      queryParameters: {
        'settlementStatus': isSettlement ? 2 : 1,
        'pageNumber': page,
        'pageSize': 20,
      },
    );
    return res.data;
  }

  /// 获取合约保证金详情
  static Future<ContractMarginEntity?> fetchContractMarginDetail({required int contractId}) async {
    final res = await Http().request<ContractMarginEntity>(
      '${ApiEndpoints.getContractAccountDetail}/$contractId',
      method: HttpMethod.get,
    );
    return res.data;
  }

  /// 追加保证金
  static Future<bool> operateExpandMargin({
    required int contractId,
    required double applyAmount,
    required int type,
  }) async {
    final res = await Http().request(
      ApiEndpoints.addExpandMargin,
      params: {
        'contractId': contractId,
        'applyAmount': applyAmount,
        'type': type,
      },
    );
    return res.isSuccess;
  }

  static Future<bool> operateRenewalContract({required int contractId}) async {
    final res = await Http().request(
      ApiEndpoints.renewalContract,
      params: {'contractId': contractId},
    );
    return res.isSuccess;
  }

  /// 获取现货订单详情
  static Future<PositionEntity?> fetchPositionDetail({required int id}) async {
    final res = await Http().request<PositionEntity>(
      '${ApiEndpoints.getPositionDetail}/$id',
      method: HttpMethod.get,
    );
    return res.data;
  }

  /// 获取单个合约的持仓信息
  static Future<ContractSummaryPageRecord?> fetchContractSummaryDetail({required int contractId}) async {
    // https://h5.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E5%90%88%E7%BA%A6%E4%B8%9A%E5%8A%A1/getContractSummaryUsingGET_1
    final res = await Http().request<ContractSummaryPageRecord>(
      '${ApiEndpoints.getContractSummary}/$contractId',
      method: HttpMethod.get,
    );
    return res.data;
  }

  /// 持仓列表
  static Future<FTradeAcctOrderModel?> getPositionList({
    int page = 1,
    int? pageSize = 20,
    int? contractId,
    String? symbol,
    String? market,
    String? securityType,
    String? commentAssetId,
    int? dataType, // 	数据类型 1:A股 2：港股 3：美股 4：股指 5：国内期货
    CancelToken? cancelToken,
  }) async {
    final res = await Http().request<FTradeAcctOrderModel>(
      ApiEndpoints.getPositionList,
      method: HttpMethod.get,
      queryParameters: {
        'pageNumber': page,
        'pageSize': pageSize,
        if (dataType != null) 'dataType': dataType,
        if (contractId != null) 'contractId': contractId,
        if (symbol != null) 'symbol': symbol,
        if (market != null) 'market': market,
        if (securityType != null) 'securityType': securityType,
        if (commentAssetId != null && contractId == null) 'commentAssetId': commentAssetId,
      },
      cancelToken: cancelToken,
    );

    return res.data;
  }

  /// 订单列表：委托明细、成交明细
  static Future<FTradeAcctOrderModel?> getOrderList({
    int page = 1,
    int? pageSize = 20,
    int? status, // 订单委托状态： 0 委托中 1委托撤销 2.订单成交成功 3.合约到期自动撤销
    int? contractId, // 合约账户id
    String? symbol,
    String? market, // 市场
    String? securityType, // 股票类型
    String? commentAssetId, // 现货账户id
    int? dataType, // 	数据类型 1:A股 2：港股 3：美股 4：股指 5：国内期货
    CancelToken? cancelToken,
  }) async {
    /// https://h5.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E8%AE%A2%E5%8D%95%E7%9B%B8%E5%85%B3/pageUsingGET_11
    final res = await Http().request<FTradeAcctOrderModel>(
      ApiEndpoints.getOrderList,
      method: HttpMethod.get,
      queryParameters: {
        'pageNum': page,
        'pageSize': pageSize,
        if (dataType != null) 'dataType': dataType,
        if (contractId != null) 'contractId': contractId,
        if (status != null) 'status': status,
        if (symbol != null) 'symbol': symbol,
        if (market != null) 'market': market,
        if (securityType != null) 'securityType': securityType,
        if (commentAssetId != null && contractId == null) 'commentAssetId': commentAssetId,
      },
      cancelToken: cancelToken,
    );
    return res.data;
  }

  /// 获取合约提盈config
  static Future<ProfitWithdrawalConfigEntity?> fetchWithdrawalConfig({required int contractId}) async {
    final res = await Http().request<ProfitWithdrawalConfigEntity>(
      '${ApiEndpoints.getWithdrawAmount}/$contractId',
      method: HttpMethod.get,
    );
    return res.data;
  }

  /// 获取 提交提现申请
  static Future<bool> submitWithdraw({required String applyAmount, required int id}) async {
    final res = await Http().request(
      ApiEndpoints.contractWithdraw,
      params: {
        'applyAmount': applyAmount,
        'id': id,
      },
    );
    return res.isSuccess;
  }

  static Future<ApplyTrialContractConfigEntity?> fetchTrialContractConfig({
    required int type,
    required int parentType,
  }) async {
    final res = await Http().request<ApplyTrialContractConfigEntity>(
      ApiEndpoints.getContractActivity,
      method: HttpMethod.get,
      queryParameters: {
        'type': type,
        'parentType': parentType,
      },
    );
    return res.data;
  }

  static Future<bool> applyTrialContract({
    required int activityId,
    required int activityRiskId,
    required double applyAmount,
    required int type,
    bool isBonus = false,
  }) async {
    final res = await Http().request(
      isBonus ? ApiEndpoints.applyBonusContract : ApiEndpoints.applyExperienceContract,
      params: {
        'activityId': activityId,
        'activityRiskId': activityRiskId,
        'applyAmount': applyAmount,
        'type': type,
      },
    );
    return res.isSuccess;
  }

  static Future<ContractApplyAmountEntity?> getBonusContractAmount({
    required int activityId,
    required int activityRiskId,
    required int applyAmount,
    required int type,
  }) async {
    final res = await Http().request<ContractApplyAmountEntity>(
      ApiEndpoints.getBonusContractAmount,
      method: HttpMethod.get,
      queryParameters: {
        'activityId': activityId,
        'activityRiskId': activityRiskId,
        'applyAmount': applyAmount,
        'type': type,
      },
    );
    return res.data;
  }

  static Future<bool> terminateContract({required String contractId}) async {
    final res = await Http().request(
      ApiEndpoints.endContract,
      params: {'contractId': contractId},
    );
    return res.isSuccess;
  }
}
