import 'dart:convert';
import 'package:encrypt/encrypt.dart' as encryp;
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/utils/log.dart';

class AESEncryption {
  static final AESEncryption _instance = AESEncryption._internal();
  factory AESEncryption() => _instance;

  AESEncryption._internal() {
    _initializeEncrypter();
  }

  late final encryp.Encrypter _encrypter;
  late final encryp.IV _iv;

  String get _encryptionKey => AppConfig.instance.encryptionKey;

  void _initializeEncrypter() {
    try {
      final key = encryp.Key.fromUtf8(_encryptionKey);
      _iv = encryp.IV.fromLength(16); // Use a consistent IV for ECB mode
      _encrypter = encryp.Encrypter(
        encryp.AES(
          key,
          mode: encryp.AESMode.ecb,
          padding: 'PKCS7',
        ),
      );
      LogD('Encrypter initialized successfully');
    } catch (e) {
      LogE('Encrypter initialization error: $e');
    }
  }

  String encrypt(String data) {
    try {
      final encrypted = _encrypter.encrypt(data, iv: _iv);
      return encrypted.base64;
    } catch (e) {
      LogE('Encryption error: $e');
      return data;
    }
  }

  String decrypt(String encryptedData) {
    try {
      final decrypted = _encrypter.decrypt(
        encryp.Encrypted.fromBase64(encryptedData),
        iv: _iv,
      );
      return decrypted;
    } catch (e) {
      LogE('Decryption error: $e');
      return encryptedData;
    }
  }

  /// Handles response data which might be a string or map
  dynamic decryptResponse(dynamic response) {
    if (response is String) {
      try {
        // First try to decrypt the string
        final decrypted = decrypt(response);
        // Then try to parse it as JSON
        return jsonDecode(decrypted);
      } catch (e) {
        LogE('Response decryption error: $e');
        return response;
      }
    } else if (response is Map<String, dynamic>) {
      return decryptMap(response);
    }
    return response;
  }

  Map<String, dynamic> encryptMap(Map<String, dynamic> data, {bool skipEncryption = false}) {
    if (skipEncryption) return data;

    return Map<String, dynamic>.fromEntries(
      data.entries.map((entry) {
        final value = entry.value;
        if (value == null) return entry;
        return MapEntry(entry.key, _encryptValue(value));
      }),
    );
  }

  dynamic _encryptValue(dynamic value) {
    if (value is String) {
      return encrypt(value);
    }
    if (value is num || value is bool) {
      return encrypt(value.toString());
    }
    if (value is Map) {
      return jsonEncode(encryptMap(Map<String, dynamic>.from(value)));
    }
    if (value is List) {
      return jsonEncode(_encryptList(value));
    }
    return encrypt(value.toString());
  }

  List<dynamic> _encryptList(List<dynamic> list) {
    return list.map((item) {
      if (item is Map) {
        return encryptMap(Map<String, dynamic>.from(item));
      }
      if (item is List) {
        return _encryptList(item);
      }
      return item;
    }).toList();
  }

  Map<String, dynamic> decryptMap(Map<String, dynamic> encryptedData, {bool skipDecryption = false}) {
    if (skipDecryption) return encryptedData;

    return Map<String, dynamic>.fromEntries(
      encryptedData.entries.map((entry) {
        final value = entry.value;
        if (value == null) return entry;
        return MapEntry(entry.key, _decryptValue(value));
      }),
    );
  }

  dynamic _decryptValue(dynamic value) {
    if (value is! String) {
      if (value is Map) {
        return decryptMap(Map<String, dynamic>.from(value));
      }
      if (value is List) {
        return _decryptList(value);
      }
      return value;
    }

    try {
      // First try to decrypt the string
      final decrypted = decrypt(value);
      // Then try to parse as JSON
      try {
        final decoded = jsonDecode(decrypted);
        if (decoded is Map) {
          return decryptMap(Map<String, dynamic>.from(decoded));
        }
        if (decoded is List) {
          return _decryptList(decoded);
        }
        return decrypted;
      } catch (_) {
        // If not valid JSON, return decrypted string
        return decrypted;
      }
    } catch (_) {
      return value;
    }
  }

  List<dynamic> _decryptList(List<dynamic> list) {
    return list.map((item) {
      if (item is Map) {
        return decryptMap(Map<String, dynamic>.from(item));
      }
      if (item is List) {
        return _decryptList(item);
      }
      return item;
    }).toList();
  }
}
