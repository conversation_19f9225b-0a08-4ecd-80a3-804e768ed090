import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../features/main/domain/enums/navigation_item.dart';
import '../../features/main/logic/main/main_cubit.dart';
import '../../shared/models/action_model.dart';
import '../../shared/routes/routes.dart';

class AppNavigationHandler {
  // Dictionary for jumpUrl integer values to routes
  // Map format: jumpUrl -> (route, needsAuth)
  static final Map<String, (String, bool)> routeDict = {
    '1': (routeDepositMain, true),
    '2': (routeInvite, true),
    '5': (routeMissionCenter, true),
  };

  // Special handling for routes that need arguments
  // Map format: jumpUrl -> (route, needsAuth, argumentsBuilder)
  static final Map<String, (String, bool, Map<String, dynamic> Function())> specialRouteDict = {
    '4': (routeContractApply, true, () => {'mainContractType': MainContractType.stock}),
    // Add future special routes here as needed
  };

  // Special handling for tab navigation
  // Map format: jumpUrl -> (NavigationItem, needsAuth)
  static final Map<String, (NavigationItem, bool)> tabNavigationDict = {
    '3': (NavigationItem.trade, false),
    // Add future tab indices here as needed
  };

  static void handleNavigation(
    BuildContext context, {
    required int? jumpType,
    required String? jumpUrl,
    bool requireAuth = false,
  }) {
    if (jumpType == null || jumpUrl == null || jumpUrl.isEmpty) return;

    void navigate() {
      if (jumpType == 1) {
        // Check if it's a tab navigation
        if (tabNavigationDict.containsKey(jumpUrl)) {
          final (navigationItem, needsAuth) = tabNavigationDict[jumpUrl]!;

          if (needsAuth) {
            context.verifyAuth(() => context.read<MainCubit>().selectedNavigationItem(navigationItem));
          } else {
            context.read<MainCubit>().selectedNavigationItem(navigationItem);
          }
        }
        // Check if it's a special route that needs arguments
        else if (specialRouteDict.containsKey(jumpUrl)) {
          final (route, needsAuth, argumentsBuilder) = specialRouteDict[jumpUrl]!;
          final arguments = argumentsBuilder();

          if (needsAuth) {
            context.verifyAuth(() => Navigator.pushNamed(context, route, arguments: arguments));
          } else {
            Navigator.pushNamed(context, route, arguments: arguments);
          }
        }
        // Check if it's a regular route navigation
        else if (routeDict.containsKey(jumpUrl)) {
          final (route, needsAuth) = routeDict[jumpUrl]!;

          if (needsAuth) {
            context.verifyAuth(() => Navigator.pushNamed(context, route));
          } else {
            Navigator.pushNamed(context, route);
          }
        }
      } else if (jumpType == 2) {
        // For URLs, check if authentication is required
        if (requireAuth) {
          context.verifyAuth(() => launchUrl(Uri.parse(jumpUrl), mode: LaunchMode.externalApplication));
        } else {
          launchUrl(Uri.parse(jumpUrl), mode: LaunchMode.externalApplication);
        }
      }
    }

    // If the overall navigation requires auth (regardless of specific route)
    if (requireAuth) {
      context.verifyAuth(() => navigate());
    } else {
      navigate();
    }
  }

  static ActionModel getNavigationAction(int? jumpType, String? jumpUrl) {
    if (jumpType == null || jumpUrl == null) return ActionModel();

    if (jumpType == 1) {
      // Tab navigation
      if (tabNavigationDict.containsKey(jumpUrl)) {
        final (navigationItem, _) = tabNavigationDict[jumpUrl]!; // We don't need the auth flag here
        return ActionModel(actionType: ActionType.switchTab, bottomBarIndex: navigationItem, tabIndex: 0);
      }
      // Special route that needs arguments
      else if (specialRouteDict.containsKey(jumpUrl)) {
        final (route, _, _) = specialRouteDict[jumpUrl]!; // We only need the route name here
        return ActionModel(actionType: ActionType.navigate, routeName: route);
      }
      // Regular route navigation
      else if (routeDict.containsKey(jumpUrl)) {
        final (route, _) = routeDict[jumpUrl]!; // We don't need the auth flag here
        return ActionModel(actionType: ActionType.navigate, routeName: route);
      }
    }

    return ActionModel();
  }
}
