
import 'dart:async';

extension FutureListExtensions<T> on List<Future<T>> {
  /// 返回第一个成功的结果（不会跳过 null）
  /// Returns the first successful result (does not skip null)
  Future<T> firstSuccessful() {
    if (isEmpty) {
      return Future.error(Exception('Future 列表为空 / Future list is empty'));
    }

    final completer = Completer<T>();
    int completedCount = 0;

    for (final future in this) {
      future.then((value) {
        if (!completer.isCompleted) {
          completer.complete(value);
        }
      }).catchError((_) {
        completedCount++;
        if (completedCount == length && !completer.isCompleted) {
          completer.completeError(Exception('所有 Future 失败 / All futures failed'));
        }
      });
    }

    return completer.future;
  }
}

extension NullableFutureListExtensions<T> on List<Future<T?>> {
  /// 返回第一个成功且非 null 的结果
  /// Returns the first successful and non-null result
  Future<T?> firstNonNullSuccessful() {
    if (isEmpty) return Future.value(null);

    final completer = Completer<T?>();
    int completed = 0;

    for (final future in this) {
      future.then((value) {
        if (!completer.isCompleted && value != null) {
          completer.complete(value);
        } else {
          completed++;
          if (completed == length && !completer.isCompleted) {
            completer.complete(null);
          }
        }
      }).catchError((_) {
        completed++;
        if (completed == length && !completer.isCompleted) {
          completer.complete(null);
        }
      });
    }

    return completer.future;
  }
}

extension DedupMergeList<T> on List<T>? {
  /// 保留后出现的元素（默认）
  List<T> mergeDedupKeepLast(List<T>? other, Object Function(T) keySelector) {
    final map = <Object, T>{};
    for (var item in [...?this, ...?other]) {
      map[keySelector(item)] = item;
    }
    return map.values.toList();
  }

  /// 保留先出现的元素（你现在需要的）
  List<T> mergeDedupKeepFirst(List<T>? other, Object Function(T) keySelector) {
    final map = <Object, T>{};
    for (var item in [...?this, ...?other]) {
      final key = keySelector(item);
      map.putIfAbsent(key, () => item); // 只在 key 不存在时加入
    }
    return map.values.toList();
  }
}