// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contract_activity_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ContractActivityResponseImpl _$$ContractActivityResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ContractActivityResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : ContractActivityData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$ContractActivityResponseImplToJson(
        _$ContractActivityResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$ContractActivityDataImpl _$$ContractActivityDataImplFromJson(
        Map<String, dynamic> json) =>
    _$ContractActivityDataImpl(
      activityRiskMap: (json['activityRiskMap'] as List<dynamic>?)
          ?.map((e) => ActivityRiskMap.fromJson(e as Map<String, dynamic>))
          .toList(),
      currency: json['currency'] as String?,
      interestCash: (json['interestCash'] as num?)?.toInt(),
      useAmount: (json['useAmount'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$ContractActivityDataImplToJson(
        _$ContractActivityDataImpl instance) =>
    <String, dynamic>{
      'activityRiskMap': instance.activityRiskMap,
      'currency': instance.currency,
      'interestCash': instance.interestCash,
      'useAmount': instance.useAmount,
    };

_$ActivityRiskMapImpl _$$ActivityRiskMapImplFromJson(
        Map<String, dynamic> json) =>
    _$ActivityRiskMapImpl(
      activityId: (json['activityId'] as num?)?.toInt(),
      activityRule: json['activityRule'] as String? ?? '',
      applyAmountList: (json['applyAmountList'] as List<dynamic>?)
          ?.map((e) => (e as num).toDouble())
          .toList(),
      closeLossRadio: (json['closeLossRadio'] as num?)?.toDouble(),
      giveDay: (json['giveDay'] as num?)?.toInt(),
      giveRatio: (json['giveRatio'] as num?)?.toDouble(),
      interestRate: (json['interestRate'] as num?)?.toDouble(),
      marketType: json['marketType'] as String?,
      multiple: (json['multiple'] as num?)?.toInt(),
      riskId: (json['riskId'] as num?)?.toInt(),
      shareRatio: (json['shareRatio'] as num?)?.toDouble(),
      warnLossRadio: (json['warnLossRadio'] as num?)?.toDouble(),
      giveAmount: (json['giveAmount'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$ActivityRiskMapImplToJson(
        _$ActivityRiskMapImpl instance) =>
    <String, dynamic>{
      'activityId': instance.activityId,
      'activityRule': instance.activityRule,
      'applyAmountList': instance.applyAmountList,
      'closeLossRadio': instance.closeLossRadio,
      'giveDay': instance.giveDay,
      'giveRatio': instance.giveRatio,
      'interestRate': instance.interestRate,
      'marketType': instance.marketType,
      'multiple': instance.multiple,
      'riskId': instance.riskId,
      'shareRatio': instance.shareRatio,
      'warnLossRadio': instance.warnLossRadio,
      'giveAmount': instance.giveAmount,
    };
