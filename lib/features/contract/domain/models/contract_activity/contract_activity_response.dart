// To parse this JSON data, do
//
//     final contractActivityResponse = contractActivityResponseFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'contract_activity_response.freezed.dart';
part 'contract_activity_response.g.dart';

ContractActivityResponse contractActivityResponseFromJson(str) => ContractActivityResponse.fromJson((str));

String contractActivityResponseToJson(ContractActivityResponse data) => json.encode(data.toJson());

@freezed
class ContractActivityResponse with _$ContractActivityResponse {
  const factory ContractActivityResponse({
    int? code,
    ContractActivityData? data,
    String? msg,
  }) = _ContractActivityResponse;

  factory ContractActivityResponse.fromJson(Map<String, dynamic> json) => _$ContractActivityResponseFromJson(json);
}

@freezed
class ContractActivityData with _$ContractActivityData {
  const factory ContractActivityData({
    List<ActivityRiskMap>? activityRiskMap,
    String? currency,
    int? interestCash,
    double? useAmount,
  }) = _ContractActivityData;

  factory ContractActivityData.fromJson(Map<String, dynamic> json) => _$ContractActivityDataFromJson(json);
}

@freezed
class ActivityRiskMap with _$ActivityRiskMap {
  const factory ActivityRiskMap({
    int? activityId,
    @Default('') String activityRule,
    List<double>? applyAmountList,
    double? closeLossRadio,
    int? giveDay,
    double? giveRatio,
    double? interestRate,
    String? marketType,
    int? multiple,
    int? riskId,
    double? shareRatio,
    double? warnLossRadio,
    double? giveAmount,
  }) = _ActivityRiskMap;

  factory ActivityRiskMap.fromJson(Map<String, dynamic> json) => _$ActivityRiskMapFromJson(json);
}
