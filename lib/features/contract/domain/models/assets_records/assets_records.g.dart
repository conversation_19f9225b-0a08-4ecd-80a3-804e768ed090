// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'assets_records.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AssetsRecordsImpl _$$AssetsRecordsImplFromJson(Map<String, dynamic> json) =>
    _$AssetsRecordsImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : AssetsRecordsData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$AssetsRecordsImplToJson(_$AssetsRecordsImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$AssetsRecordsDataImpl _$$AssetsRecordsDataImplFromJson(
        Map<String, dynamic> json) =>
    _$AssetsRecordsDataImpl(
      current: (json['current'] as num?)?.toInt(),
      hasNext: json['hasNext'] as bool?,
      records: (json['records'] as List<dynamic>?)
          ?.map((e) => RecordData.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$AssetsRecordsDataImplToJson(
        _$AssetsRecordsDataImpl instance) =>
    <String, dynamic>{
      'current': instance.current,
      'hasNext': instance.hasNext,
      'records': instance.records,
      'total': instance.total,
    };

_$RecordDataImpl _$$RecordDataImplFromJson(Map<String, dynamic> json) =>
    _$RecordDataImpl(
      afterNum: (json['afterNum'] as num?)?.toDouble(),
      beforeNum: (json['beforeNum'] as num?)?.toDouble(),
      createTime: json['createTime'] as String?,
      currency: json['currency'] as String?,
      fromType: (json['fromType'] as num?)?.toInt(),
      id: (json['id'] as num?)?.toInt(),
      mobile: json['mobile'] as String?,
      realName: json['realName'] as String?,
      type: (json['type'] as num?)?.toInt(),
      updateNum: (json['updateNum'] as num?)?.toDouble(),
      userId: (json['userId'] as num?)?.toInt(),
      serialNo: json['serialNo'] as String?,
    );

Map<String, dynamic> _$$RecordDataImplToJson(_$RecordDataImpl instance) =>
    <String, dynamic>{
      'afterNum': instance.afterNum,
      'beforeNum': instance.beforeNum,
      'createTime': instance.createTime,
      'currency': instance.currency,
      'fromType': instance.fromType,
      'id': instance.id,
      'mobile': instance.mobile,
      'realName': instance.realName,
      'type': instance.type,
      'updateNum': instance.updateNum,
      'userId': instance.userId,
      'serialNo': instance.serialNo,
    };
