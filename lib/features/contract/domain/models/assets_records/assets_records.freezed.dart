// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'assets_records.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AssetsRecords _$AssetsRecordsFromJson(Map<String, dynamic> json) {
  return _AssetsRecords.fromJson(json);
}

/// @nodoc
mixin _$AssetsRecords {
  int? get code => throw _privateConstructorUsedError;
  AssetsRecordsData? get data => throw _privateConstructorUsedError;
  String? get msg => throw _privateConstructorUsedError;

  /// Serializes this AssetsRecords to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AssetsRecords
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AssetsRecordsCopyWith<AssetsRecords> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AssetsRecordsCopyWith<$Res> {
  factory $AssetsRecordsCopyWith(
          AssetsRecords value, $Res Function(AssetsRecords) then) =
      _$AssetsRecordsCopyWithImpl<$Res, AssetsRecords>;
  @useResult
  $Res call({int? code, AssetsRecordsData? data, String? msg});

  $AssetsRecordsDataCopyWith<$Res>? get data;
}

/// @nodoc
class _$AssetsRecordsCopyWithImpl<$Res, $Val extends AssetsRecords>
    implements $AssetsRecordsCopyWith<$Res> {
  _$AssetsRecordsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AssetsRecords
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_value.copyWith(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as AssetsRecordsData?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of AssetsRecords
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AssetsRecordsDataCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $AssetsRecordsDataCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AssetsRecordsImplCopyWith<$Res>
    implements $AssetsRecordsCopyWith<$Res> {
  factory _$$AssetsRecordsImplCopyWith(
          _$AssetsRecordsImpl value, $Res Function(_$AssetsRecordsImpl) then) =
      __$$AssetsRecordsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? code, AssetsRecordsData? data, String? msg});

  @override
  $AssetsRecordsDataCopyWith<$Res>? get data;
}

/// @nodoc
class __$$AssetsRecordsImplCopyWithImpl<$Res>
    extends _$AssetsRecordsCopyWithImpl<$Res, _$AssetsRecordsImpl>
    implements _$$AssetsRecordsImplCopyWith<$Res> {
  __$$AssetsRecordsImplCopyWithImpl(
      _$AssetsRecordsImpl _value, $Res Function(_$AssetsRecordsImpl) _then)
      : super(_value, _then);

  /// Create a copy of AssetsRecords
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_$AssetsRecordsImpl(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as AssetsRecordsData?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AssetsRecordsImpl implements _AssetsRecords {
  const _$AssetsRecordsImpl({this.code, this.data, this.msg});

  factory _$AssetsRecordsImpl.fromJson(Map<String, dynamic> json) =>
      _$$AssetsRecordsImplFromJson(json);

  @override
  final int? code;
  @override
  final AssetsRecordsData? data;
  @override
  final String? msg;

  @override
  String toString() {
    return 'AssetsRecords(code: $code, data: $data, msg: $msg)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AssetsRecordsImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.msg, msg) || other.msg == msg));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, data, msg);

  /// Create a copy of AssetsRecords
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AssetsRecordsImplCopyWith<_$AssetsRecordsImpl> get copyWith =>
      __$$AssetsRecordsImplCopyWithImpl<_$AssetsRecordsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AssetsRecordsImplToJson(
      this,
    );
  }
}

abstract class _AssetsRecords implements AssetsRecords {
  const factory _AssetsRecords(
      {final int? code,
      final AssetsRecordsData? data,
      final String? msg}) = _$AssetsRecordsImpl;

  factory _AssetsRecords.fromJson(Map<String, dynamic> json) =
      _$AssetsRecordsImpl.fromJson;

  @override
  int? get code;
  @override
  AssetsRecordsData? get data;
  @override
  String? get msg;

  /// Create a copy of AssetsRecords
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AssetsRecordsImplCopyWith<_$AssetsRecordsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AssetsRecordsData _$AssetsRecordsDataFromJson(Map<String, dynamic> json) {
  return _AssetsRecordsData.fromJson(json);
}

/// @nodoc
mixin _$AssetsRecordsData {
  int? get current => throw _privateConstructorUsedError;
  bool? get hasNext => throw _privateConstructorUsedError;
  List<RecordData>? get records => throw _privateConstructorUsedError;
  int? get total => throw _privateConstructorUsedError;

  /// Serializes this AssetsRecordsData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AssetsRecordsData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AssetsRecordsDataCopyWith<AssetsRecordsData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AssetsRecordsDataCopyWith<$Res> {
  factory $AssetsRecordsDataCopyWith(
          AssetsRecordsData value, $Res Function(AssetsRecordsData) then) =
      _$AssetsRecordsDataCopyWithImpl<$Res, AssetsRecordsData>;
  @useResult
  $Res call(
      {int? current, bool? hasNext, List<RecordData>? records, int? total});
}

/// @nodoc
class _$AssetsRecordsDataCopyWithImpl<$Res, $Val extends AssetsRecordsData>
    implements $AssetsRecordsDataCopyWith<$Res> {
  _$AssetsRecordsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AssetsRecordsData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = freezed,
    Object? hasNext = freezed,
    Object? records = freezed,
    Object? total = freezed,
  }) {
    return _then(_value.copyWith(
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      hasNext: freezed == hasNext
          ? _value.hasNext
          : hasNext // ignore: cast_nullable_to_non_nullable
              as bool?,
      records: freezed == records
          ? _value.records
          : records // ignore: cast_nullable_to_non_nullable
              as List<RecordData>?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AssetsRecordsDataImplCopyWith<$Res>
    implements $AssetsRecordsDataCopyWith<$Res> {
  factory _$$AssetsRecordsDataImplCopyWith(_$AssetsRecordsDataImpl value,
          $Res Function(_$AssetsRecordsDataImpl) then) =
      __$$AssetsRecordsDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? current, bool? hasNext, List<RecordData>? records, int? total});
}

/// @nodoc
class __$$AssetsRecordsDataImplCopyWithImpl<$Res>
    extends _$AssetsRecordsDataCopyWithImpl<$Res, _$AssetsRecordsDataImpl>
    implements _$$AssetsRecordsDataImplCopyWith<$Res> {
  __$$AssetsRecordsDataImplCopyWithImpl(_$AssetsRecordsDataImpl _value,
      $Res Function(_$AssetsRecordsDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of AssetsRecordsData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = freezed,
    Object? hasNext = freezed,
    Object? records = freezed,
    Object? total = freezed,
  }) {
    return _then(_$AssetsRecordsDataImpl(
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      hasNext: freezed == hasNext
          ? _value.hasNext
          : hasNext // ignore: cast_nullable_to_non_nullable
              as bool?,
      records: freezed == records
          ? _value._records
          : records // ignore: cast_nullable_to_non_nullable
              as List<RecordData>?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AssetsRecordsDataImpl implements _AssetsRecordsData {
  const _$AssetsRecordsDataImpl(
      {this.current, this.hasNext, final List<RecordData>? records, this.total})
      : _records = records;

  factory _$AssetsRecordsDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$AssetsRecordsDataImplFromJson(json);

  @override
  final int? current;
  @override
  final bool? hasNext;
  final List<RecordData>? _records;
  @override
  List<RecordData>? get records {
    final value = _records;
    if (value == null) return null;
    if (_records is EqualUnmodifiableListView) return _records;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? total;

  @override
  String toString() {
    return 'AssetsRecordsData(current: $current, hasNext: $hasNext, records: $records, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AssetsRecordsDataImpl &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.hasNext, hasNext) || other.hasNext == hasNext) &&
            const DeepCollectionEquality().equals(other._records, _records) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, current, hasNext,
      const DeepCollectionEquality().hash(_records), total);

  /// Create a copy of AssetsRecordsData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AssetsRecordsDataImplCopyWith<_$AssetsRecordsDataImpl> get copyWith =>
      __$$AssetsRecordsDataImplCopyWithImpl<_$AssetsRecordsDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AssetsRecordsDataImplToJson(
      this,
    );
  }
}

abstract class _AssetsRecordsData implements AssetsRecordsData {
  const factory _AssetsRecordsData(
      {final int? current,
      final bool? hasNext,
      final List<RecordData>? records,
      final int? total}) = _$AssetsRecordsDataImpl;

  factory _AssetsRecordsData.fromJson(Map<String, dynamic> json) =
      _$AssetsRecordsDataImpl.fromJson;

  @override
  int? get current;
  @override
  bool? get hasNext;
  @override
  List<RecordData>? get records;
  @override
  int? get total;

  /// Create a copy of AssetsRecordsData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AssetsRecordsDataImplCopyWith<_$AssetsRecordsDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RecordData _$RecordDataFromJson(Map<String, dynamic> json) {
  return _RecordData.fromJson(json);
}

/// @nodoc
mixin _$RecordData {
  double? get afterNum => throw _privateConstructorUsedError;
  double? get beforeNum => throw _privateConstructorUsedError;
  String? get createTime => throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;
  int? get fromType => throw _privateConstructorUsedError;
  int? get id => throw _privateConstructorUsedError;
  String? get mobile => throw _privateConstructorUsedError;
  String? get realName => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;
  double? get updateNum => throw _privateConstructorUsedError;
  int? get userId => throw _privateConstructorUsedError;
  String? get serialNo => throw _privateConstructorUsedError;

  /// Serializes this RecordData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RecordData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RecordDataCopyWith<RecordData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecordDataCopyWith<$Res> {
  factory $RecordDataCopyWith(
          RecordData value, $Res Function(RecordData) then) =
      _$RecordDataCopyWithImpl<$Res, RecordData>;
  @useResult
  $Res call(
      {double? afterNum,
      double? beforeNum,
      String? createTime,
      String? currency,
      int? fromType,
      int? id,
      String? mobile,
      String? realName,
      int? type,
      double? updateNum,
      int? userId,
      String? serialNo});
}

/// @nodoc
class _$RecordDataCopyWithImpl<$Res, $Val extends RecordData>
    implements $RecordDataCopyWith<$Res> {
  _$RecordDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RecordData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? afterNum = freezed,
    Object? beforeNum = freezed,
    Object? createTime = freezed,
    Object? currency = freezed,
    Object? fromType = freezed,
    Object? id = freezed,
    Object? mobile = freezed,
    Object? realName = freezed,
    Object? type = freezed,
    Object? updateNum = freezed,
    Object? userId = freezed,
    Object? serialNo = freezed,
  }) {
    return _then(_value.copyWith(
      afterNum: freezed == afterNum
          ? _value.afterNum
          : afterNum // ignore: cast_nullable_to_non_nullable
              as double?,
      beforeNum: freezed == beforeNum
          ? _value.beforeNum
          : beforeNum // ignore: cast_nullable_to_non_nullable
              as double?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      fromType: freezed == fromType
          ? _value.fromType
          : fromType // ignore: cast_nullable_to_non_nullable
              as int?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
      realName: freezed == realName
          ? _value.realName
          : realName // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      updateNum: freezed == updateNum
          ? _value.updateNum
          : updateNum // ignore: cast_nullable_to_non_nullable
              as double?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      serialNo: freezed == serialNo
          ? _value.serialNo
          : serialNo // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RecordDataImplCopyWith<$Res>
    implements $RecordDataCopyWith<$Res> {
  factory _$$RecordDataImplCopyWith(
          _$RecordDataImpl value, $Res Function(_$RecordDataImpl) then) =
      __$$RecordDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double? afterNum,
      double? beforeNum,
      String? createTime,
      String? currency,
      int? fromType,
      int? id,
      String? mobile,
      String? realName,
      int? type,
      double? updateNum,
      int? userId,
      String? serialNo});
}

/// @nodoc
class __$$RecordDataImplCopyWithImpl<$Res>
    extends _$RecordDataCopyWithImpl<$Res, _$RecordDataImpl>
    implements _$$RecordDataImplCopyWith<$Res> {
  __$$RecordDataImplCopyWithImpl(
      _$RecordDataImpl _value, $Res Function(_$RecordDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of RecordData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? afterNum = freezed,
    Object? beforeNum = freezed,
    Object? createTime = freezed,
    Object? currency = freezed,
    Object? fromType = freezed,
    Object? id = freezed,
    Object? mobile = freezed,
    Object? realName = freezed,
    Object? type = freezed,
    Object? updateNum = freezed,
    Object? userId = freezed,
    Object? serialNo = freezed,
  }) {
    return _then(_$RecordDataImpl(
      afterNum: freezed == afterNum
          ? _value.afterNum
          : afterNum // ignore: cast_nullable_to_non_nullable
              as double?,
      beforeNum: freezed == beforeNum
          ? _value.beforeNum
          : beforeNum // ignore: cast_nullable_to_non_nullable
              as double?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      fromType: freezed == fromType
          ? _value.fromType
          : fromType // ignore: cast_nullable_to_non_nullable
              as int?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
      realName: freezed == realName
          ? _value.realName
          : realName // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      updateNum: freezed == updateNum
          ? _value.updateNum
          : updateNum // ignore: cast_nullable_to_non_nullable
              as double?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      serialNo: freezed == serialNo
          ? _value.serialNo
          : serialNo // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RecordDataImpl implements _RecordData {
  const _$RecordDataImpl(
      {this.afterNum,
      this.beforeNum,
      this.createTime,
      this.currency,
      this.fromType,
      this.id,
      this.mobile,
      this.realName,
      this.type,
      this.updateNum,
      this.userId,
      this.serialNo});

  factory _$RecordDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$RecordDataImplFromJson(json);

  @override
  final double? afterNum;
  @override
  final double? beforeNum;
  @override
  final String? createTime;
  @override
  final String? currency;
  @override
  final int? fromType;
  @override
  final int? id;
  @override
  final String? mobile;
  @override
  final String? realName;
  @override
  final int? type;
  @override
  final double? updateNum;
  @override
  final int? userId;
  @override
  final String? serialNo;

  @override
  String toString() {
    return 'RecordData(afterNum: $afterNum, beforeNum: $beforeNum, createTime: $createTime, currency: $currency, fromType: $fromType, id: $id, mobile: $mobile, realName: $realName, type: $type, updateNum: $updateNum, userId: $userId, serialNo: $serialNo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecordDataImpl &&
            (identical(other.afterNum, afterNum) ||
                other.afterNum == afterNum) &&
            (identical(other.beforeNum, beforeNum) ||
                other.beforeNum == beforeNum) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.fromType, fromType) ||
                other.fromType == fromType) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            (identical(other.realName, realName) ||
                other.realName == realName) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.updateNum, updateNum) ||
                other.updateNum == updateNum) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.serialNo, serialNo) ||
                other.serialNo == serialNo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      afterNum,
      beforeNum,
      createTime,
      currency,
      fromType,
      id,
      mobile,
      realName,
      type,
      updateNum,
      userId,
      serialNo);

  /// Create a copy of RecordData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RecordDataImplCopyWith<_$RecordDataImpl> get copyWith =>
      __$$RecordDataImplCopyWithImpl<_$RecordDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RecordDataImplToJson(
      this,
    );
  }
}

abstract class _RecordData implements RecordData {
  const factory _RecordData(
      {final double? afterNum,
      final double? beforeNum,
      final String? createTime,
      final String? currency,
      final int? fromType,
      final int? id,
      final String? mobile,
      final String? realName,
      final int? type,
      final double? updateNum,
      final int? userId,
      final String? serialNo}) = _$RecordDataImpl;

  factory _RecordData.fromJson(Map<String, dynamic> json) =
      _$RecordDataImpl.fromJson;

  @override
  double? get afterNum;
  @override
  double? get beforeNum;
  @override
  String? get createTime;
  @override
  String? get currency;
  @override
  int? get fromType;
  @override
  int? get id;
  @override
  String? get mobile;
  @override
  String? get realName;
  @override
  int? get type;
  @override
  double? get updateNum;
  @override
  int? get userId;
  @override
  String? get serialNo;

  /// Create a copy of RecordData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RecordDataImplCopyWith<_$RecordDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
