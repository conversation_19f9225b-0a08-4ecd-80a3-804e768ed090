import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/interest_records_model.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/interest_records_model.g.dart';

@JsonSerializable()
class InterestRecordsModel {
  int current = 0;
  bool hasNext = false;
  List<InterestRecordModel> records = [];
  int total = 0;

  InterestRecordsModel();

  factory InterestRecordsModel.fromJson(Map<String, dynamic> json) => $InterestRecordsModelFromJson(json);

  Map<String, dynamic> toJson() => $InterestRecordsModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class InterestRecordModel {
  /// 调整后数量
  double afterNum = 0.0;

  /// 调整前数量
  double beforeNum = 0.0;

  /// 现货账户ID
  int commonAccountId = 0;

  /// 合约账户ID（只有消费的时候会关联）
  int contractAccountId = 0;

  /// 合约申请ID（只有合约申请才会存入）
  int contractApplyId = 0;

  /// 创建时间
  String createTime = '';

  /// 币种
  String currency = '';

  /// 拓展数据
  String extra = '';

  /// 来源
  /// 1. 后台添加
  /// 2. 签到赠送
  /// 3. 活动赠送
  /// 4. 申请合约账户
  /// 5. 利息扣除
  /// 6. 充值赠送
  /// 7. 后台减少
  int fromType = 0;

  /// 唯一标识
  int id = 0;

  /// 调整数额
  double updateNum = 0.0;

  /// 用户ID
  int userId = 0;

  InterestRecordModel();

  factory InterestRecordModel.fromJson(Map<String, dynamic> json) => $InterestRecordModelFromJson(json);

  Map<String, dynamic> toJson() => $InterestRecordModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
