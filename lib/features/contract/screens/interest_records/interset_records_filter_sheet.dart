import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/app_dropdown.dart';

class IntersetRecordsFilterSheet extends StatefulWidget {
  final int? selectedFromType;
  final Function(int?) onTapFromType;
  const IntersetRecordsFilterSheet({super.key, this.selectedFromType, required this.onTapFromType});

  @override
  State<IntersetRecordsFilterSheet> createState() => _IntersetRecordsFilterSheetState();
}

class _IntersetRecordsFilterSheetState extends State<IntersetRecordsFilterSheet> {
  late int? tempSelectedIdx;

  @override
  void initState() {
    super.initState();
    tempSelectedIdx = widget.selectedFromType;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16),
      color: context.theme.cardColor,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 10,
        children: [
          const SizedBox(height: 10, width: double.infinity),
          Text(
            "fundType".tr(),
            style: context.textTheme.regular.fs13,
          ),
          AppDropdown<int?>(
            selected: tempSelectedIdx,
            hintText: 'selectFundType'.tr(),
            items: [1, 2, 3, 4, 5, 6, 7]
                .map(
                  (e) => DropdownMenuItem(
                    value: e,
                    child: Text('interest_type_$e'.tr()),
                  ),
                )
                .toList(),
            onChanged: (idx) {
              setState(() {
                tempSelectedIdx = idx;
              });
            },
          ),
          Row(
            children: [
              Expanded(
                child: CustomMaterialButton(
                  onPressed: () {
                    setState(() {
                      tempSelectedIdx = null;
                    });
                  },
                  isOutLined: true,
                  buttonText: 'reset'.tr(),
                ),
              ),
              SizedBox(width: 10.gw),
              Expanded(
                child: CustomMaterialButton(
                  onPressed: () {
                    widget.onTapFromType(tempSelectedIdx);
                    Navigator.pop(context);
                  },
                  buttonText: 'submit'.tr(),
                ),
              )
            ],
          ),
          const SizedBox(height: 30, width: double.infinity),
        ],
      ),
    );
  }
}
