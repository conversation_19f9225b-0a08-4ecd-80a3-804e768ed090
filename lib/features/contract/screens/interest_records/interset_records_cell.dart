import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/contract/domain/models/interest_records/interest_records_model.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

import 'package:gp_stock_app/shared/widgets/flip_text.dart';

class IntersetRecordsCell extends StatelessWidget {
  final InterestRecordModel record;
  const IntersetRecordsCell({super.key, required this.record});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.gh),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withNewOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16.gr),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'interest_type_${record.fromType}'.tr(),
                  style: context.textTheme.primary.w600,
                ),
                Text(
                  record.createTime,
                  style: context.textTheme.regular.fs9,
                ),
              ],
            ),
            12.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('beforeChangeAmount'.tr(), style: context.textTheme.regular),
                FlipText(
                  record.beforeNum,
                  style: context.textTheme.regular,
                  suffix: ' ${record.currency}',
                ),
              ],
            ),
            8.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('changeAmount'.tr(), style: context.textTheme.regular),
                FlipText(
                  record.updateNum,
                  style: context.textTheme.primary.w600.copyWith(color: (record.updateNum).getValueColor(context)),
                  suffix: ' ${record.currency}',
                  prefix: (record.updateNum) < 0 ? '' : '+',
                ),
              ],
            ),
            8.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('afterChangeAmount'.tr(), style: context.textTheme.regular),
                FlipText(
                  record.afterNum,
                  style: context.textTheme.primary.w600.copyWith(
                    color: context.theme.primaryColor,
                  ),
                  suffix: record.currency,
                ),
              ],
            ),
            // 8.verticalSpace,
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            //     Text('serialNumber'.tr(), style: context.textTheme.regular),
            //     Text(
            //       record.id.toString(),
            //       style: context.textTheme.primary.w600.copyWith(
            //         color: context.appTheme.redColor,
            //         fontFamily: 'Akzidenz-Grotesk',
            //       ),
            //     ),
            //   ],
            // ),
          ],
        ),
      ),
    );
  }
}
