// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'warn_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WarnResponse _$WarnResponseFromJson(Map<String, dynamic> json) {
  return _WarnResponse.fromJson(json);
}

/// @nodoc
mixin _$WarnResponse {
  String get createTime => throw _privateConstructorUsedError;
  int get id => throw _privateConstructorUsedError;
  String get market => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get securityType => throw _privateConstructorUsedError;
  String get symbol => throw _privateConstructorUsedError;
  double get targetDownGain => throw _privateConstructorUsedError;
  double get targetDownPrice => throw _privateConstructorUsedError;
  double get targetUpGain => throw _privateConstructorUsedError;
  double get targetUpPrice => throw _privateConstructorUsedError;
  String get updateTime => throw _privateConstructorUsedError;
  int get userId => throw _privateConstructorUsedError;

  /// Serializes this WarnResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WarnResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WarnResponseCopyWith<WarnResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WarnResponseCopyWith<$Res> {
  factory $WarnResponseCopyWith(
          WarnResponse value, $Res Function(WarnResponse) then) =
      _$WarnResponseCopyWithImpl<$Res, WarnResponse>;
  @useResult
  $Res call(
      {String createTime,
      int id,
      String market,
      String name,
      String securityType,
      String symbol,
      double targetDownGain,
      double targetDownPrice,
      double targetUpGain,
      double targetUpPrice,
      String updateTime,
      int userId});
}

/// @nodoc
class _$WarnResponseCopyWithImpl<$Res, $Val extends WarnResponse>
    implements $WarnResponseCopyWith<$Res> {
  _$WarnResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WarnResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createTime = null,
    Object? id = null,
    Object? market = null,
    Object? name = null,
    Object? securityType = null,
    Object? symbol = null,
    Object? targetDownGain = null,
    Object? targetDownPrice = null,
    Object? targetUpGain = null,
    Object? targetUpPrice = null,
    Object? updateTime = null,
    Object? userId = null,
  }) {
    return _then(_value.copyWith(
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      market: null == market
          ? _value.market
          : market // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      securityType: null == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as String,
      symbol: null == symbol
          ? _value.symbol
          : symbol // ignore: cast_nullable_to_non_nullable
              as String,
      targetDownGain: null == targetDownGain
          ? _value.targetDownGain
          : targetDownGain // ignore: cast_nullable_to_non_nullable
              as double,
      targetDownPrice: null == targetDownPrice
          ? _value.targetDownPrice
          : targetDownPrice // ignore: cast_nullable_to_non_nullable
              as double,
      targetUpGain: null == targetUpGain
          ? _value.targetUpGain
          : targetUpGain // ignore: cast_nullable_to_non_nullable
              as double,
      targetUpPrice: null == targetUpPrice
          ? _value.targetUpPrice
          : targetUpPrice // ignore: cast_nullable_to_non_nullable
              as double,
      updateTime: null == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WarnResponseImplCopyWith<$Res>
    implements $WarnResponseCopyWith<$Res> {
  factory _$$WarnResponseImplCopyWith(
          _$WarnResponseImpl value, $Res Function(_$WarnResponseImpl) then) =
      __$$WarnResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String createTime,
      int id,
      String market,
      String name,
      String securityType,
      String symbol,
      double targetDownGain,
      double targetDownPrice,
      double targetUpGain,
      double targetUpPrice,
      String updateTime,
      int userId});
}

/// @nodoc
class __$$WarnResponseImplCopyWithImpl<$Res>
    extends _$WarnResponseCopyWithImpl<$Res, _$WarnResponseImpl>
    implements _$$WarnResponseImplCopyWith<$Res> {
  __$$WarnResponseImplCopyWithImpl(
      _$WarnResponseImpl _value, $Res Function(_$WarnResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of WarnResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createTime = null,
    Object? id = null,
    Object? market = null,
    Object? name = null,
    Object? securityType = null,
    Object? symbol = null,
    Object? targetDownGain = null,
    Object? targetDownPrice = null,
    Object? targetUpGain = null,
    Object? targetUpPrice = null,
    Object? updateTime = null,
    Object? userId = null,
  }) {
    return _then(_$WarnResponseImpl(
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      market: null == market
          ? _value.market
          : market // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      securityType: null == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as String,
      symbol: null == symbol
          ? _value.symbol
          : symbol // ignore: cast_nullable_to_non_nullable
              as String,
      targetDownGain: null == targetDownGain
          ? _value.targetDownGain
          : targetDownGain // ignore: cast_nullable_to_non_nullable
              as double,
      targetDownPrice: null == targetDownPrice
          ? _value.targetDownPrice
          : targetDownPrice // ignore: cast_nullable_to_non_nullable
              as double,
      targetUpGain: null == targetUpGain
          ? _value.targetUpGain
          : targetUpGain // ignore: cast_nullable_to_non_nullable
              as double,
      targetUpPrice: null == targetUpPrice
          ? _value.targetUpPrice
          : targetUpPrice // ignore: cast_nullable_to_non_nullable
              as double,
      updateTime: null == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WarnResponseImpl implements _WarnResponse {
  const _$WarnResponseImpl(
      {this.createTime = '',
      this.id = 0,
      this.market = '',
      this.name = '',
      this.securityType = '',
      this.symbol = '',
      this.targetDownGain = 0,
      this.targetDownPrice = 0,
      this.targetUpGain = 0,
      this.targetUpPrice = 0,
      this.updateTime = '',
      this.userId = 0});

  factory _$WarnResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$WarnResponseImplFromJson(json);

  @override
  @JsonKey()
  final String createTime;
  @override
  @JsonKey()
  final int id;
  @override
  @JsonKey()
  final String market;
  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final String securityType;
  @override
  @JsonKey()
  final String symbol;
  @override
  @JsonKey()
  final double targetDownGain;
  @override
  @JsonKey()
  final double targetDownPrice;
  @override
  @JsonKey()
  final double targetUpGain;
  @override
  @JsonKey()
  final double targetUpPrice;
  @override
  @JsonKey()
  final String updateTime;
  @override
  @JsonKey()
  final int userId;

  @override
  String toString() {
    return 'WarnResponse(createTime: $createTime, id: $id, market: $market, name: $name, securityType: $securityType, symbol: $symbol, targetDownGain: $targetDownGain, targetDownPrice: $targetDownPrice, targetUpGain: $targetUpGain, targetUpPrice: $targetUpPrice, updateTime: $updateTime, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WarnResponseImpl &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.market, market) || other.market == market) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.securityType, securityType) ||
                other.securityType == securityType) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.targetDownGain, targetDownGain) ||
                other.targetDownGain == targetDownGain) &&
            (identical(other.targetDownPrice, targetDownPrice) ||
                other.targetDownPrice == targetDownPrice) &&
            (identical(other.targetUpGain, targetUpGain) ||
                other.targetUpGain == targetUpGain) &&
            (identical(other.targetUpPrice, targetUpPrice) ||
                other.targetUpPrice == targetUpPrice) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      createTime,
      id,
      market,
      name,
      securityType,
      symbol,
      targetDownGain,
      targetDownPrice,
      targetUpGain,
      targetUpPrice,
      updateTime,
      userId);

  /// Create a copy of WarnResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WarnResponseImplCopyWith<_$WarnResponseImpl> get copyWith =>
      __$$WarnResponseImplCopyWithImpl<_$WarnResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WarnResponseImplToJson(
      this,
    );
  }
}

abstract class _WarnResponse implements WarnResponse {
  const factory _WarnResponse(
      {final String createTime,
      final int id,
      final String market,
      final String name,
      final String securityType,
      final String symbol,
      final double targetDownGain,
      final double targetDownPrice,
      final double targetUpGain,
      final double targetUpPrice,
      final String updateTime,
      final int userId}) = _$WarnResponseImpl;

  factory _WarnResponse.fromJson(Map<String, dynamic> json) =
      _$WarnResponseImpl.fromJson;

  @override
  String get createTime;
  @override
  int get id;
  @override
  String get market;
  @override
  String get name;
  @override
  String get securityType;
  @override
  String get symbol;
  @override
  double get targetDownGain;
  @override
  double get targetDownPrice;
  @override
  double get targetUpGain;
  @override
  double get targetUpPrice;
  @override
  String get updateTime;
  @override
  int get userId;

  /// Create a copy of WarnResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WarnResponseImplCopyWith<_$WarnResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
