// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index_page_response_order.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$IndexPageOrderResponseImpl _$$IndexPageOrderResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$IndexPageOrderResponseImpl(
      current: (json['current'] as num?)?.toInt() ?? 0,
      hasNext: json['hasNext'] as bool? ?? false,
      records: (json['records'] as List<dynamic>?)
              ?.map((e) => IndexOrderRecord.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      total: (json['total'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$IndexPageOrderResponseImplToJson(
        _$IndexPageOrderResponseImpl instance) =>
    <String, dynamic>{
      'current': instance.current,
      'hasNext': instance.hasNext,
      'records': instance.records,
      'total': instance.total,
    };

_$IndexOrderRecordImpl _$$IndexOrderRecordImplFromJson(
        Map<String, dynamic> json) =>
    _$IndexOrderRecordImpl(
      createTime: json['createTime'] as String? ?? '',
      currency: json['currency'] as String? ?? '',
      dealFee: (json['dealFee'] as num?)?.toDouble() ?? 0.0,
      dealNum: (json['dealNum'] as num?)?.toDouble() ?? 0.0,
      dealPrice: (json['dealPrice'] as num?)?.toDouble() ?? 0.0,
      dealTime: json['dealTime'] as String? ?? '',
      direction: (json['direction'] as num?)?.toInt() ?? 0,
      id: (json['id'] as num?)?.toInt() ?? 0,
      market: json['market'] as String? ?? '',
      securityType: json['securityType'] as String? ?? '',
      symbol: json['symbol'] as String? ?? '',
      symbolName: json['symbolName'] as String? ?? '',
      tradeType: (json['tradeType'] as num?)?.toInt() ?? 0,
      tradeUnit: (json['tradeUnit'] as num?)?.toDouble() ?? 0.0,
      type: (json['type'] as num?)?.toInt() ?? 0,
      updateTime: json['updateTime'] as String? ?? '',
      winAmount: (json['winAmount'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$IndexOrderRecordImplToJson(
        _$IndexOrderRecordImpl instance) =>
    <String, dynamic>{
      'createTime': instance.createTime,
      'currency': instance.currency,
      'dealFee': instance.dealFee,
      'dealNum': instance.dealNum,
      'dealPrice': instance.dealPrice,
      'dealTime': instance.dealTime,
      'direction': instance.direction,
      'id': instance.id,
      'market': instance.market,
      'securityType': instance.securityType,
      'symbol': instance.symbol,
      'symbolName': instance.symbolName,
      'tradeType': instance.tradeType,
      'tradeUnit': instance.tradeUnit,
      'type': instance.type,
      'updateTime': instance.updateTime,
      'winAmount': instance.winAmount,
    };
