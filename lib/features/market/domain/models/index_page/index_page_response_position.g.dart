// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index_page_response_position.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$IndexPagePositionResponseImpl _$$IndexPagePositionResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$IndexPagePositionResponseImpl(
      current: (json['current'] as num?)?.toInt() ?? 0,
      hasNext: json['hasNext'] as bool? ?? false,
      records: (json['records'] as List<dynamic>?)
              ?.map((e) => IndexRecord.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      total: (json['total'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$IndexPagePositionResponseImplToJson(
        _$IndexPagePositionResponseImpl instance) =>
    <String, dynamic>{
      'current': instance.current,
      'hasNext': instance.hasNext,
      'records': instance.records,
      'total': instance.total,
    };

_$IndexRecordImpl _$$IndexRecordImplFromJson(Map<String, dynamic> json) =>
    _$IndexRecordImpl(
      buyAvgPrice: (json['buyAvgPrice'] as num?)?.toDouble() ?? 0.0,
      buyTotalNum: (json['buyTotalNum'] as num?)?.toDouble() ?? 0.0,
      createTime: json['createTime'] as String? ?? '',
      currency: json['currency'] as String? ?? '',
      direction: (json['direction'] as num?)?.toInt() ?? 0,
      id: (json['id'] as num?)?.toInt() ?? 0,
      market: json['market'] as String? ?? '',
      securityType: json['securityType'] as String? ?? '',
      sellAvgPrice: (json['sellAvgPrice'] as num?)?.toDouble() ?? 0.0,
      symbol: json['symbol'] as String? ?? '',
      symbolName: json['symbolName'] as String? ?? '',
      tradeType: (json['tradeType'] as num?)?.toInt() ?? 0,
      tradeUnit: (json['tradeUnit'] as num?)?.toInt() ?? 0,
      type: (json['type'] as num?)?.toInt() ?? 0,
      updateTime: json['updateTime'] as String? ?? '',
      winAmount: (json['winAmount'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$IndexRecordImplToJson(_$IndexRecordImpl instance) =>
    <String, dynamic>{
      'buyAvgPrice': instance.buyAvgPrice,
      'buyTotalNum': instance.buyTotalNum,
      'createTime': instance.createTime,
      'currency': instance.currency,
      'direction': instance.direction,
      'id': instance.id,
      'market': instance.market,
      'securityType': instance.securityType,
      'sellAvgPrice': instance.sellAvgPrice,
      'symbol': instance.symbol,
      'symbolName': instance.symbolName,
      'tradeType': instance.tradeType,
      'tradeUnit': instance.tradeUnit,
      'type': instance.type,
      'updateTime': instance.updateTime,
      'winAmount': instance.winAmount,
    };
