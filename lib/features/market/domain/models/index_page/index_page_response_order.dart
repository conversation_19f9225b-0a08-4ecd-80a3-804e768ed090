import 'package:freezed_annotation/freezed_annotation.dart';

part 'index_page_response_order.freezed.dart';
part 'index_page_response_order.g.dart';


@freezed
class IndexPageOrderResponse with _$IndexPageOrderResponse {
  const factory IndexPageOrderResponse({
    @Default(0) int current,
    @Default(false) bool hasNext,
    @Default([]) List<IndexOrderRecord> records,
    @Default(0) int total,
  }) = _IndexPageOrderResponse;

  factory IndexPageOrderResponse.fromJson(Map<String, dynamic> json) => _$IndexPageOrderResponseFromJson(json);
}

@freezed
class IndexOrderRecord with _$IndexOrderRecord {
  const factory IndexOrderRecord({
    @Default('') String createTime,
    @Default('') String currency,
    @Default(0.0) double dealFee,
    @Default(0.0) double dealNum,
    @Default(0.0) double dealPrice,
    @Default('') String dealTime,
    @Default(0) int direction,
    @Default(0) int id,
    @Default('') String market,
    @Default('') String securityType,
    @Default('') String symbol,
    @Default('') String symbolName,
    @Default(0) int tradeType,
    @Default(0.0) double tradeUnit,
    @Default(0) int type,
    @Default('') String updateTime,
    @Default(0.0) double winAmount,
  }) = _IndexOrderRecord;

  factory IndexOrderRecord.fromJson(Map<String, dynamic> json) => _$IndexOrderRecordFromJson(json);
}
