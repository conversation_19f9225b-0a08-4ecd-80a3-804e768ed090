// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'index_page_response_order.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

IndexPageOrderResponse _$IndexPageOrderResponseFromJson(
    Map<String, dynamic> json) {
  return _IndexPageOrderResponse.fromJson(json);
}

/// @nodoc
mixin _$IndexPageOrderResponse {
  int get current => throw _privateConstructorUsedError;
  bool get hasNext => throw _privateConstructorUsedError;
  List<IndexOrderRecord> get records => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;

  /// Serializes this IndexPageOrderResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of IndexPageOrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IndexPageOrderResponseCopyWith<IndexPageOrderResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IndexPageOrderResponseCopyWith<$Res> {
  factory $IndexPageOrderResponseCopyWith(IndexPageOrderResponse value,
          $Res Function(IndexPageOrderResponse) then) =
      _$IndexPageOrderResponseCopyWithImpl<$Res, IndexPageOrderResponse>;
  @useResult
  $Res call(
      {int current, bool hasNext, List<IndexOrderRecord> records, int total});
}

/// @nodoc
class _$IndexPageOrderResponseCopyWithImpl<$Res,
        $Val extends IndexPageOrderResponse>
    implements $IndexPageOrderResponseCopyWith<$Res> {
  _$IndexPageOrderResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IndexPageOrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = null,
    Object? hasNext = null,
    Object? records = null,
    Object? total = null,
  }) {
    return _then(_value.copyWith(
      current: null == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int,
      hasNext: null == hasNext
          ? _value.hasNext
          : hasNext // ignore: cast_nullable_to_non_nullable
              as bool,
      records: null == records
          ? _value.records
          : records // ignore: cast_nullable_to_non_nullable
              as List<IndexOrderRecord>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$IndexPageOrderResponseImplCopyWith<$Res>
    implements $IndexPageOrderResponseCopyWith<$Res> {
  factory _$$IndexPageOrderResponseImplCopyWith(
          _$IndexPageOrderResponseImpl value,
          $Res Function(_$IndexPageOrderResponseImpl) then) =
      __$$IndexPageOrderResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int current, bool hasNext, List<IndexOrderRecord> records, int total});
}

/// @nodoc
class __$$IndexPageOrderResponseImplCopyWithImpl<$Res>
    extends _$IndexPageOrderResponseCopyWithImpl<$Res,
        _$IndexPageOrderResponseImpl>
    implements _$$IndexPageOrderResponseImplCopyWith<$Res> {
  __$$IndexPageOrderResponseImplCopyWithImpl(
      _$IndexPageOrderResponseImpl _value,
      $Res Function(_$IndexPageOrderResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of IndexPageOrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = null,
    Object? hasNext = null,
    Object? records = null,
    Object? total = null,
  }) {
    return _then(_$IndexPageOrderResponseImpl(
      current: null == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int,
      hasNext: null == hasNext
          ? _value.hasNext
          : hasNext // ignore: cast_nullable_to_non_nullable
              as bool,
      records: null == records
          ? _value._records
          : records // ignore: cast_nullable_to_non_nullable
              as List<IndexOrderRecord>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$IndexPageOrderResponseImpl implements _IndexPageOrderResponse {
  const _$IndexPageOrderResponseImpl(
      {this.current = 0,
      this.hasNext = false,
      final List<IndexOrderRecord> records = const [],
      this.total = 0})
      : _records = records;

  factory _$IndexPageOrderResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$IndexPageOrderResponseImplFromJson(json);

  @override
  @JsonKey()
  final int current;
  @override
  @JsonKey()
  final bool hasNext;
  final List<IndexOrderRecord> _records;
  @override
  @JsonKey()
  List<IndexOrderRecord> get records {
    if (_records is EqualUnmodifiableListView) return _records;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_records);
  }

  @override
  @JsonKey()
  final int total;

  @override
  String toString() {
    return 'IndexPageOrderResponse(current: $current, hasNext: $hasNext, records: $records, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IndexPageOrderResponseImpl &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.hasNext, hasNext) || other.hasNext == hasNext) &&
            const DeepCollectionEquality().equals(other._records, _records) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, current, hasNext,
      const DeepCollectionEquality().hash(_records), total);

  /// Create a copy of IndexPageOrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IndexPageOrderResponseImplCopyWith<_$IndexPageOrderResponseImpl>
      get copyWith => __$$IndexPageOrderResponseImplCopyWithImpl<
          _$IndexPageOrderResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$IndexPageOrderResponseImplToJson(
      this,
    );
  }
}

abstract class _IndexPageOrderResponse implements IndexPageOrderResponse {
  const factory _IndexPageOrderResponse(
      {final int current,
      final bool hasNext,
      final List<IndexOrderRecord> records,
      final int total}) = _$IndexPageOrderResponseImpl;

  factory _IndexPageOrderResponse.fromJson(Map<String, dynamic> json) =
      _$IndexPageOrderResponseImpl.fromJson;

  @override
  int get current;
  @override
  bool get hasNext;
  @override
  List<IndexOrderRecord> get records;
  @override
  int get total;

  /// Create a copy of IndexPageOrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IndexPageOrderResponseImplCopyWith<_$IndexPageOrderResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

IndexOrderRecord _$IndexOrderRecordFromJson(Map<String, dynamic> json) {
  return _IndexOrderRecord.fromJson(json);
}

/// @nodoc
mixin _$IndexOrderRecord {
  String get createTime => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  double get dealFee => throw _privateConstructorUsedError;
  double get dealNum => throw _privateConstructorUsedError;
  double get dealPrice => throw _privateConstructorUsedError;
  String get dealTime => throw _privateConstructorUsedError;
  int get direction => throw _privateConstructorUsedError;
  int get id => throw _privateConstructorUsedError;
  String get market => throw _privateConstructorUsedError;
  String get securityType => throw _privateConstructorUsedError;
  String get symbol => throw _privateConstructorUsedError;
  String get symbolName => throw _privateConstructorUsedError;
  int get tradeType => throw _privateConstructorUsedError;
  double get tradeUnit => throw _privateConstructorUsedError;
  int get type => throw _privateConstructorUsedError;
  String get updateTime => throw _privateConstructorUsedError;
  double get winAmount => throw _privateConstructorUsedError;

  /// Serializes this IndexOrderRecord to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of IndexOrderRecord
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IndexOrderRecordCopyWith<IndexOrderRecord> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IndexOrderRecordCopyWith<$Res> {
  factory $IndexOrderRecordCopyWith(
          IndexOrderRecord value, $Res Function(IndexOrderRecord) then) =
      _$IndexOrderRecordCopyWithImpl<$Res, IndexOrderRecord>;
  @useResult
  $Res call(
      {String createTime,
      String currency,
      double dealFee,
      double dealNum,
      double dealPrice,
      String dealTime,
      int direction,
      int id,
      String market,
      String securityType,
      String symbol,
      String symbolName,
      int tradeType,
      double tradeUnit,
      int type,
      String updateTime,
      double winAmount});
}

/// @nodoc
class _$IndexOrderRecordCopyWithImpl<$Res, $Val extends IndexOrderRecord>
    implements $IndexOrderRecordCopyWith<$Res> {
  _$IndexOrderRecordCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IndexOrderRecord
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createTime = null,
    Object? currency = null,
    Object? dealFee = null,
    Object? dealNum = null,
    Object? dealPrice = null,
    Object? dealTime = null,
    Object? direction = null,
    Object? id = null,
    Object? market = null,
    Object? securityType = null,
    Object? symbol = null,
    Object? symbolName = null,
    Object? tradeType = null,
    Object? tradeUnit = null,
    Object? type = null,
    Object? updateTime = null,
    Object? winAmount = null,
  }) {
    return _then(_value.copyWith(
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      dealFee: null == dealFee
          ? _value.dealFee
          : dealFee // ignore: cast_nullable_to_non_nullable
              as double,
      dealNum: null == dealNum
          ? _value.dealNum
          : dealNum // ignore: cast_nullable_to_non_nullable
              as double,
      dealPrice: null == dealPrice
          ? _value.dealPrice
          : dealPrice // ignore: cast_nullable_to_non_nullable
              as double,
      dealTime: null == dealTime
          ? _value.dealTime
          : dealTime // ignore: cast_nullable_to_non_nullable
              as String,
      direction: null == direction
          ? _value.direction
          : direction // ignore: cast_nullable_to_non_nullable
              as int,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      market: null == market
          ? _value.market
          : market // ignore: cast_nullable_to_non_nullable
              as String,
      securityType: null == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as String,
      symbol: null == symbol
          ? _value.symbol
          : symbol // ignore: cast_nullable_to_non_nullable
              as String,
      symbolName: null == symbolName
          ? _value.symbolName
          : symbolName // ignore: cast_nullable_to_non_nullable
              as String,
      tradeType: null == tradeType
          ? _value.tradeType
          : tradeType // ignore: cast_nullable_to_non_nullable
              as int,
      tradeUnit: null == tradeUnit
          ? _value.tradeUnit
          : tradeUnit // ignore: cast_nullable_to_non_nullable
              as double,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      updateTime: null == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String,
      winAmount: null == winAmount
          ? _value.winAmount
          : winAmount // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$IndexOrderRecordImplCopyWith<$Res>
    implements $IndexOrderRecordCopyWith<$Res> {
  factory _$$IndexOrderRecordImplCopyWith(_$IndexOrderRecordImpl value,
          $Res Function(_$IndexOrderRecordImpl) then) =
      __$$IndexOrderRecordImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String createTime,
      String currency,
      double dealFee,
      double dealNum,
      double dealPrice,
      String dealTime,
      int direction,
      int id,
      String market,
      String securityType,
      String symbol,
      String symbolName,
      int tradeType,
      double tradeUnit,
      int type,
      String updateTime,
      double winAmount});
}

/// @nodoc
class __$$IndexOrderRecordImplCopyWithImpl<$Res>
    extends _$IndexOrderRecordCopyWithImpl<$Res, _$IndexOrderRecordImpl>
    implements _$$IndexOrderRecordImplCopyWith<$Res> {
  __$$IndexOrderRecordImplCopyWithImpl(_$IndexOrderRecordImpl _value,
      $Res Function(_$IndexOrderRecordImpl) _then)
      : super(_value, _then);

  /// Create a copy of IndexOrderRecord
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createTime = null,
    Object? currency = null,
    Object? dealFee = null,
    Object? dealNum = null,
    Object? dealPrice = null,
    Object? dealTime = null,
    Object? direction = null,
    Object? id = null,
    Object? market = null,
    Object? securityType = null,
    Object? symbol = null,
    Object? symbolName = null,
    Object? tradeType = null,
    Object? tradeUnit = null,
    Object? type = null,
    Object? updateTime = null,
    Object? winAmount = null,
  }) {
    return _then(_$IndexOrderRecordImpl(
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      dealFee: null == dealFee
          ? _value.dealFee
          : dealFee // ignore: cast_nullable_to_non_nullable
              as double,
      dealNum: null == dealNum
          ? _value.dealNum
          : dealNum // ignore: cast_nullable_to_non_nullable
              as double,
      dealPrice: null == dealPrice
          ? _value.dealPrice
          : dealPrice // ignore: cast_nullable_to_non_nullable
              as double,
      dealTime: null == dealTime
          ? _value.dealTime
          : dealTime // ignore: cast_nullable_to_non_nullable
              as String,
      direction: null == direction
          ? _value.direction
          : direction // ignore: cast_nullable_to_non_nullable
              as int,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      market: null == market
          ? _value.market
          : market // ignore: cast_nullable_to_non_nullable
              as String,
      securityType: null == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as String,
      symbol: null == symbol
          ? _value.symbol
          : symbol // ignore: cast_nullable_to_non_nullable
              as String,
      symbolName: null == symbolName
          ? _value.symbolName
          : symbolName // ignore: cast_nullable_to_non_nullable
              as String,
      tradeType: null == tradeType
          ? _value.tradeType
          : tradeType // ignore: cast_nullable_to_non_nullable
              as int,
      tradeUnit: null == tradeUnit
          ? _value.tradeUnit
          : tradeUnit // ignore: cast_nullable_to_non_nullable
              as double,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      updateTime: null == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String,
      winAmount: null == winAmount
          ? _value.winAmount
          : winAmount // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$IndexOrderRecordImpl implements _IndexOrderRecord {
  const _$IndexOrderRecordImpl(
      {this.createTime = '',
      this.currency = '',
      this.dealFee = 0.0,
      this.dealNum = 0.0,
      this.dealPrice = 0.0,
      this.dealTime = '',
      this.direction = 0,
      this.id = 0,
      this.market = '',
      this.securityType = '',
      this.symbol = '',
      this.symbolName = '',
      this.tradeType = 0,
      this.tradeUnit = 0.0,
      this.type = 0,
      this.updateTime = '',
      this.winAmount = 0.0});

  factory _$IndexOrderRecordImpl.fromJson(Map<String, dynamic> json) =>
      _$$IndexOrderRecordImplFromJson(json);

  @override
  @JsonKey()
  final String createTime;
  @override
  @JsonKey()
  final String currency;
  @override
  @JsonKey()
  final double dealFee;
  @override
  @JsonKey()
  final double dealNum;
  @override
  @JsonKey()
  final double dealPrice;
  @override
  @JsonKey()
  final String dealTime;
  @override
  @JsonKey()
  final int direction;
  @override
  @JsonKey()
  final int id;
  @override
  @JsonKey()
  final String market;
  @override
  @JsonKey()
  final String securityType;
  @override
  @JsonKey()
  final String symbol;
  @override
  @JsonKey()
  final String symbolName;
  @override
  @JsonKey()
  final int tradeType;
  @override
  @JsonKey()
  final double tradeUnit;
  @override
  @JsonKey()
  final int type;
  @override
  @JsonKey()
  final String updateTime;
  @override
  @JsonKey()
  final double winAmount;

  @override
  String toString() {
    return 'IndexOrderRecord(createTime: $createTime, currency: $currency, dealFee: $dealFee, dealNum: $dealNum, dealPrice: $dealPrice, dealTime: $dealTime, direction: $direction, id: $id, market: $market, securityType: $securityType, symbol: $symbol, symbolName: $symbolName, tradeType: $tradeType, tradeUnit: $tradeUnit, type: $type, updateTime: $updateTime, winAmount: $winAmount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IndexOrderRecordImpl &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.dealFee, dealFee) || other.dealFee == dealFee) &&
            (identical(other.dealNum, dealNum) || other.dealNum == dealNum) &&
            (identical(other.dealPrice, dealPrice) ||
                other.dealPrice == dealPrice) &&
            (identical(other.dealTime, dealTime) ||
                other.dealTime == dealTime) &&
            (identical(other.direction, direction) ||
                other.direction == direction) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.market, market) || other.market == market) &&
            (identical(other.securityType, securityType) ||
                other.securityType == securityType) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.symbolName, symbolName) ||
                other.symbolName == symbolName) &&
            (identical(other.tradeType, tradeType) ||
                other.tradeType == tradeType) &&
            (identical(other.tradeUnit, tradeUnit) ||
                other.tradeUnit == tradeUnit) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            (identical(other.winAmount, winAmount) ||
                other.winAmount == winAmount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      createTime,
      currency,
      dealFee,
      dealNum,
      dealPrice,
      dealTime,
      direction,
      id,
      market,
      securityType,
      symbol,
      symbolName,
      tradeType,
      tradeUnit,
      type,
      updateTime,
      winAmount);

  /// Create a copy of IndexOrderRecord
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IndexOrderRecordImplCopyWith<_$IndexOrderRecordImpl> get copyWith =>
      __$$IndexOrderRecordImplCopyWithImpl<_$IndexOrderRecordImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$IndexOrderRecordImplToJson(
      this,
    );
  }
}

abstract class _IndexOrderRecord implements IndexOrderRecord {
  const factory _IndexOrderRecord(
      {final String createTime,
      final String currency,
      final double dealFee,
      final double dealNum,
      final double dealPrice,
      final String dealTime,
      final int direction,
      final int id,
      final String market,
      final String securityType,
      final String symbol,
      final String symbolName,
      final int tradeType,
      final double tradeUnit,
      final int type,
      final String updateTime,
      final double winAmount}) = _$IndexOrderRecordImpl;

  factory _IndexOrderRecord.fromJson(Map<String, dynamic> json) =
      _$IndexOrderRecordImpl.fromJson;

  @override
  String get createTime;
  @override
  String get currency;
  @override
  double get dealFee;
  @override
  double get dealNum;
  @override
  double get dealPrice;
  @override
  String get dealTime;
  @override
  int get direction;
  @override
  int get id;
  @override
  String get market;
  @override
  String get securityType;
  @override
  String get symbol;
  @override
  String get symbolName;
  @override
  int get tradeType;
  @override
  double get tradeUnit;
  @override
  int get type;
  @override
  String get updateTime;
  @override
  double get winAmount;

  /// Create a copy of IndexOrderRecord
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IndexOrderRecordImplCopyWith<_$IndexOrderRecordImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
