import 'package:freezed_annotation/freezed_annotation.dart';

part 'index_page_response_position.freezed.dart';
part 'index_page_response_position.g.dart';

@freezed
class IndexPagePositionResponse with _$IndexPagePositionResponse {
  const factory IndexPagePositionResponse({
    @Default(0) int current,
    @Default(false) bool hasNext,
    @Default([]) List<IndexRecord> records,
    @Default(0) int total,
  }) = _IndexPagePositionResponse;

  factory IndexPagePositionResponse.fromJson(Map<String, dynamic> json) => _$IndexPagePositionResponseFromJson(json);
}

@freezed
class IndexRecord with _$IndexRecord {
  const factory IndexRecord({
    @Default(0.0) double buyAvgPrice,
    @Default(0.0) double buyTotalNum,
    @Default('') String createTime,
    @Default('') String currency,
    @Default(0) int direction,
    @Default(0) int id,
    @Default('') String market,
    @Default('') String securityType,
    @Default(0.0) double sellAvgPrice,
    @Default('') String symbol,
    @Default('') String symbolName,
    @Default(0) int tradeType,
    @Default(0) int tradeUnit,
    @Default(0) int type,
    @Default('') String updateTime,
    @Default(0.0) double winAmount,
  }) = _IndexRecord;

  factory IndexRecord.fromJson(Map<String, dynamic> json) => _$IndexRecordFromJson(json);
}
