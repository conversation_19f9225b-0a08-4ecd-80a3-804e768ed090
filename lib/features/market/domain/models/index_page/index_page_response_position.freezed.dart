// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'index_page_response_position.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

IndexPagePositionResponse _$IndexPagePositionResponseFromJson(
    Map<String, dynamic> json) {
  return _IndexPagePositionResponse.fromJson(json);
}

/// @nodoc
mixin _$IndexPagePositionResponse {
  int get current => throw _privateConstructorUsedError;
  bool get hasNext => throw _privateConstructorUsedError;
  List<IndexRecord> get records => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;

  /// Serializes this IndexPagePositionResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of IndexPagePositionResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IndexPagePositionResponseCopyWith<IndexPagePositionResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IndexPagePositionResponseCopyWith<$Res> {
  factory $IndexPagePositionResponseCopyWith(IndexPagePositionResponse value,
          $Res Function(IndexPagePositionResponse) then) =
      _$IndexPagePositionResponseCopyWithImpl<$Res, IndexPagePositionResponse>;
  @useResult
  $Res call({int current, bool hasNext, List<IndexRecord> records, int total});
}

/// @nodoc
class _$IndexPagePositionResponseCopyWithImpl<$Res,
        $Val extends IndexPagePositionResponse>
    implements $IndexPagePositionResponseCopyWith<$Res> {
  _$IndexPagePositionResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IndexPagePositionResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = null,
    Object? hasNext = null,
    Object? records = null,
    Object? total = null,
  }) {
    return _then(_value.copyWith(
      current: null == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int,
      hasNext: null == hasNext
          ? _value.hasNext
          : hasNext // ignore: cast_nullable_to_non_nullable
              as bool,
      records: null == records
          ? _value.records
          : records // ignore: cast_nullable_to_non_nullable
              as List<IndexRecord>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$IndexPagePositionResponseImplCopyWith<$Res>
    implements $IndexPagePositionResponseCopyWith<$Res> {
  factory _$$IndexPagePositionResponseImplCopyWith(
          _$IndexPagePositionResponseImpl value,
          $Res Function(_$IndexPagePositionResponseImpl) then) =
      __$$IndexPagePositionResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int current, bool hasNext, List<IndexRecord> records, int total});
}

/// @nodoc
class __$$IndexPagePositionResponseImplCopyWithImpl<$Res>
    extends _$IndexPagePositionResponseCopyWithImpl<$Res,
        _$IndexPagePositionResponseImpl>
    implements _$$IndexPagePositionResponseImplCopyWith<$Res> {
  __$$IndexPagePositionResponseImplCopyWithImpl(
      _$IndexPagePositionResponseImpl _value,
      $Res Function(_$IndexPagePositionResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of IndexPagePositionResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = null,
    Object? hasNext = null,
    Object? records = null,
    Object? total = null,
  }) {
    return _then(_$IndexPagePositionResponseImpl(
      current: null == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int,
      hasNext: null == hasNext
          ? _value.hasNext
          : hasNext // ignore: cast_nullable_to_non_nullable
              as bool,
      records: null == records
          ? _value._records
          : records // ignore: cast_nullable_to_non_nullable
              as List<IndexRecord>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$IndexPagePositionResponseImpl implements _IndexPagePositionResponse {
  const _$IndexPagePositionResponseImpl(
      {this.current = 0,
      this.hasNext = false,
      final List<IndexRecord> records = const [],
      this.total = 0})
      : _records = records;

  factory _$IndexPagePositionResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$IndexPagePositionResponseImplFromJson(json);

  @override
  @JsonKey()
  final int current;
  @override
  @JsonKey()
  final bool hasNext;
  final List<IndexRecord> _records;
  @override
  @JsonKey()
  List<IndexRecord> get records {
    if (_records is EqualUnmodifiableListView) return _records;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_records);
  }

  @override
  @JsonKey()
  final int total;

  @override
  String toString() {
    return 'IndexPagePositionResponse(current: $current, hasNext: $hasNext, records: $records, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IndexPagePositionResponseImpl &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.hasNext, hasNext) || other.hasNext == hasNext) &&
            const DeepCollectionEquality().equals(other._records, _records) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, current, hasNext,
      const DeepCollectionEquality().hash(_records), total);

  /// Create a copy of IndexPagePositionResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IndexPagePositionResponseImplCopyWith<_$IndexPagePositionResponseImpl>
      get copyWith => __$$IndexPagePositionResponseImplCopyWithImpl<
          _$IndexPagePositionResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$IndexPagePositionResponseImplToJson(
      this,
    );
  }
}

abstract class _IndexPagePositionResponse implements IndexPagePositionResponse {
  const factory _IndexPagePositionResponse(
      {final int current,
      final bool hasNext,
      final List<IndexRecord> records,
      final int total}) = _$IndexPagePositionResponseImpl;

  factory _IndexPagePositionResponse.fromJson(Map<String, dynamic> json) =
      _$IndexPagePositionResponseImpl.fromJson;

  @override
  int get current;
  @override
  bool get hasNext;
  @override
  List<IndexRecord> get records;
  @override
  int get total;

  /// Create a copy of IndexPagePositionResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IndexPagePositionResponseImplCopyWith<_$IndexPagePositionResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

IndexRecord _$IndexRecordFromJson(Map<String, dynamic> json) {
  return _IndexRecord.fromJson(json);
}

/// @nodoc
mixin _$IndexRecord {
  double get buyAvgPrice => throw _privateConstructorUsedError;
  double get buyTotalNum => throw _privateConstructorUsedError;
  String get createTime => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  int get direction => throw _privateConstructorUsedError;
  int get id => throw _privateConstructorUsedError;
  String get market => throw _privateConstructorUsedError;
  String get securityType => throw _privateConstructorUsedError;
  double get sellAvgPrice => throw _privateConstructorUsedError;
  String get symbol => throw _privateConstructorUsedError;
  String get symbolName => throw _privateConstructorUsedError;
  int get tradeType => throw _privateConstructorUsedError;
  int get tradeUnit => throw _privateConstructorUsedError;
  int get type => throw _privateConstructorUsedError;
  String get updateTime => throw _privateConstructorUsedError;
  double get winAmount => throw _privateConstructorUsedError;

  /// Serializes this IndexRecord to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of IndexRecord
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IndexRecordCopyWith<IndexRecord> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IndexRecordCopyWith<$Res> {
  factory $IndexRecordCopyWith(
          IndexRecord value, $Res Function(IndexRecord) then) =
      _$IndexRecordCopyWithImpl<$Res, IndexRecord>;
  @useResult
  $Res call(
      {double buyAvgPrice,
      double buyTotalNum,
      String createTime,
      String currency,
      int direction,
      int id,
      String market,
      String securityType,
      double sellAvgPrice,
      String symbol,
      String symbolName,
      int tradeType,
      int tradeUnit,
      int type,
      String updateTime,
      double winAmount});
}

/// @nodoc
class _$IndexRecordCopyWithImpl<$Res, $Val extends IndexRecord>
    implements $IndexRecordCopyWith<$Res> {
  _$IndexRecordCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IndexRecord
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? buyAvgPrice = null,
    Object? buyTotalNum = null,
    Object? createTime = null,
    Object? currency = null,
    Object? direction = null,
    Object? id = null,
    Object? market = null,
    Object? securityType = null,
    Object? sellAvgPrice = null,
    Object? symbol = null,
    Object? symbolName = null,
    Object? tradeType = null,
    Object? tradeUnit = null,
    Object? type = null,
    Object? updateTime = null,
    Object? winAmount = null,
  }) {
    return _then(_value.copyWith(
      buyAvgPrice: null == buyAvgPrice
          ? _value.buyAvgPrice
          : buyAvgPrice // ignore: cast_nullable_to_non_nullable
              as double,
      buyTotalNum: null == buyTotalNum
          ? _value.buyTotalNum
          : buyTotalNum // ignore: cast_nullable_to_non_nullable
              as double,
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      direction: null == direction
          ? _value.direction
          : direction // ignore: cast_nullable_to_non_nullable
              as int,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      market: null == market
          ? _value.market
          : market // ignore: cast_nullable_to_non_nullable
              as String,
      securityType: null == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as String,
      sellAvgPrice: null == sellAvgPrice
          ? _value.sellAvgPrice
          : sellAvgPrice // ignore: cast_nullable_to_non_nullable
              as double,
      symbol: null == symbol
          ? _value.symbol
          : symbol // ignore: cast_nullable_to_non_nullable
              as String,
      symbolName: null == symbolName
          ? _value.symbolName
          : symbolName // ignore: cast_nullable_to_non_nullable
              as String,
      tradeType: null == tradeType
          ? _value.tradeType
          : tradeType // ignore: cast_nullable_to_non_nullable
              as int,
      tradeUnit: null == tradeUnit
          ? _value.tradeUnit
          : tradeUnit // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      updateTime: null == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String,
      winAmount: null == winAmount
          ? _value.winAmount
          : winAmount // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$IndexRecordImplCopyWith<$Res>
    implements $IndexRecordCopyWith<$Res> {
  factory _$$IndexRecordImplCopyWith(
          _$IndexRecordImpl value, $Res Function(_$IndexRecordImpl) then) =
      __$$IndexRecordImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double buyAvgPrice,
      double buyTotalNum,
      String createTime,
      String currency,
      int direction,
      int id,
      String market,
      String securityType,
      double sellAvgPrice,
      String symbol,
      String symbolName,
      int tradeType,
      int tradeUnit,
      int type,
      String updateTime,
      double winAmount});
}

/// @nodoc
class __$$IndexRecordImplCopyWithImpl<$Res>
    extends _$IndexRecordCopyWithImpl<$Res, _$IndexRecordImpl>
    implements _$$IndexRecordImplCopyWith<$Res> {
  __$$IndexRecordImplCopyWithImpl(
      _$IndexRecordImpl _value, $Res Function(_$IndexRecordImpl) _then)
      : super(_value, _then);

  /// Create a copy of IndexRecord
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? buyAvgPrice = null,
    Object? buyTotalNum = null,
    Object? createTime = null,
    Object? currency = null,
    Object? direction = null,
    Object? id = null,
    Object? market = null,
    Object? securityType = null,
    Object? sellAvgPrice = null,
    Object? symbol = null,
    Object? symbolName = null,
    Object? tradeType = null,
    Object? tradeUnit = null,
    Object? type = null,
    Object? updateTime = null,
    Object? winAmount = null,
  }) {
    return _then(_$IndexRecordImpl(
      buyAvgPrice: null == buyAvgPrice
          ? _value.buyAvgPrice
          : buyAvgPrice // ignore: cast_nullable_to_non_nullable
              as double,
      buyTotalNum: null == buyTotalNum
          ? _value.buyTotalNum
          : buyTotalNum // ignore: cast_nullable_to_non_nullable
              as double,
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      direction: null == direction
          ? _value.direction
          : direction // ignore: cast_nullable_to_non_nullable
              as int,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      market: null == market
          ? _value.market
          : market // ignore: cast_nullable_to_non_nullable
              as String,
      securityType: null == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as String,
      sellAvgPrice: null == sellAvgPrice
          ? _value.sellAvgPrice
          : sellAvgPrice // ignore: cast_nullable_to_non_nullable
              as double,
      symbol: null == symbol
          ? _value.symbol
          : symbol // ignore: cast_nullable_to_non_nullable
              as String,
      symbolName: null == symbolName
          ? _value.symbolName
          : symbolName // ignore: cast_nullable_to_non_nullable
              as String,
      tradeType: null == tradeType
          ? _value.tradeType
          : tradeType // ignore: cast_nullable_to_non_nullable
              as int,
      tradeUnit: null == tradeUnit
          ? _value.tradeUnit
          : tradeUnit // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      updateTime: null == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String,
      winAmount: null == winAmount
          ? _value.winAmount
          : winAmount // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$IndexRecordImpl implements _IndexRecord {
  const _$IndexRecordImpl(
      {this.buyAvgPrice = 0.0,
      this.buyTotalNum = 0.0,
      this.createTime = '',
      this.currency = '',
      this.direction = 0,
      this.id = 0,
      this.market = '',
      this.securityType = '',
      this.sellAvgPrice = 0.0,
      this.symbol = '',
      this.symbolName = '',
      this.tradeType = 0,
      this.tradeUnit = 0,
      this.type = 0,
      this.updateTime = '',
      this.winAmount = 0.0});

  factory _$IndexRecordImpl.fromJson(Map<String, dynamic> json) =>
      _$$IndexRecordImplFromJson(json);

  @override
  @JsonKey()
  final double buyAvgPrice;
  @override
  @JsonKey()
  final double buyTotalNum;
  @override
  @JsonKey()
  final String createTime;
  @override
  @JsonKey()
  final String currency;
  @override
  @JsonKey()
  final int direction;
  @override
  @JsonKey()
  final int id;
  @override
  @JsonKey()
  final String market;
  @override
  @JsonKey()
  final String securityType;
  @override
  @JsonKey()
  final double sellAvgPrice;
  @override
  @JsonKey()
  final String symbol;
  @override
  @JsonKey()
  final String symbolName;
  @override
  @JsonKey()
  final int tradeType;
  @override
  @JsonKey()
  final int tradeUnit;
  @override
  @JsonKey()
  final int type;
  @override
  @JsonKey()
  final String updateTime;
  @override
  @JsonKey()
  final double winAmount;

  @override
  String toString() {
    return 'IndexRecord(buyAvgPrice: $buyAvgPrice, buyTotalNum: $buyTotalNum, createTime: $createTime, currency: $currency, direction: $direction, id: $id, market: $market, securityType: $securityType, sellAvgPrice: $sellAvgPrice, symbol: $symbol, symbolName: $symbolName, tradeType: $tradeType, tradeUnit: $tradeUnit, type: $type, updateTime: $updateTime, winAmount: $winAmount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IndexRecordImpl &&
            (identical(other.buyAvgPrice, buyAvgPrice) ||
                other.buyAvgPrice == buyAvgPrice) &&
            (identical(other.buyTotalNum, buyTotalNum) ||
                other.buyTotalNum == buyTotalNum) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.direction, direction) ||
                other.direction == direction) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.market, market) || other.market == market) &&
            (identical(other.securityType, securityType) ||
                other.securityType == securityType) &&
            (identical(other.sellAvgPrice, sellAvgPrice) ||
                other.sellAvgPrice == sellAvgPrice) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.symbolName, symbolName) ||
                other.symbolName == symbolName) &&
            (identical(other.tradeType, tradeType) ||
                other.tradeType == tradeType) &&
            (identical(other.tradeUnit, tradeUnit) ||
                other.tradeUnit == tradeUnit) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            (identical(other.winAmount, winAmount) ||
                other.winAmount == winAmount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      buyAvgPrice,
      buyTotalNum,
      createTime,
      currency,
      direction,
      id,
      market,
      securityType,
      sellAvgPrice,
      symbol,
      symbolName,
      tradeType,
      tradeUnit,
      type,
      updateTime,
      winAmount);

  /// Create a copy of IndexRecord
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IndexRecordImplCopyWith<_$IndexRecordImpl> get copyWith =>
      __$$IndexRecordImplCopyWithImpl<_$IndexRecordImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$IndexRecordImplToJson(
      this,
    );
  }
}

abstract class _IndexRecord implements IndexRecord {
  const factory _IndexRecord(
      {final double buyAvgPrice,
      final double buyTotalNum,
      final String createTime,
      final String currency,
      final int direction,
      final int id,
      final String market,
      final String securityType,
      final double sellAvgPrice,
      final String symbol,
      final String symbolName,
      final int tradeType,
      final int tradeUnit,
      final int type,
      final String updateTime,
      final double winAmount}) = _$IndexRecordImpl;

  factory _IndexRecord.fromJson(Map<String, dynamic> json) =
      _$IndexRecordImpl.fromJson;

  @override
  double get buyAvgPrice;
  @override
  double get buyTotalNum;
  @override
  String get createTime;
  @override
  String get currency;
  @override
  int get direction;
  @override
  int get id;
  @override
  String get market;
  @override
  String get securityType;
  @override
  double get sellAvgPrice;
  @override
  String get symbol;
  @override
  String get symbolName;
  @override
  int get tradeType;
  @override
  int get tradeUnit;
  @override
  int get type;
  @override
  String get updateTime;
  @override
  double get winAmount;

  /// Create a copy of IndexRecord
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IndexRecordImplCopyWith<_$IndexRecordImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
