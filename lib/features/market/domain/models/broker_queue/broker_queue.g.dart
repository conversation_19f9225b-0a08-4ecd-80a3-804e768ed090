// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'broker_queue.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BrokerQueueResponseImpl _$$BrokerQueueResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$BrokerQueueResponseImpl(
      market: json['market'] as String,
      symbol: json['symbol'] as String,
      securityType: json['securityType'] as String,
      buy: (json['buy'] as List<dynamic>)
          .map((e) => BrokerQueueLevel.fromJson(e as Map<String, dynamic>))
          .toList(),
      sell: (json['sell'] as List<dynamic>)
          .map((e) => BrokerQueueLevel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$BrokerQueueResponseImplToJson(
        _$BrokerQueueResponseImpl instance) =>
    <String, dynamic>{
      'market': instance.market,
      'symbol': instance.symbol,
      'securityType': instance.securityType,
      'buy': instance.buy,
      'sell': instance.sell,
    };

_$BrokerQueueLevelImpl _$$BrokerQueueLevelImplFromJson(
        Map<String, dynamic> json) =>
    _$BrokerQueueLevelImpl(
      level: (json['level'] as num).toInt(),
      price: (json['price'] as num).toDouble(),
      num: (json['num'] as num).toInt(),
      list: (json['list'] as List<dynamic>)
          .map((e) => BrokerInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$BrokerQueueLevelImplToJson(
        _$BrokerQueueLevelImpl instance) =>
    <String, dynamic>{
      'level': instance.level,
      'price': instance.price,
      'num': instance.num,
      'list': instance.list,
    };

_$BrokerInfoImpl _$$BrokerInfoImplFromJson(Map<String, dynamic> json) =>
    _$BrokerInfoImpl(
      brokerNameEn: json['brokerNameEn'] as String?,
      brokerId: json['brokerId'] as String,
      brokerName: json['brokerName'] as String,
      brokerNameTc: json['brokerNameTc'] as String?,
    );

Map<String, dynamic> _$$BrokerInfoImplToJson(_$BrokerInfoImpl instance) =>
    <String, dynamic>{
      'brokerNameEn': instance.brokerNameEn,
      'brokerId': instance.brokerId,
      'brokerName': instance.brokerName,
      'brokerNameTc': instance.brokerNameTc,
    };
