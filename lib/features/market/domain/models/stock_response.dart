import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_reponse/stock_info_response.dart';

part 'stock_response.freezed.dart';
part 'stock_response.g.dart';

@freezed
class StockResponse with _$StockResponse {
  const factory StockResponse({
    int? code,
    String? msg,
    StockData? data,
  }) = _StockResponse;

  factory StockResponse.fromJson(Map<String, dynamic> json) => _$StockResponseFromJson(json);
}

@freezed
class StockData with _$StockData {
  const factory StockData({
    List<StockInfoData>? list,
  }) = _StockData;

  factory StockData.fromJson(Map<String, dynamic> json) => _$StockDataFromJson(json);
}

