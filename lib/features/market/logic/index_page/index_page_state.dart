part of 'index_page_cubit.dart';

class IndexPageState extends Equatable {
  const IndexPageState({
    this.indexPageFetchStatus = DataStatus.idle,
    this.indexPageLoadMoreStatus = DataStatus.idle,
    this.indexPagePositionResponse = const IndexPagePositionResponse(),
    this.indexPageOrderResponse = const IndexPageOrderResponse(),
    this.error,
  });

  final DataStatus indexPageFetchStatus;
  final DataStatus indexPageLoadMoreStatus;
  final IndexPagePositionResponse indexPagePositionResponse;
  final IndexPageOrderResponse indexPageOrderResponse;
  final String? error;

  @override
  List<Object?> get props => [
        indexPageFetchStatus,
        indexPagePositionResponse,
        indexPageOrderResponse,
        error,
      ];

  IndexPageState copyWith({
    DataStatus? indexPageFetchStatus,
    DataStatus? indexPageLoadMoreStatus,
    IndexPagePositionResponse? indexPageResponse,
    IndexPageOrderResponse? indexPageOrderResponse,
    String? error,
  }) {
    return IndexPageState(
      indexPageFetchStatus: indexPageFetchStatus ?? this.indexPageFetchStatus,
      indexPageLoadMoreStatus: indexPageLoadMoreStatus ?? this.indexPageLoadMoreStatus,
      indexPagePositionResponse: indexPageResponse ?? indexPagePositionResponse,
      indexPageOrderResponse: indexPageOrderResponse ?? this.indexPageOrderResponse,
      error: error ?? this.error,
    );
  }
}
