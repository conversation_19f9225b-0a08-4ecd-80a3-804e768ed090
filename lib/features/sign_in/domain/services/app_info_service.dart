import 'package:dio/dio.dart';import 'package:injectable/injectable.dart';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/api/network/models/result.dart';
import '../../../../core/api/network/network.dart';
import '../models/app_info/app_info.dart';
import '../repository/app_info_repository.dart';

@Injectable(as: AppInfoRepository)
class AppInfoService implements AppInfoRepository {
  @override
  Future<ResponseResult<AppInfo>> getInfoById(int id) async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.appInfo,
        queryParameters: {'id': id},
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: AppInfo.fromJson(response.data['data']));
        }
        return ResponseResult(error: response.data['msg']);
      }
      return ResponseResult(error: 'Failed to get info');
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

}
