// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'login_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LoginResponse _$LoginResponseFromJson(Map<String, dynamic> json) {
  return _LoginResponse.fromJson(json);
}

/// @nodoc
mixin _$LoginResponse {
  @JsonKey(name: "code")
  int? get code => throw _privateConstructorUsedError;
  @JsonKey(name: "data")
  UserData? get data => throw _privateConstructorUsedError;
  @JsonKey(name: "msg")
  String? get msg => throw _privateConstructorUsedError;

  /// Serializes this LoginResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LoginResponseCopyWith<LoginResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoginResponseCopyWith<$Res> {
  factory $LoginResponseCopyWith(
          LoginResponse value, $Res Function(LoginResponse) then) =
      _$LoginResponseCopyWithImpl<$Res, LoginResponse>;
  @useResult
  $Res call(
      {@JsonKey(name: "code") int? code,
      @JsonKey(name: "data") UserData? data,
      @JsonKey(name: "msg") String? msg});

  $UserDataCopyWith<$Res>? get data;
}

/// @nodoc
class _$LoginResponseCopyWithImpl<$Res, $Val extends LoginResponse>
    implements $LoginResponseCopyWith<$Res> {
  _$LoginResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_value.copyWith(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as UserData?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of LoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserDataCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $UserDataCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LoginResponseImplCopyWith<$Res>
    implements $LoginResponseCopyWith<$Res> {
  factory _$$LoginResponseImplCopyWith(
          _$LoginResponseImpl value, $Res Function(_$LoginResponseImpl) then) =
      __$$LoginResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "code") int? code,
      @JsonKey(name: "data") UserData? data,
      @JsonKey(name: "msg") String? msg});

  @override
  $UserDataCopyWith<$Res>? get data;
}

/// @nodoc
class __$$LoginResponseImplCopyWithImpl<$Res>
    extends _$LoginResponseCopyWithImpl<$Res, _$LoginResponseImpl>
    implements _$$LoginResponseImplCopyWith<$Res> {
  __$$LoginResponseImplCopyWithImpl(
      _$LoginResponseImpl _value, $Res Function(_$LoginResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_$LoginResponseImpl(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as UserData?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LoginResponseImpl implements _LoginResponse {
  const _$LoginResponseImpl(
      {@JsonKey(name: "code") this.code,
      @JsonKey(name: "data") this.data,
      @JsonKey(name: "msg") this.msg});

  factory _$LoginResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$LoginResponseImplFromJson(json);

  @override
  @JsonKey(name: "code")
  final int? code;
  @override
  @JsonKey(name: "data")
  final UserData? data;
  @override
  @JsonKey(name: "msg")
  final String? msg;

  @override
  String toString() {
    return 'LoginResponse(code: $code, data: $data, msg: $msg)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginResponseImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.msg, msg) || other.msg == msg));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, data, msg);

  /// Create a copy of LoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginResponseImplCopyWith<_$LoginResponseImpl> get copyWith =>
      __$$LoginResponseImplCopyWithImpl<_$LoginResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LoginResponseImplToJson(
      this,
    );
  }
}

abstract class _LoginResponse implements LoginResponse {
  const factory _LoginResponse(
      {@JsonKey(name: "code") final int? code,
      @JsonKey(name: "data") final UserData? data,
      @JsonKey(name: "msg") final String? msg}) = _$LoginResponseImpl;

  factory _LoginResponse.fromJson(Map<String, dynamic> json) =
      _$LoginResponseImpl.fromJson;

  @override
  @JsonKey(name: "code")
  int? get code;
  @override
  @JsonKey(name: "data")
  UserData? get data;
  @override
  @JsonKey(name: "msg")
  String? get msg;

  /// Create a copy of LoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoginResponseImplCopyWith<_$LoginResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserData _$UserDataFromJson(Map<String, dynamic> json) {
  return _UserData.fromJson(json);
}

/// @nodoc
mixin _$UserData {
  @JsonKey(name: "auth")
  bool? get auth => throw _privateConstructorUsedError;
  @JsonKey(name: "authStatus")
  int? get authStatus => throw _privateConstructorUsedError;
  @JsonKey(name: "avatar")
  String? get avatar => throw _privateConstructorUsedError;
  @JsonKey(name: "countryCode")
  String? get countryCode => throw _privateConstructorUsedError;
  @JsonKey(name: "email")
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: "fromType")
  int? get fromType => throw _privateConstructorUsedError;
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "idCard")
  String? get idCard => throw _privateConstructorUsedError;
  @JsonKey(name: "inviteCode")
  String? get inviteCode => throw _privateConstructorUsedError;
  @JsonKey(name: "isPayment")
  bool? get isPayment => throw _privateConstructorUsedError;
  @JsonKey(name: "level")
  int? get level => throw _privateConstructorUsedError;
  @JsonKey(name: "mobile")
  String? get mobile => throw _privateConstructorUsedError;
  @JsonKey(name: "nickname")
  String? get nickname => throw _privateConstructorUsedError;
  @JsonKey(name: "pid")
  int? get pid => throw _privateConstructorUsedError;
  @JsonKey(name: "profiles")
  String? get profiles => throw _privateConstructorUsedError;
  @JsonKey(name: "realName")
  String? get realName => throw _privateConstructorUsedError;
  @JsonKey(name: "score")
  int? get score => throw _privateConstructorUsedError;
  @JsonKey(name: "sex")
  int? get sex => throw _privateConstructorUsedError;
  @JsonKey(name: "status")
  bool? get status => throw _privateConstructorUsedError;
  @JsonKey(name: "tradeStatus")
  int? get tradeStatus => throw _privateConstructorUsedError;
  @JsonKey(name: "type")
  int? get type => throw _privateConstructorUsedError;

  /// Serializes this UserData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserDataCopyWith<UserData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserDataCopyWith<$Res> {
  factory $UserDataCopyWith(UserData value, $Res Function(UserData) then) =
      _$UserDataCopyWithImpl<$Res, UserData>;
  @useResult
  $Res call(
      {@JsonKey(name: "auth") bool? auth,
      @JsonKey(name: "authStatus") int? authStatus,
      @JsonKey(name: "avatar") String? avatar,
      @JsonKey(name: "countryCode") String? countryCode,
      @JsonKey(name: "email") String? email,
      @JsonKey(name: "fromType") int? fromType,
      @JsonKey(name: "id") int? id,
      @JsonKey(name: "idCard") String? idCard,
      @JsonKey(name: "inviteCode") String? inviteCode,
      @JsonKey(name: "isPayment") bool? isPayment,
      @JsonKey(name: "level") int? level,
      @JsonKey(name: "mobile") String? mobile,
      @JsonKey(name: "nickname") String? nickname,
      @JsonKey(name: "pid") int? pid,
      @JsonKey(name: "profiles") String? profiles,
      @JsonKey(name: "realName") String? realName,
      @JsonKey(name: "score") int? score,
      @JsonKey(name: "sex") int? sex,
      @JsonKey(name: "status") bool? status,
      @JsonKey(name: "tradeStatus") int? tradeStatus,
      @JsonKey(name: "type") int? type});
}

/// @nodoc
class _$UserDataCopyWithImpl<$Res, $Val extends UserData>
    implements $UserDataCopyWith<$Res> {
  _$UserDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? auth = freezed,
    Object? authStatus = freezed,
    Object? avatar = freezed,
    Object? countryCode = freezed,
    Object? email = freezed,
    Object? fromType = freezed,
    Object? id = freezed,
    Object? idCard = freezed,
    Object? inviteCode = freezed,
    Object? isPayment = freezed,
    Object? level = freezed,
    Object? mobile = freezed,
    Object? nickname = freezed,
    Object? pid = freezed,
    Object? profiles = freezed,
    Object? realName = freezed,
    Object? score = freezed,
    Object? sex = freezed,
    Object? status = freezed,
    Object? tradeStatus = freezed,
    Object? type = freezed,
  }) {
    return _then(_value.copyWith(
      auth: freezed == auth
          ? _value.auth
          : auth // ignore: cast_nullable_to_non_nullable
              as bool?,
      authStatus: freezed == authStatus
          ? _value.authStatus
          : authStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      avatar: freezed == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      fromType: freezed == fromType
          ? _value.fromType
          : fromType // ignore: cast_nullable_to_non_nullable
              as int?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      idCard: freezed == idCard
          ? _value.idCard
          : idCard // ignore: cast_nullable_to_non_nullable
              as String?,
      inviteCode: freezed == inviteCode
          ? _value.inviteCode
          : inviteCode // ignore: cast_nullable_to_non_nullable
              as String?,
      isPayment: freezed == isPayment
          ? _value.isPayment
          : isPayment // ignore: cast_nullable_to_non_nullable
              as bool?,
      level: freezed == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int?,
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      pid: freezed == pid
          ? _value.pid
          : pid // ignore: cast_nullable_to_non_nullable
              as int?,
      profiles: freezed == profiles
          ? _value.profiles
          : profiles // ignore: cast_nullable_to_non_nullable
              as String?,
      realName: freezed == realName
          ? _value.realName
          : realName // ignore: cast_nullable_to_non_nullable
              as String?,
      score: freezed == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as int?,
      sex: freezed == sex
          ? _value.sex
          : sex // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as bool?,
      tradeStatus: freezed == tradeStatus
          ? _value.tradeStatus
          : tradeStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserDataImplCopyWith<$Res>
    implements $UserDataCopyWith<$Res> {
  factory _$$UserDataImplCopyWith(
          _$UserDataImpl value, $Res Function(_$UserDataImpl) then) =
      __$$UserDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "auth") bool? auth,
      @JsonKey(name: "authStatus") int? authStatus,
      @JsonKey(name: "avatar") String? avatar,
      @JsonKey(name: "countryCode") String? countryCode,
      @JsonKey(name: "email") String? email,
      @JsonKey(name: "fromType") int? fromType,
      @JsonKey(name: "id") int? id,
      @JsonKey(name: "idCard") String? idCard,
      @JsonKey(name: "inviteCode") String? inviteCode,
      @JsonKey(name: "isPayment") bool? isPayment,
      @JsonKey(name: "level") int? level,
      @JsonKey(name: "mobile") String? mobile,
      @JsonKey(name: "nickname") String? nickname,
      @JsonKey(name: "pid") int? pid,
      @JsonKey(name: "profiles") String? profiles,
      @JsonKey(name: "realName") String? realName,
      @JsonKey(name: "score") int? score,
      @JsonKey(name: "sex") int? sex,
      @JsonKey(name: "status") bool? status,
      @JsonKey(name: "tradeStatus") int? tradeStatus,
      @JsonKey(name: "type") int? type});
}

/// @nodoc
class __$$UserDataImplCopyWithImpl<$Res>
    extends _$UserDataCopyWithImpl<$Res, _$UserDataImpl>
    implements _$$UserDataImplCopyWith<$Res> {
  __$$UserDataImplCopyWithImpl(
      _$UserDataImpl _value, $Res Function(_$UserDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? auth = freezed,
    Object? authStatus = freezed,
    Object? avatar = freezed,
    Object? countryCode = freezed,
    Object? email = freezed,
    Object? fromType = freezed,
    Object? id = freezed,
    Object? idCard = freezed,
    Object? inviteCode = freezed,
    Object? isPayment = freezed,
    Object? level = freezed,
    Object? mobile = freezed,
    Object? nickname = freezed,
    Object? pid = freezed,
    Object? profiles = freezed,
    Object? realName = freezed,
    Object? score = freezed,
    Object? sex = freezed,
    Object? status = freezed,
    Object? tradeStatus = freezed,
    Object? type = freezed,
  }) {
    return _then(_$UserDataImpl(
      auth: freezed == auth
          ? _value.auth
          : auth // ignore: cast_nullable_to_non_nullable
              as bool?,
      authStatus: freezed == authStatus
          ? _value.authStatus
          : authStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      avatar: freezed == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      fromType: freezed == fromType
          ? _value.fromType
          : fromType // ignore: cast_nullable_to_non_nullable
              as int?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      idCard: freezed == idCard
          ? _value.idCard
          : idCard // ignore: cast_nullable_to_non_nullable
              as String?,
      inviteCode: freezed == inviteCode
          ? _value.inviteCode
          : inviteCode // ignore: cast_nullable_to_non_nullable
              as String?,
      isPayment: freezed == isPayment
          ? _value.isPayment
          : isPayment // ignore: cast_nullable_to_non_nullable
              as bool?,
      level: freezed == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int?,
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      pid: freezed == pid
          ? _value.pid
          : pid // ignore: cast_nullable_to_non_nullable
              as int?,
      profiles: freezed == profiles
          ? _value.profiles
          : profiles // ignore: cast_nullable_to_non_nullable
              as String?,
      realName: freezed == realName
          ? _value.realName
          : realName // ignore: cast_nullable_to_non_nullable
              as String?,
      score: freezed == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as int?,
      sex: freezed == sex
          ? _value.sex
          : sex // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as bool?,
      tradeStatus: freezed == tradeStatus
          ? _value.tradeStatus
          : tradeStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserDataImpl implements _UserData {
  const _$UserDataImpl(
      {@JsonKey(name: "auth") this.auth,
      @JsonKey(name: "authStatus") this.authStatus,
      @JsonKey(name: "avatar") this.avatar,
      @JsonKey(name: "countryCode") this.countryCode,
      @JsonKey(name: "email") this.email,
      @JsonKey(name: "fromType") this.fromType,
      @JsonKey(name: "id") this.id,
      @JsonKey(name: "idCard") this.idCard,
      @JsonKey(name: "inviteCode") this.inviteCode,
      @JsonKey(name: "isPayment") this.isPayment,
      @JsonKey(name: "level") this.level,
      @JsonKey(name: "mobile") this.mobile,
      @JsonKey(name: "nickname") this.nickname,
      @JsonKey(name: "pid") this.pid,
      @JsonKey(name: "profiles") this.profiles,
      @JsonKey(name: "realName") this.realName,
      @JsonKey(name: "score") this.score,
      @JsonKey(name: "sex") this.sex,
      @JsonKey(name: "status") this.status,
      @JsonKey(name: "tradeStatus") this.tradeStatus,
      @JsonKey(name: "type") this.type});

  factory _$UserDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserDataImplFromJson(json);

  @override
  @JsonKey(name: "auth")
  final bool? auth;
  @override
  @JsonKey(name: "authStatus")
  final int? authStatus;
  @override
  @JsonKey(name: "avatar")
  final String? avatar;
  @override
  @JsonKey(name: "countryCode")
  final String? countryCode;
  @override
  @JsonKey(name: "email")
  final String? email;
  @override
  @JsonKey(name: "fromType")
  final int? fromType;
  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "idCard")
  final String? idCard;
  @override
  @JsonKey(name: "inviteCode")
  final String? inviteCode;
  @override
  @JsonKey(name: "isPayment")
  final bool? isPayment;
  @override
  @JsonKey(name: "level")
  final int? level;
  @override
  @JsonKey(name: "mobile")
  final String? mobile;
  @override
  @JsonKey(name: "nickname")
  final String? nickname;
  @override
  @JsonKey(name: "pid")
  final int? pid;
  @override
  @JsonKey(name: "profiles")
  final String? profiles;
  @override
  @JsonKey(name: "realName")
  final String? realName;
  @override
  @JsonKey(name: "score")
  final int? score;
  @override
  @JsonKey(name: "sex")
  final int? sex;
  @override
  @JsonKey(name: "status")
  final bool? status;
  @override
  @JsonKey(name: "tradeStatus")
  final int? tradeStatus;
  @override
  @JsonKey(name: "type")
  final int? type;

  @override
  String toString() {
    return 'UserData(auth: $auth, authStatus: $authStatus, avatar: $avatar, countryCode: $countryCode, email: $email, fromType: $fromType, id: $id, idCard: $idCard, inviteCode: $inviteCode, isPayment: $isPayment, level: $level, mobile: $mobile, nickname: $nickname, pid: $pid, profiles: $profiles, realName: $realName, score: $score, sex: $sex, status: $status, tradeStatus: $tradeStatus, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserDataImpl &&
            (identical(other.auth, auth) || other.auth == auth) &&
            (identical(other.authStatus, authStatus) ||
                other.authStatus == authStatus) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.fromType, fromType) ||
                other.fromType == fromType) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.idCard, idCard) || other.idCard == idCard) &&
            (identical(other.inviteCode, inviteCode) ||
                other.inviteCode == inviteCode) &&
            (identical(other.isPayment, isPayment) ||
                other.isPayment == isPayment) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.pid, pid) || other.pid == pid) &&
            (identical(other.profiles, profiles) ||
                other.profiles == profiles) &&
            (identical(other.realName, realName) ||
                other.realName == realName) &&
            (identical(other.score, score) || other.score == score) &&
            (identical(other.sex, sex) || other.sex == sex) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.tradeStatus, tradeStatus) ||
                other.tradeStatus == tradeStatus) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        auth,
        authStatus,
        avatar,
        countryCode,
        email,
        fromType,
        id,
        idCard,
        inviteCode,
        isPayment,
        level,
        mobile,
        nickname,
        pid,
        profiles,
        realName,
        score,
        sex,
        status,
        tradeStatus,
        type
      ]);

  /// Create a copy of UserData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserDataImplCopyWith<_$UserDataImpl> get copyWith =>
      __$$UserDataImplCopyWithImpl<_$UserDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserDataImplToJson(
      this,
    );
  }
}

abstract class _UserData implements UserData {
  const factory _UserData(
      {@JsonKey(name: "auth") final bool? auth,
      @JsonKey(name: "authStatus") final int? authStatus,
      @JsonKey(name: "avatar") final String? avatar,
      @JsonKey(name: "countryCode") final String? countryCode,
      @JsonKey(name: "email") final String? email,
      @JsonKey(name: "fromType") final int? fromType,
      @JsonKey(name: "id") final int? id,
      @JsonKey(name: "idCard") final String? idCard,
      @JsonKey(name: "inviteCode") final String? inviteCode,
      @JsonKey(name: "isPayment") final bool? isPayment,
      @JsonKey(name: "level") final int? level,
      @JsonKey(name: "mobile") final String? mobile,
      @JsonKey(name: "nickname") final String? nickname,
      @JsonKey(name: "pid") final int? pid,
      @JsonKey(name: "profiles") final String? profiles,
      @JsonKey(name: "realName") final String? realName,
      @JsonKey(name: "score") final int? score,
      @JsonKey(name: "sex") final int? sex,
      @JsonKey(name: "status") final bool? status,
      @JsonKey(name: "tradeStatus") final int? tradeStatus,
      @JsonKey(name: "type") final int? type}) = _$UserDataImpl;

  factory _UserData.fromJson(Map<String, dynamic> json) =
      _$UserDataImpl.fromJson;

  @override
  @JsonKey(name: "auth")
  bool? get auth;
  @override
  @JsonKey(name: "authStatus")
  int? get authStatus;
  @override
  @JsonKey(name: "avatar")
  String? get avatar;
  @override
  @JsonKey(name: "countryCode")
  String? get countryCode;
  @override
  @JsonKey(name: "email")
  String? get email;
  @override
  @JsonKey(name: "fromType")
  int? get fromType;
  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "idCard")
  String? get idCard;
  @override
  @JsonKey(name: "inviteCode")
  String? get inviteCode;
  @override
  @JsonKey(name: "isPayment")
  bool? get isPayment;
  @override
  @JsonKey(name: "level")
  int? get level;
  @override
  @JsonKey(name: "mobile")
  String? get mobile;
  @override
  @JsonKey(name: "nickname")
  String? get nickname;
  @override
  @JsonKey(name: "pid")
  int? get pid;
  @override
  @JsonKey(name: "profiles")
  String? get profiles;
  @override
  @JsonKey(name: "realName")
  String? get realName;
  @override
  @JsonKey(name: "score")
  int? get score;
  @override
  @JsonKey(name: "sex")
  int? get sex;
  @override
  @JsonKey(name: "status")
  bool? get status;
  @override
  @JsonKey(name: "tradeStatus")
  int? get tradeStatus;
  @override
  @JsonKey(name: "type")
  int? get type;

  /// Create a copy of UserData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserDataImplCopyWith<_$UserDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
