// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LoginResponseImpl _$$LoginResponseImplFromJson(Map<String, dynamic> json) =>
    _$LoginResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : UserData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$LoginResponseImplToJson(_$LoginResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$UserDataImpl _$$UserDataImplFromJson(Map<String, dynamic> json) =>
    _$UserDataImpl(
      auth: json['auth'] as bool?,
      authStatus: (json['authStatus'] as num?)?.toInt(),
      avatar: json['avatar'] as String?,
      countryCode: json['countryCode'] as String?,
      email: json['email'] as String?,
      fromType: (json['fromType'] as num?)?.toInt(),
      id: (json['id'] as num?)?.toInt(),
      idCard: json['idCard'] as String?,
      inviteCode: json['inviteCode'] as String?,
      isPayment: json['isPayment'] as bool?,
      level: (json['level'] as num?)?.toInt(),
      mobile: json['mobile'] as String?,
      nickname: json['nickname'] as String?,
      pid: (json['pid'] as num?)?.toInt(),
      profiles: json['profiles'] as String?,
      realName: json['realName'] as String?,
      score: (json['score'] as num?)?.toInt(),
      sex: (json['sex'] as num?)?.toInt(),
      status: json['status'] as bool?,
      tradeStatus: (json['tradeStatus'] as num?)?.toInt(),
      type: (json['type'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$UserDataImplToJson(_$UserDataImpl instance) =>
    <String, dynamic>{
      'auth': instance.auth,
      'authStatus': instance.authStatus,
      'avatar': instance.avatar,
      'countryCode': instance.countryCode,
      'email': instance.email,
      'fromType': instance.fromType,
      'id': instance.id,
      'idCard': instance.idCard,
      'inviteCode': instance.inviteCode,
      'isPayment': instance.isPayment,
      'level': instance.level,
      'mobile': instance.mobile,
      'nickname': instance.nickname,
      'pid': instance.pid,
      'profiles': instance.profiles,
      'realName': instance.realName,
      'score': instance.score,
      'sex': instance.sex,
      'status': instance.status,
      'tradeStatus': instance.tradeStatus,
      'type': instance.type,
    };
