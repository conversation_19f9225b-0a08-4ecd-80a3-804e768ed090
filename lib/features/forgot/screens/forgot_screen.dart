// forgot_screen.dart
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';

import '../../../core/utils/validators.dart';
import '../../../shared/app/utilities/easy_loading.dart';
import '../../../shared/constants/assets.dart';
import '../../../shared/constants/enums.dart';
import '../../../shared/widgets/app_header.dart';
import '../../../shared/widgets/otp_field.dart';
import '../../../shared/widgets/text_fields/text_field_widget.dart';
import '../../account/logic/otp/otp_cubit.dart';
import '../../account/logic/otp/otp_state.dart';
import '../logic/forgot/forgot_cubit.dart';
import '../logic/forgot/forgot_state.dart';

class ForgotScreen extends StatelessWidget with AppHeaderMixin {
  const ForgotScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        body: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              buildAppHeader(context, height: .40.gsh, showHeaderImage: true),
              _ForgotForm(),
            ],
          ),
        ),
      ),
    );
  }
}

class _ForgotForm extends StatefulWidget {
  @override
  State<_ForgotForm> createState() => _ForgotFormState();
}

class _ForgotFormState extends State<_ForgotForm> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _codeController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  final OtpCubit _otpCubit = OtpCubit();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _phoneController.dispose();
    _codeController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _otpCubit.close();
    super.dispose();
  }

  void _handleSendVerificationCode() {
    // Validate only the phone field
    if (_phoneController.text.isEmpty || Validators.validateMobile(_phoneController.text) != null) {
      _formKey.currentState?.validate();
      return;
    }

    GPEasyLoading.showLoading(message: 'sendingCode'.tr());

    _otpCubit.sendOtp(_phoneController.text, type: OtpType.updatePassword).then((_) {
      GPEasyLoading.dismiss();

      if (_otpCubit.state.sendStatus == DataStatus.success) {
        GPEasyLoading.showSuccess(message: 'verificationCodeSent'.tr());
      } else if (_otpCubit.state.sendStatus == DataStatus.failed) {
        GPEasyLoading.showToast(
          _otpCubit.state.error?.tr() ?? 'failedToSendCode'.tr(),
          bgColor: Colors.red,
        );
      }
    });
  }

  void _handleReset(BuildContext context) {
    // Validate all form fields
    if (!_formKey.currentState!.validate()) {
      return;
    }

    context.read<ForgotCubit>().resetPassword(
          mobile: _phoneController.text,
          password: _passwordController.text,
          smsCode: _codeController.text,
        );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => _otpCubit,
      child: BlocListener<ForgotCubit, ForgotState>(
        listener: (context, state) {
          if (state.status == DataStatus.loading) {
            GPEasyLoading.showLoading(message: 'resetting'.tr());
          }
          if (state.status == DataStatus.success) {
            GPEasyLoading.showSuccess(message: 'resetSuccess'.tr());
            Navigator.pop(context);
          }
          if (state.status == DataStatus.failed) {
            GPEasyLoading.showToast(
              state.error ?? 'resetFailed'.tr(),
              bgColor: Colors.red,
            );
          }
        },
        child: AnimationConfiguration.synchronized(
          duration: const Duration(milliseconds: 800),
          child: SlideAnimation(
            verticalOffset: 100.0,
            child: FadeInAnimation(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: context.theme.cardColor,
                  borderRadius: BorderRadius.circular(40.gr),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.gw),
                  child: AnimationLimiter(
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: AnimationConfiguration.toStaggeredList(
                          duration: const Duration(milliseconds: 600),
                          childAnimationBuilder: (widget) => SlideAnimation(
                            verticalOffset: 50.0,
                            child: FadeInAnimation(
                              child: widget,
                            ),
                          ),
                          children: [
                            28.verticalSpace,
                            Text(
                              'forgotPasswordTitle'.tr(),
                              style: context.textTheme.primary.fs18.w600.copyWith(
                                color: context.theme.primaryColor,
                              ),
                            ),
                            30.verticalSpace,
                            BlocBuilder<OtpCubit, OtpState>(
                              builder: (context, otpState) {
                                return OtpField(
                                  mobileController: _phoneController,
                                  codeController: _codeController,
                                  otpState: otpState,
                                  onSendCode: _handleSendVerificationCode,
                                );
                              },
                            ),
                            15.verticalSpace,
                            TextFieldWidget(
                              hintText: 'registerPasswordHint'.tr(),
                              controller: _passwordController,
                              textInputType: TextInputType.visiblePassword,
                              prefixIcon: SvgPicture.asset(
                                Assets.lockIcon,
                                fit: BoxFit.scaleDown,
                                width: 18.gw,
                                height: 18.gh,
                              ),
                              obscureText: true,
                              passwordIcon: true,
                              validator: (value) => Validators.validatePassword(value),
                            ),
                            15.verticalSpace,
                            TextFieldWidget(
                              hintText: 'registerConfirmPassword'.tr(),
                              controller: _confirmPasswordController,
                              textInputType: TextInputType.visiblePassword,
                              prefixIcon: SvgPicture.asset(
                                Assets.lockIcon,
                                fit: BoxFit.scaleDown,
                                width: 18.gw,
                                height: 18.gh,
                              ),
                              obscureText: true,
                              passwordIcon: true,
                              validator: (value) =>
                                  value != _passwordController.text ? 'passwordsDoNotMatch'.tr() : null,
                            ),
                            24.verticalSpace,
                            AnimationConfiguration.synchronized(
                              duration: const Duration(milliseconds: 300),
                              child: ScaleAnimation(
                                scale: 0.95,
                                child: CommonButton(
                                  title: 'reset'.tr(),
                                  onPressed: () => _handleReset(context),
                                ),
                              ),
                            ),
                            8.verticalSpace,
                            Center(
                              child: AnimationConfiguration.synchronized(
                                duration: const Duration(milliseconds: 300),
                                child: ScaleAnimation(
                                  scale: 0.95,
                                  child: TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    style: TextButton.styleFrom(
                                      foregroundColor: context.theme.primaryColor,
                                      minimumSize: Size.zero,
                                      padding: EdgeInsets.all(8.gr),
                                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                    ),
                                    child: Text("backToLogin".tr()),
                                  ),
                                ),
                              ),
                            ),
                            50.verticalSpace,
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
