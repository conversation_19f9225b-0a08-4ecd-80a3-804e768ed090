// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_services_conversation.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ChatServicesConversation _$ChatServicesConversationFromJson(
    Map<String, dynamic> json) {
  return _ChatServicesConversation.fromJson(json);
}

/// @nodoc
mixin _$ChatServicesConversation {
  String get imAccount => throw _privateConstructorUsedError;
  String get nickname => throw _privateConstructorUsedError;
  String get pImAccount => throw _privateConstructorUsedError;
  String get pNickname => throw _privateConstructorUsedError;

  /// Serializes this ChatServicesConversation to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChatServicesConversation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChatServicesConversationCopyWith<ChatServicesConversation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatServicesConversationCopyWith<$Res> {
  factory $ChatServicesConversationCopyWith(ChatServicesConversation value,
          $Res Function(ChatServicesConversation) then) =
      _$ChatServicesConversationCopyWithImpl<$Res, ChatServicesConversation>;
  @useResult
  $Res call(
      {String imAccount, String nickname, String pImAccount, String pNickname});
}

/// @nodoc
class _$ChatServicesConversationCopyWithImpl<$Res,
        $Val extends ChatServicesConversation>
    implements $ChatServicesConversationCopyWith<$Res> {
  _$ChatServicesConversationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChatServicesConversation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? imAccount = null,
    Object? nickname = null,
    Object? pImAccount = null,
    Object? pNickname = null,
  }) {
    return _then(_value.copyWith(
      imAccount: null == imAccount
          ? _value.imAccount
          : imAccount // ignore: cast_nullable_to_non_nullable
              as String,
      nickname: null == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String,
      pImAccount: null == pImAccount
          ? _value.pImAccount
          : pImAccount // ignore: cast_nullable_to_non_nullable
              as String,
      pNickname: null == pNickname
          ? _value.pNickname
          : pNickname // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChatServicesConversationImplCopyWith<$Res>
    implements $ChatServicesConversationCopyWith<$Res> {
  factory _$$ChatServicesConversationImplCopyWith(
          _$ChatServicesConversationImpl value,
          $Res Function(_$ChatServicesConversationImpl) then) =
      __$$ChatServicesConversationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String imAccount, String nickname, String pImAccount, String pNickname});
}

/// @nodoc
class __$$ChatServicesConversationImplCopyWithImpl<$Res>
    extends _$ChatServicesConversationCopyWithImpl<$Res,
        _$ChatServicesConversationImpl>
    implements _$$ChatServicesConversationImplCopyWith<$Res> {
  __$$ChatServicesConversationImplCopyWithImpl(
      _$ChatServicesConversationImpl _value,
      $Res Function(_$ChatServicesConversationImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChatServicesConversation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? imAccount = null,
    Object? nickname = null,
    Object? pImAccount = null,
    Object? pNickname = null,
  }) {
    return _then(_$ChatServicesConversationImpl(
      imAccount: null == imAccount
          ? _value.imAccount
          : imAccount // ignore: cast_nullable_to_non_nullable
              as String,
      nickname: null == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String,
      pImAccount: null == pImAccount
          ? _value.pImAccount
          : pImAccount // ignore: cast_nullable_to_non_nullable
              as String,
      pNickname: null == pNickname
          ? _value.pNickname
          : pNickname // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChatServicesConversationImpl implements _ChatServicesConversation {
  const _$ChatServicesConversationImpl(
      {this.imAccount = '',
      this.nickname = '',
      this.pImAccount = '',
      this.pNickname = ''});

  factory _$ChatServicesConversationImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChatServicesConversationImplFromJson(json);

  @override
  @JsonKey()
  final String imAccount;
  @override
  @JsonKey()
  final String nickname;
  @override
  @JsonKey()
  final String pImAccount;
  @override
  @JsonKey()
  final String pNickname;

  @override
  String toString() {
    return 'ChatServicesConversation(imAccount: $imAccount, nickname: $nickname, pImAccount: $pImAccount, pNickname: $pNickname)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatServicesConversationImpl &&
            (identical(other.imAccount, imAccount) ||
                other.imAccount == imAccount) &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.pImAccount, pImAccount) ||
                other.pImAccount == pImAccount) &&
            (identical(other.pNickname, pNickname) ||
                other.pNickname == pNickname));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, imAccount, nickname, pImAccount, pNickname);

  /// Create a copy of ChatServicesConversation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatServicesConversationImplCopyWith<_$ChatServicesConversationImpl>
      get copyWith => __$$ChatServicesConversationImplCopyWithImpl<
          _$ChatServicesConversationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChatServicesConversationImplToJson(
      this,
    );
  }
}

abstract class _ChatServicesConversation implements ChatServicesConversation {
  const factory _ChatServicesConversation(
      {final String imAccount,
      final String nickname,
      final String pImAccount,
      final String pNickname}) = _$ChatServicesConversationImpl;

  factory _ChatServicesConversation.fromJson(Map<String, dynamic> json) =
      _$ChatServicesConversationImpl.fromJson;

  @override
  String get imAccount;
  @override
  String get nickname;
  @override
  String get pImAccount;
  @override
  String get pNickname;

  /// Create a copy of ChatServicesConversation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChatServicesConversationImplCopyWith<_$ChatServicesConversationImpl>
      get copyWith => throw _privateConstructorUsedError;
}
