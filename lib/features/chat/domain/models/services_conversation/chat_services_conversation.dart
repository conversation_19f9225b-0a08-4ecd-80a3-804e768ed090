import 'package:freezed_annotation/freezed_annotation.dart';

part 'chat_services_conversation.freezed.dart';
part 'chat_services_conversation.g.dart';

@freezed
class ChatServicesConversation with _$ChatServicesConversation {
  const factory ChatServicesConversation({
    @Default('') String imAccount,
    @Default('') String nickname,
    @Default('') String pImAccount,
    @Default('') String pNickname,
  }) = _ChatServicesConversation;

  factory ChatServicesConversation.fromJson(Map<String, dynamic> json) => _$ChatServicesConversationFromJson(json);
}