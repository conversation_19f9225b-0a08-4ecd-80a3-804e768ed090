import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/third_party_channel_entity_list_entity.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/third_party_channel_entity_list_entity.g.dart';

@JsonSerializable()
class ThirdPartyChannelEntityListEntity {
	List<ThirdPartyChannelEntityListList> list = [];

	ThirdPartyChannelEntityListEntity();

	factory ThirdPartyChannelEntityListEntity.fromJson(Map<String, dynamic> json) => $ThirdPartyChannelEntityListEntityFromJson(json);

	Map<String, dynamic> toJson() => $ThirdPartyChannelEntityListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ThirdPartyChannelEntityListList {
	String currency = "";
	String icon = "";
	List<ThirdPartyChannelEntityListListPayTypeList> payTypeList = [];
	String payWayCode = "";
	String payWayName = "";
	bool recommended = false;

	ThirdPartyChannelEntityListList();

	factory ThirdPartyChannelEntityListList.fromJson(Map<String, dynamic> json) => $ThirdPartyChannelEntityListListFromJson(json);

	Map<String, dynamic> toJson() => $ThirdPartyChannelEntityListListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ThirdPartyChannelEntityListListPayTypeList {
	String amountList = "";
	int amountMaxLimit = 0;
	int amountMinLimit = 0;
	String controllerTips = "";
	int exchangeRate = 0;
	bool fixedAmount = false;
	int payTypeId = 0;
	String payTypeName = "";
	int sort = 0;

	ThirdPartyChannelEntityListListPayTypeList();

	factory ThirdPartyChannelEntityListListPayTypeList.fromJson(Map<String, dynamic> json) => $ThirdPartyChannelEntityListListPayTypeListFromJson(json);

	Map<String, dynamic> toJson() => $ThirdPartyChannelEntityListListPayTypeListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}