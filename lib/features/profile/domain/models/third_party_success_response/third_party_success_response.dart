import 'package:freezed_annotation/freezed_annotation.dart';

part 'third_party_success_response.freezed.dart';
part 'third_party_success_response.g.dart';

@freezed
class ThirdPartySuccessResponse with _$ThirdPartySuccessResponse {
  const factory ThirdPartySuccessResponse({
    @Default(0) double amount,
    @Default('') String currency,
    Map<String, dynamic>? ext,
    @Default('') String orderNo,
    @Default('') String payUrl,
    @Default('') String payWayName,
    @Default('') String tradeTime,
  }) = _ThirdPartySuccessResponse;

  factory ThirdPartySuccessResponse.fromJson(Map<String, dynamic> json) => _$ThirdPartySuccessResponseFromJson(json);
}
