// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'third_party_success_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ThirdPartySuccessResponse _$ThirdPartySuccessResponseFromJson(
    Map<String, dynamic> json) {
  return _ThirdPartySuccessResponse.fromJson(json);
}

/// @nodoc
mixin _$ThirdPartySuccessResponse {
  double get amount => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  Map<String, dynamic>? get ext => throw _privateConstructorUsedError;
  String get orderNo => throw _privateConstructorUsedError;
  String get payUrl => throw _privateConstructorUsedError;
  String get payWayName => throw _privateConstructorUsedError;
  String get tradeTime => throw _privateConstructorUsedError;

  /// Serializes this ThirdPartySuccessResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ThirdPartySuccessResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ThirdPartySuccessResponseCopyWith<ThirdPartySuccessResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ThirdPartySuccessResponseCopyWith<$Res> {
  factory $ThirdPartySuccessResponseCopyWith(ThirdPartySuccessResponse value,
          $Res Function(ThirdPartySuccessResponse) then) =
      _$ThirdPartySuccessResponseCopyWithImpl<$Res, ThirdPartySuccessResponse>;
  @useResult
  $Res call(
      {double amount,
      String currency,
      Map<String, dynamic>? ext,
      String orderNo,
      String payUrl,
      String payWayName,
      String tradeTime});
}

/// @nodoc
class _$ThirdPartySuccessResponseCopyWithImpl<$Res,
        $Val extends ThirdPartySuccessResponse>
    implements $ThirdPartySuccessResponseCopyWith<$Res> {
  _$ThirdPartySuccessResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ThirdPartySuccessResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? currency = null,
    Object? ext = freezed,
    Object? orderNo = null,
    Object? payUrl = null,
    Object? payWayName = null,
    Object? tradeTime = null,
  }) {
    return _then(_value.copyWith(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      ext: freezed == ext
          ? _value.ext
          : ext // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      orderNo: null == orderNo
          ? _value.orderNo
          : orderNo // ignore: cast_nullable_to_non_nullable
              as String,
      payUrl: null == payUrl
          ? _value.payUrl
          : payUrl // ignore: cast_nullable_to_non_nullable
              as String,
      payWayName: null == payWayName
          ? _value.payWayName
          : payWayName // ignore: cast_nullable_to_non_nullable
              as String,
      tradeTime: null == tradeTime
          ? _value.tradeTime
          : tradeTime // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ThirdPartySuccessResponseImplCopyWith<$Res>
    implements $ThirdPartySuccessResponseCopyWith<$Res> {
  factory _$$ThirdPartySuccessResponseImplCopyWith(
          _$ThirdPartySuccessResponseImpl value,
          $Res Function(_$ThirdPartySuccessResponseImpl) then) =
      __$$ThirdPartySuccessResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double amount,
      String currency,
      Map<String, dynamic>? ext,
      String orderNo,
      String payUrl,
      String payWayName,
      String tradeTime});
}

/// @nodoc
class __$$ThirdPartySuccessResponseImplCopyWithImpl<$Res>
    extends _$ThirdPartySuccessResponseCopyWithImpl<$Res,
        _$ThirdPartySuccessResponseImpl>
    implements _$$ThirdPartySuccessResponseImplCopyWith<$Res> {
  __$$ThirdPartySuccessResponseImplCopyWithImpl(
      _$ThirdPartySuccessResponseImpl _value,
      $Res Function(_$ThirdPartySuccessResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of ThirdPartySuccessResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? currency = null,
    Object? ext = freezed,
    Object? orderNo = null,
    Object? payUrl = null,
    Object? payWayName = null,
    Object? tradeTime = null,
  }) {
    return _then(_$ThirdPartySuccessResponseImpl(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      ext: freezed == ext
          ? _value._ext
          : ext // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      orderNo: null == orderNo
          ? _value.orderNo
          : orderNo // ignore: cast_nullable_to_non_nullable
              as String,
      payUrl: null == payUrl
          ? _value.payUrl
          : payUrl // ignore: cast_nullable_to_non_nullable
              as String,
      payWayName: null == payWayName
          ? _value.payWayName
          : payWayName // ignore: cast_nullable_to_non_nullable
              as String,
      tradeTime: null == tradeTime
          ? _value.tradeTime
          : tradeTime // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ThirdPartySuccessResponseImpl implements _ThirdPartySuccessResponse {
  const _$ThirdPartySuccessResponseImpl(
      {this.amount = 0,
      this.currency = '',
      final Map<String, dynamic>? ext,
      this.orderNo = '',
      this.payUrl = '',
      this.payWayName = '',
      this.tradeTime = ''})
      : _ext = ext;

  factory _$ThirdPartySuccessResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ThirdPartySuccessResponseImplFromJson(json);

  @override
  @JsonKey()
  final double amount;
  @override
  @JsonKey()
  final String currency;
  final Map<String, dynamic>? _ext;
  @override
  Map<String, dynamic>? get ext {
    final value = _ext;
    if (value == null) return null;
    if (_ext is EqualUnmodifiableMapView) return _ext;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  @JsonKey()
  final String orderNo;
  @override
  @JsonKey()
  final String payUrl;
  @override
  @JsonKey()
  final String payWayName;
  @override
  @JsonKey()
  final String tradeTime;

  @override
  String toString() {
    return 'ThirdPartySuccessResponse(amount: $amount, currency: $currency, ext: $ext, orderNo: $orderNo, payUrl: $payUrl, payWayName: $payWayName, tradeTime: $tradeTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ThirdPartySuccessResponseImpl &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            const DeepCollectionEquality().equals(other._ext, _ext) &&
            (identical(other.orderNo, orderNo) || other.orderNo == orderNo) &&
            (identical(other.payUrl, payUrl) || other.payUrl == payUrl) &&
            (identical(other.payWayName, payWayName) ||
                other.payWayName == payWayName) &&
            (identical(other.tradeTime, tradeTime) ||
                other.tradeTime == tradeTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      amount,
      currency,
      const DeepCollectionEquality().hash(_ext),
      orderNo,
      payUrl,
      payWayName,
      tradeTime);

  /// Create a copy of ThirdPartySuccessResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ThirdPartySuccessResponseImplCopyWith<_$ThirdPartySuccessResponseImpl>
      get copyWith => __$$ThirdPartySuccessResponseImplCopyWithImpl<
          _$ThirdPartySuccessResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ThirdPartySuccessResponseImplToJson(
      this,
    );
  }
}

abstract class _ThirdPartySuccessResponse implements ThirdPartySuccessResponse {
  const factory _ThirdPartySuccessResponse(
      {final double amount,
      final String currency,
      final Map<String, dynamic>? ext,
      final String orderNo,
      final String payUrl,
      final String payWayName,
      final String tradeTime}) = _$ThirdPartySuccessResponseImpl;

  factory _ThirdPartySuccessResponse.fromJson(Map<String, dynamic> json) =
      _$ThirdPartySuccessResponseImpl.fromJson;

  @override
  double get amount;
  @override
  String get currency;
  @override
  Map<String, dynamic>? get ext;
  @override
  String get orderNo;
  @override
  String get payUrl;
  @override
  String get payWayName;
  @override
  String get tradeTime;

  /// Create a copy of ThirdPartySuccessResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ThirdPartySuccessResponseImplCopyWith<_$ThirdPartySuccessResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
