// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'third_party_channel.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ThirdPartyChannel _$ThirdPartyChannelFromJson(Map<String, dynamic> json) {
  return _ThirdPartyChannel.fromJson(json);
}

/// @nodoc
mixin _$ThirdPartyChannel {
  String get currency => throw _privateConstructorUsedError;
  String get icon => throw _privateConstructorUsedError;
  List<PayType> get payTypeList => throw _privateConstructorUsedError;
  String get payWayCode => throw _privateConstructorUsedError;
  String get payWayName => throw _privateConstructorUsedError;
  bool get recommended => throw _privateConstructorUsedError;

  /// Serializes this ThirdPartyChannel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ThirdPartyChannel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ThirdPartyChannelCopyWith<ThirdPartyChannel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ThirdPartyChannelCopyWith<$Res> {
  factory $ThirdPartyChannelCopyWith(
          ThirdPartyChannel value, $Res Function(ThirdPartyChannel) then) =
      _$ThirdPartyChannelCopyWithImpl<$Res, ThirdPartyChannel>;
  @useResult
  $Res call(
      {String currency,
      String icon,
      List<PayType> payTypeList,
      String payWayCode,
      String payWayName,
      bool recommended});
}

/// @nodoc
class _$ThirdPartyChannelCopyWithImpl<$Res, $Val extends ThirdPartyChannel>
    implements $ThirdPartyChannelCopyWith<$Res> {
  _$ThirdPartyChannelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ThirdPartyChannel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currency = null,
    Object? icon = null,
    Object? payTypeList = null,
    Object? payWayCode = null,
    Object? payWayName = null,
    Object? recommended = null,
  }) {
    return _then(_value.copyWith(
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String,
      payTypeList: null == payTypeList
          ? _value.payTypeList
          : payTypeList // ignore: cast_nullable_to_non_nullable
              as List<PayType>,
      payWayCode: null == payWayCode
          ? _value.payWayCode
          : payWayCode // ignore: cast_nullable_to_non_nullable
              as String,
      payWayName: null == payWayName
          ? _value.payWayName
          : payWayName // ignore: cast_nullable_to_non_nullable
              as String,
      recommended: null == recommended
          ? _value.recommended
          : recommended // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ThirdPartyChannelImplCopyWith<$Res>
    implements $ThirdPartyChannelCopyWith<$Res> {
  factory _$$ThirdPartyChannelImplCopyWith(_$ThirdPartyChannelImpl value,
          $Res Function(_$ThirdPartyChannelImpl) then) =
      __$$ThirdPartyChannelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String currency,
      String icon,
      List<PayType> payTypeList,
      String payWayCode,
      String payWayName,
      bool recommended});
}

/// @nodoc
class __$$ThirdPartyChannelImplCopyWithImpl<$Res>
    extends _$ThirdPartyChannelCopyWithImpl<$Res, _$ThirdPartyChannelImpl>
    implements _$$ThirdPartyChannelImplCopyWith<$Res> {
  __$$ThirdPartyChannelImplCopyWithImpl(_$ThirdPartyChannelImpl _value,
      $Res Function(_$ThirdPartyChannelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ThirdPartyChannel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currency = null,
    Object? icon = null,
    Object? payTypeList = null,
    Object? payWayCode = null,
    Object? payWayName = null,
    Object? recommended = null,
  }) {
    return _then(_$ThirdPartyChannelImpl(
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String,
      payTypeList: null == payTypeList
          ? _value._payTypeList
          : payTypeList // ignore: cast_nullable_to_non_nullable
              as List<PayType>,
      payWayCode: null == payWayCode
          ? _value.payWayCode
          : payWayCode // ignore: cast_nullable_to_non_nullable
              as String,
      payWayName: null == payWayName
          ? _value.payWayName
          : payWayName // ignore: cast_nullable_to_non_nullable
              as String,
      recommended: null == recommended
          ? _value.recommended
          : recommended // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ThirdPartyChannelImpl implements _ThirdPartyChannel {
  const _$ThirdPartyChannelImpl(
      {this.currency = '',
      this.icon = '',
      final List<PayType> payTypeList = const [],
      this.payWayCode = '',
      this.payWayName = '',
      this.recommended = false})
      : _payTypeList = payTypeList;

  factory _$ThirdPartyChannelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ThirdPartyChannelImplFromJson(json);

  @override
  @JsonKey()
  final String currency;
  @override
  @JsonKey()
  final String icon;
  final List<PayType> _payTypeList;
  @override
  @JsonKey()
  List<PayType> get payTypeList {
    if (_payTypeList is EqualUnmodifiableListView) return _payTypeList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_payTypeList);
  }

  @override
  @JsonKey()
  final String payWayCode;
  @override
  @JsonKey()
  final String payWayName;
  @override
  @JsonKey()
  final bool recommended;

  @override
  String toString() {
    return 'ThirdPartyChannel(currency: $currency, icon: $icon, payTypeList: $payTypeList, payWayCode: $payWayCode, payWayName: $payWayName, recommended: $recommended)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ThirdPartyChannelImpl &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            const DeepCollectionEquality()
                .equals(other._payTypeList, _payTypeList) &&
            (identical(other.payWayCode, payWayCode) ||
                other.payWayCode == payWayCode) &&
            (identical(other.payWayName, payWayName) ||
                other.payWayName == payWayName) &&
            (identical(other.recommended, recommended) ||
                other.recommended == recommended));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      currency,
      icon,
      const DeepCollectionEquality().hash(_payTypeList),
      payWayCode,
      payWayName,
      recommended);

  /// Create a copy of ThirdPartyChannel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ThirdPartyChannelImplCopyWith<_$ThirdPartyChannelImpl> get copyWith =>
      __$$ThirdPartyChannelImplCopyWithImpl<_$ThirdPartyChannelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ThirdPartyChannelImplToJson(
      this,
    );
  }
}

abstract class _ThirdPartyChannel implements ThirdPartyChannel {
  const factory _ThirdPartyChannel(
      {final String currency,
      final String icon,
      final List<PayType> payTypeList,
      final String payWayCode,
      final String payWayName,
      final bool recommended}) = _$ThirdPartyChannelImpl;

  factory _ThirdPartyChannel.fromJson(Map<String, dynamic> json) =
      _$ThirdPartyChannelImpl.fromJson;

  @override
  String get currency;
  @override
  String get icon;
  @override
  List<PayType> get payTypeList;
  @override
  String get payWayCode;
  @override
  String get payWayName;
  @override
  bool get recommended;

  /// Create a copy of ThirdPartyChannel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ThirdPartyChannelImplCopyWith<_$ThirdPartyChannelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PayType _$PayTypeFromJson(Map<String, dynamic> json) {
  return _PayType.fromJson(json);
}

/// @nodoc
mixin _$PayType {
  String get amountList => throw _privateConstructorUsedError;
  double get amountMaxLimit => throw _privateConstructorUsedError;
  double get amountMinLimit => throw _privateConstructorUsedError;
  String get controllerTips => throw _privateConstructorUsedError;
  double get exchangeRate => throw _privateConstructorUsedError;
  bool get fixedAmount => throw _privateConstructorUsedError;
  int get payTypeId => throw _privateConstructorUsedError;
  String get payTypeName => throw _privateConstructorUsedError;
  int get sort => throw _privateConstructorUsedError;

  /// Serializes this PayType to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PayType
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PayTypeCopyWith<PayType> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PayTypeCopyWith<$Res> {
  factory $PayTypeCopyWith(PayType value, $Res Function(PayType) then) =
      _$PayTypeCopyWithImpl<$Res, PayType>;
  @useResult
  $Res call(
      {String amountList,
      double amountMaxLimit,
      double amountMinLimit,
      String controllerTips,
      double exchangeRate,
      bool fixedAmount,
      int payTypeId,
      String payTypeName,
      int sort});
}

/// @nodoc
class _$PayTypeCopyWithImpl<$Res, $Val extends PayType>
    implements $PayTypeCopyWith<$Res> {
  _$PayTypeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PayType
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amountList = null,
    Object? amountMaxLimit = null,
    Object? amountMinLimit = null,
    Object? controllerTips = null,
    Object? exchangeRate = null,
    Object? fixedAmount = null,
    Object? payTypeId = null,
    Object? payTypeName = null,
    Object? sort = null,
  }) {
    return _then(_value.copyWith(
      amountList: null == amountList
          ? _value.amountList
          : amountList // ignore: cast_nullable_to_non_nullable
              as String,
      amountMaxLimit: null == amountMaxLimit
          ? _value.amountMaxLimit
          : amountMaxLimit // ignore: cast_nullable_to_non_nullable
              as double,
      amountMinLimit: null == amountMinLimit
          ? _value.amountMinLimit
          : amountMinLimit // ignore: cast_nullable_to_non_nullable
              as double,
      controllerTips: null == controllerTips
          ? _value.controllerTips
          : controllerTips // ignore: cast_nullable_to_non_nullable
              as String,
      exchangeRate: null == exchangeRate
          ? _value.exchangeRate
          : exchangeRate // ignore: cast_nullable_to_non_nullable
              as double,
      fixedAmount: null == fixedAmount
          ? _value.fixedAmount
          : fixedAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      payTypeId: null == payTypeId
          ? _value.payTypeId
          : payTypeId // ignore: cast_nullable_to_non_nullable
              as int,
      payTypeName: null == payTypeName
          ? _value.payTypeName
          : payTypeName // ignore: cast_nullable_to_non_nullable
              as String,
      sort: null == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PayTypeImplCopyWith<$Res> implements $PayTypeCopyWith<$Res> {
  factory _$$PayTypeImplCopyWith(
          _$PayTypeImpl value, $Res Function(_$PayTypeImpl) then) =
      __$$PayTypeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String amountList,
      double amountMaxLimit,
      double amountMinLimit,
      String controllerTips,
      double exchangeRate,
      bool fixedAmount,
      int payTypeId,
      String payTypeName,
      int sort});
}

/// @nodoc
class __$$PayTypeImplCopyWithImpl<$Res>
    extends _$PayTypeCopyWithImpl<$Res, _$PayTypeImpl>
    implements _$$PayTypeImplCopyWith<$Res> {
  __$$PayTypeImplCopyWithImpl(
      _$PayTypeImpl _value, $Res Function(_$PayTypeImpl) _then)
      : super(_value, _then);

  /// Create a copy of PayType
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amountList = null,
    Object? amountMaxLimit = null,
    Object? amountMinLimit = null,
    Object? controllerTips = null,
    Object? exchangeRate = null,
    Object? fixedAmount = null,
    Object? payTypeId = null,
    Object? payTypeName = null,
    Object? sort = null,
  }) {
    return _then(_$PayTypeImpl(
      amountList: null == amountList
          ? _value.amountList
          : amountList // ignore: cast_nullable_to_non_nullable
              as String,
      amountMaxLimit: null == amountMaxLimit
          ? _value.amountMaxLimit
          : amountMaxLimit // ignore: cast_nullable_to_non_nullable
              as double,
      amountMinLimit: null == amountMinLimit
          ? _value.amountMinLimit
          : amountMinLimit // ignore: cast_nullable_to_non_nullable
              as double,
      controllerTips: null == controllerTips
          ? _value.controllerTips
          : controllerTips // ignore: cast_nullable_to_non_nullable
              as String,
      exchangeRate: null == exchangeRate
          ? _value.exchangeRate
          : exchangeRate // ignore: cast_nullable_to_non_nullable
              as double,
      fixedAmount: null == fixedAmount
          ? _value.fixedAmount
          : fixedAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      payTypeId: null == payTypeId
          ? _value.payTypeId
          : payTypeId // ignore: cast_nullable_to_non_nullable
              as int,
      payTypeName: null == payTypeName
          ? _value.payTypeName
          : payTypeName // ignore: cast_nullable_to_non_nullable
              as String,
      sort: null == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PayTypeImpl implements _PayType {
  const _$PayTypeImpl(
      {this.amountList = '',
      this.amountMaxLimit = 0,
      this.amountMinLimit = 0,
      this.controllerTips = '',
      this.exchangeRate = 0,
      this.fixedAmount = false,
      this.payTypeId = 0,
      this.payTypeName = '',
      this.sort = 0});

  factory _$PayTypeImpl.fromJson(Map<String, dynamic> json) =>
      _$$PayTypeImplFromJson(json);

  @override
  @JsonKey()
  final String amountList;
  @override
  @JsonKey()
  final double amountMaxLimit;
  @override
  @JsonKey()
  final double amountMinLimit;
  @override
  @JsonKey()
  final String controllerTips;
  @override
  @JsonKey()
  final double exchangeRate;
  @override
  @JsonKey()
  final bool fixedAmount;
  @override
  @JsonKey()
  final int payTypeId;
  @override
  @JsonKey()
  final String payTypeName;
  @override
  @JsonKey()
  final int sort;

  @override
  String toString() {
    return 'PayType(amountList: $amountList, amountMaxLimit: $amountMaxLimit, amountMinLimit: $amountMinLimit, controllerTips: $controllerTips, exchangeRate: $exchangeRate, fixedAmount: $fixedAmount, payTypeId: $payTypeId, payTypeName: $payTypeName, sort: $sort)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PayTypeImpl &&
            (identical(other.amountList, amountList) ||
                other.amountList == amountList) &&
            (identical(other.amountMaxLimit, amountMaxLimit) ||
                other.amountMaxLimit == amountMaxLimit) &&
            (identical(other.amountMinLimit, amountMinLimit) ||
                other.amountMinLimit == amountMinLimit) &&
            (identical(other.controllerTips, controllerTips) ||
                other.controllerTips == controllerTips) &&
            (identical(other.exchangeRate, exchangeRate) ||
                other.exchangeRate == exchangeRate) &&
            (identical(other.fixedAmount, fixedAmount) ||
                other.fixedAmount == fixedAmount) &&
            (identical(other.payTypeId, payTypeId) ||
                other.payTypeId == payTypeId) &&
            (identical(other.payTypeName, payTypeName) ||
                other.payTypeName == payTypeName) &&
            (identical(other.sort, sort) || other.sort == sort));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      amountList,
      amountMaxLimit,
      amountMinLimit,
      controllerTips,
      exchangeRate,
      fixedAmount,
      payTypeId,
      payTypeName,
      sort);

  /// Create a copy of PayType
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PayTypeImplCopyWith<_$PayTypeImpl> get copyWith =>
      __$$PayTypeImplCopyWithImpl<_$PayTypeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PayTypeImplToJson(
      this,
    );
  }
}

abstract class _PayType implements PayType {
  const factory _PayType(
      {final String amountList,
      final double amountMaxLimit,
      final double amountMinLimit,
      final String controllerTips,
      final double exchangeRate,
      final bool fixedAmount,
      final int payTypeId,
      final String payTypeName,
      final int sort}) = _$PayTypeImpl;

  factory _PayType.fromJson(Map<String, dynamic> json) = _$PayTypeImpl.fromJson;

  @override
  String get amountList;
  @override
  double get amountMaxLimit;
  @override
  double get amountMinLimit;
  @override
  String get controllerTips;
  @override
  double get exchangeRate;
  @override
  bool get fixedAmount;
  @override
  int get payTypeId;
  @override
  String get payTypeName;
  @override
  int get sort;

  /// Create a copy of PayType
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PayTypeImplCopyWith<_$PayTypeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
