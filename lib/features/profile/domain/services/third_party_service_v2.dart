import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';
import 'package:gp_stock_app/features/profile/domain/models/third_party_channel_entity_list_entity.dart';

class ThirdPartyServiceV2 {
  static Future<ThirdPartyChannelEntityListEntity?> getThirdPartyChannelList(int? type) async {
    final Response response = await NetworkProvider().get(
      ApiEndpoints.thirdPartyChannelList,
      isAuthRequired: true,
      queryParameters: {
        if (type != null) 'type': type,
      },
    );
    final responseModel = NetworkHelper.mappingResponseData<ThirdPartyChannelEntityListEntity>(response);
    return responseModel.data;
  }
}
