import 'dart:io';

import 'package:gp_stock_app/features/profile/domain/models/app_info/app_info_model.dart';
import 'package:gp_stock_app/features/sign_in/domain/models/login/login_response.dart';

import '../../../../core/api/network/models/result.dart';
import '../../../../shared/constants/enums.dart';
import '../models/auth_n/file/file_upload.dart';
import '../models/auth_n/info/auth_n_info.dart';

abstract class ProfileRepository {
  const ProfileRepository();
  Future<ResponseResult<bool>> authApply({
    required String? certificateBack,
    required String? certificateFront,
    required int? certificateType,
    required String? idCard,
    required String withdrawPassword,
    required String? realName,
    required String? bankMobile,
    required String? bankCardNo,
  });
  Future<ResponseResult<FileUpload>> fileUpload({
    required File? file,
    required String? type,
  });
  Future<ResponseResult<AuthNInfo>> authNInfo();

  Future<ResponseResult<bool>> updateUserInfo({
    String? avatar,
    String? nickname,
    int? sex,
    String? email,
    String? phoneNo,
  });

  Future<ResponseResult<bool>> changePassword({
    String? mobile,
    String? password,
    required String newPassword,
    String? smsCode,
    required String verifyType,
    PasswordType passwordType,
  });

  Future<ResponseResult<bool>> updateMobile({
    required String newMobile,
    required String smsCode,
  });

  Future<ResponseResult<UserData>> getUserInfo();

  Future<ResponseResult<List<AppInfoModel>>> getAppInfoList();
}
