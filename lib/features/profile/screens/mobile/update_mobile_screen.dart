import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/validators.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';

import '../../../../shared/app/utilities/easy_loading.dart';
import '../../../../shared/widgets/otp_field.dart';
import '../../../account/logic/otp/otp_cubit.dart';
import '../../../account/logic/otp/otp_state.dart';
import '../../logic/profile/profile_cubit.dart';
import '../../widgets/settings/settings_appbar.dart';

class UpdateMobileScreen extends StatefulWidget {
  const UpdateMobileScreen({super.key});

  @override
  State<UpdateMobileScreen> createState() => _UpdateMobileScreenState();
}

class _UpdateMobileScreenState extends State<UpdateMobileScreen> {
  final TextEditingController _newMobileController = TextEditingController();
  final TextEditingController _verificationCodeController = TextEditingController();
  bool _isFormValid = false;
  final OtpCubit _otpCubit = OtpCubit();

  @override
  void initState() {
    super.initState();
    _newMobileController.addListener(_validateForm);
    _verificationCodeController.addListener(_validateForm);
  }

  void _validateForm() {
    final isNewMobileValid = Validators.isMobileValid(_newMobileController.text);
    final isCodeValid = _verificationCodeController.text.length >= 4;

    setState(() {
      _isFormValid = isNewMobileValid && isCodeValid;
    });
  }

  Future<void> _handleSendVerificationCode() async {
    if (!Validators.isMobileValid(_newMobileController.text)) {
      final errorColor = context.colorTheme.stockRed;
      GPEasyLoading.showToast(
        'pleaseEnterValidMobile'.tr(),
        bgColor: errorColor,
      );
      return;
    }

    // Store the color for later use to avoid BuildContext across async gap
    final errorColor = context.colorTheme.stockRed;

    GPEasyLoading.showLoading(message: 'sendingCode'.tr());
    _otpCubit.sendOtp(_newMobileController.text, type: OtpType.updateMobile).then((_) {
      if (!mounted) return;

      GPEasyLoading.dismiss();

      if (_otpCubit.state.sendStatus == DataStatus.success) {
        GPEasyLoading.showSuccess(message: 'verificationCodeSent'.tr());
      } else if (_otpCubit.state.sendStatus == DataStatus.failed) {
        GPEasyLoading.showToast(
          _otpCubit.state.error ?? 'failedToSendCode'.tr(),
          bgColor: errorColor,
        );
      }
    });
  }

  Future<void> _handleSubmit() async {
    if (!_isFormValid) return;

    GPEasyLoading.showLoading();
    final success = await context.read<ProfileCubit>().updateMobile(
          newMobile: _newMobileController.text,
          smsCode: _verificationCodeController.text,
        );

    GPEasyLoading.dismiss();

    if (success && mounted) {
      GPEasyLoading.showSuccess(message: 'mobileUpdatedSuccessfully'.tr());
      Navigator.pop(context);
    } else if (mounted) {
      final error = context.read<ProfileCubit>().state.error;
      final errorColor = context.colorTheme.stockRed;
      GPEasyLoading.showToast(error ?? 'updateFailed'.tr(), bgColor: errorColor);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => _otpCubit,
      child: Scaffold(
        backgroundColor: context.theme.scaffoldBackgroundColor,
        appBar: SettingsAppBar(title: 'updateMobile'.tr()),
        body: SafeArea(
          child: ListView(
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            children: [
              24.verticalSpace,
              _buildMobileSection(context),
              24.verticalSpace,
              CustomMaterialButton(
                buttonText: 'submit'.tr(),
                onPressed: _isFormValid ? _handleSubmit : null,
                isEnabled: _isFormValid,
                height: 48.gh,
                borderRadius: 8.gr,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMobileSection(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.gr),
        border: Border.all(color: context.theme.dividerColor),
        color: context.theme.cardColor,
      ),
      padding: EdgeInsets.all(16.gr),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 80.gw,
                child: Text(
                  '${'newMobile'.tr()} *',
                  style: context.textTheme.stockRed.w500,
                ),
              ),
              Expanded(
                child: TextFieldWidget(
                  controller: _newMobileController,
                  hintText: 'enterNewMobile'.tr(),
                  textInputType: TextInputType.phone,
                  fillColor: Colors.transparent,
                  borderType: TextFieldBorderType.none,
                ),
              ),
            ],
          ),
          Divider(color: context.theme.dividerColor),
          BlocBuilder<OtpCubit, OtpState>(
            builder: (context, otpState) {
              return OtpField(
                mobileController: _newMobileController,
                codeController: _verificationCodeController,
                otpState: otpState,
                onSendCode: _handleSendVerificationCode,
                readOnlyMobile: false,
              );
            },
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _newMobileController.dispose();
    _verificationCodeController.dispose();
    _otpCubit.close();
    super.dispose();
  }
}
