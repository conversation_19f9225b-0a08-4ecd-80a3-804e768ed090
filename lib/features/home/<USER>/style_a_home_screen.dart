import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/utils/app_navigation_handler.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/home/<USER>/style_a_home_banner.dart';
import 'package:gp_stock_app/features/home/<USER>/style_a_home_market_tab.dart';
import 'package:gp_stock_app/features/home/<USER>/style_a_home_marquee_text.dart';
import 'package:gp_stock_app/features/home/<USER>/style_a_home_menu.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';

import '../../account/logic/account/account_cubit.dart';
import '../../activity/logic/activity/activity_cubit.dart';
import '../../market/logic/market/market_cubit.dart';
import '../../notifications/logic/notifications/notifications_cubit.dart';
import '../logic/home/<USER>';
import '../logic/news/news_cubit.dart';
import '../widgets/news_and_events.dart';

class StyleAHomeScreen extends StatefulWidget {
  const StyleAHomeScreen({super.key});

  @override
  State<StyleAHomeScreen> createState() => _StyleAHomeScreenState();
}

class _StyleAHomeScreenState extends State<StyleAHomeScreen> with WidgetsBindingObserver {
  int _appLifecycleStatePausedTimeSecond = 0;

  @override
  void initState() {
    super.initState();
    getIt<IndexTradeCubit>().subscribeToTimeline();
    WidgetsBinding.instance.addObserver(this);
  }

  Future<void> _initialFunction() async {
    final currentContext = context;
    if (!currentContext.mounted) return;
    context.read<HomeCubit>().getBannerList();
    context.read<AccountCubit>().getContractSummary();
    context.read<MarketCubit>().fetchTableData(sortType: 1, order: 'DESC', isHome: true);
    context.read<NotificationsCubit>().getNotificationCount();
    context.read<MarketCubit>().fetchTableData(sortType: 0, order: 'DESC', isHome: true);
    context.read<ActivityCubit>().getTasks();
    context.read<NewsCubit>().getNews();
  }

  @override
  void dispose() {
    getIt<IndexTradeCubit>().unsubscribeFromTimeline();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    int now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    if (state == AppLifecycleState.resumed && (now - _appLifecycleStatePausedTimeSecond) > 30) {
      getIt<IndexTradeCubit>().reloadTimeline();
    } else if (state == AppLifecycleState.paused) {
      _appLifecycleStatePausedTimeSecond = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator.adaptive(
        onRefresh: () async => await _initialFunction(),
        child: AnimationLimiter(
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: 10.gw,
            ),
            child: ListView(
              children: AnimationConfiguration.toStaggeredList(
                duration: const Duration(milliseconds: 300),
                childAnimationBuilder: (widget) => SlideAnimation(
                  verticalOffset: 50.0,
                  child: FadeInAnimation(
                    child: widget,
                  ),
                ),
                children: [
                  10.verticalSpace,
                  BlocBuilder<HomeCubit, HomeState>(builder: (context, state) {
                    return StyleAHomeBanner(
                      dataStatus: state.bannerFetchStatus,
                      list: state.bannerData ?? [],
                      onPressed: (idx) {
                        final banner = state.bannerData![idx];
                        AppNavigationHandler.handleNavigation(context,
                            jumpType: banner.jumpType, jumpUrl: banner.jumpUrl);
                      },
                    );
                  }),
                  12.verticalSpace,
                  StyleAHomeMenu(),
                  12.verticalSpace,
                  StyleAHomeMarqueeText(),
                  12.verticalSpace,
                  StyleAHomeMarketTab(),
                  12.verticalSpace,
                  NewsAndEvents(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
