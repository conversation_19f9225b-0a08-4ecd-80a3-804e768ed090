import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_update.freezed.dart';
part 'app_update.g.dart';

@freezed
class AppUpdate with _$AppUpdate {
  const factory AppUpdate({
    @<PERSON><PERSON><PERSON><PERSON>(name: 'createTime') @Default('') String createTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'creator') @Default('') String creator,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'content') @Default('') String content,
    @<PERSON>son<PERSON><PERSON>(name: 'id') @Default(0) int id,
    @Json<PERSON><PERSON>(name: 'isForce') @Default(0) int isForce,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'siteId') @Default(0) int siteId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'status') @Default(1) int status,
    @<PERSON>son<PERSON>ey(name: 'type') @Default(0) int type,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updateTime') @Default('') String updateTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updater') @Default('') String updater,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'url') @Default('') String url,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'version') @Default('') String version,
  }) = _AppUpdate;

  factory AppUpdate.fromJson(Map<String, dynamic> json) =>
      _$AppUpdateFromJson(json);
}
