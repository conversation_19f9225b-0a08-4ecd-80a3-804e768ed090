// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_update.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AppUpdate _$AppUpdateFromJson(Map<String, dynamic> json) {
  return _AppUpdate.fromJson(json);
}

/// @nodoc
mixin _$AppUpdate {
  @JsonKey(name: 'createTime')
  String get createTime => throw _privateConstructorUsedError;
  @JsonKey(name: 'creator')
  String get creator => throw _privateConstructorUsedError;
  @JsonKey(name: 'content')
  String get content => throw _privateConstructorUsedError;
  @JsonKey(name: 'id')
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'isForce')
  int get isForce => throw _privateConstructorUsedError;
  @JsonKey(name: 'siteId')
  int get siteId => throw _privateConstructorUsedError;
  @JsonKey(name: 'status')
  int get status => throw _privateConstructorUsedError;
  @JsonKey(name: 'type')
  int get type => throw _privateConstructorUsedError;
  @JsonKey(name: 'updateTime')
  String get updateTime => throw _privateConstructorUsedError;
  @JsonKey(name: 'updater')
  String get updater => throw _privateConstructorUsedError;
  @JsonKey(name: 'url')
  String get url => throw _privateConstructorUsedError;
  @JsonKey(name: 'version')
  String get version => throw _privateConstructorUsedError;

  /// Serializes this AppUpdate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppUpdateCopyWith<AppUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppUpdateCopyWith<$Res> {
  factory $AppUpdateCopyWith(AppUpdate value, $Res Function(AppUpdate) then) =
      _$AppUpdateCopyWithImpl<$Res, AppUpdate>;
  @useResult
  $Res call(
      {@JsonKey(name: 'createTime') String createTime,
      @JsonKey(name: 'creator') String creator,
      @JsonKey(name: 'content') String content,
      @JsonKey(name: 'id') int id,
      @JsonKey(name: 'isForce') int isForce,
      @JsonKey(name: 'siteId') int siteId,
      @JsonKey(name: 'status') int status,
      @JsonKey(name: 'type') int type,
      @JsonKey(name: 'updateTime') String updateTime,
      @JsonKey(name: 'updater') String updater,
      @JsonKey(name: 'url') String url,
      @JsonKey(name: 'version') String version});
}

/// @nodoc
class _$AppUpdateCopyWithImpl<$Res, $Val extends AppUpdate>
    implements $AppUpdateCopyWith<$Res> {
  _$AppUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createTime = null,
    Object? creator = null,
    Object? content = null,
    Object? id = null,
    Object? isForce = null,
    Object? siteId = null,
    Object? status = null,
    Object? type = null,
    Object? updateTime = null,
    Object? updater = null,
    Object? url = null,
    Object? version = null,
  }) {
    return _then(_value.copyWith(
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      creator: null == creator
          ? _value.creator
          : creator // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      isForce: null == isForce
          ? _value.isForce
          : isForce // ignore: cast_nullable_to_non_nullable
              as int,
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      updateTime: null == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String,
      updater: null == updater
          ? _value.updater
          : updater // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppUpdateImplCopyWith<$Res>
    implements $AppUpdateCopyWith<$Res> {
  factory _$$AppUpdateImplCopyWith(
          _$AppUpdateImpl value, $Res Function(_$AppUpdateImpl) then) =
      __$$AppUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'createTime') String createTime,
      @JsonKey(name: 'creator') String creator,
      @JsonKey(name: 'content') String content,
      @JsonKey(name: 'id') int id,
      @JsonKey(name: 'isForce') int isForce,
      @JsonKey(name: 'siteId') int siteId,
      @JsonKey(name: 'status') int status,
      @JsonKey(name: 'type') int type,
      @JsonKey(name: 'updateTime') String updateTime,
      @JsonKey(name: 'updater') String updater,
      @JsonKey(name: 'url') String url,
      @JsonKey(name: 'version') String version});
}

/// @nodoc
class __$$AppUpdateImplCopyWithImpl<$Res>
    extends _$AppUpdateCopyWithImpl<$Res, _$AppUpdateImpl>
    implements _$$AppUpdateImplCopyWith<$Res> {
  __$$AppUpdateImplCopyWithImpl(
      _$AppUpdateImpl _value, $Res Function(_$AppUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createTime = null,
    Object? creator = null,
    Object? content = null,
    Object? id = null,
    Object? isForce = null,
    Object? siteId = null,
    Object? status = null,
    Object? type = null,
    Object? updateTime = null,
    Object? updater = null,
    Object? url = null,
    Object? version = null,
  }) {
    return _then(_$AppUpdateImpl(
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      creator: null == creator
          ? _value.creator
          : creator // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      isForce: null == isForce
          ? _value.isForce
          : isForce // ignore: cast_nullable_to_non_nullable
              as int,
      siteId: null == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      updateTime: null == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String,
      updater: null == updater
          ? _value.updater
          : updater // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppUpdateImpl implements _AppUpdate {
  const _$AppUpdateImpl(
      {@JsonKey(name: 'createTime') this.createTime = '',
      @JsonKey(name: 'creator') this.creator = '',
      @JsonKey(name: 'content') this.content = '',
      @JsonKey(name: 'id') this.id = 0,
      @JsonKey(name: 'isForce') this.isForce = 0,
      @JsonKey(name: 'siteId') this.siteId = 0,
      @JsonKey(name: 'status') this.status = 1,
      @JsonKey(name: 'type') this.type = 0,
      @JsonKey(name: 'updateTime') this.updateTime = '',
      @JsonKey(name: 'updater') this.updater = '',
      @JsonKey(name: 'url') this.url = '',
      @JsonKey(name: 'version') this.version = ''});

  factory _$AppUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppUpdateImplFromJson(json);

  @override
  @JsonKey(name: 'createTime')
  final String createTime;
  @override
  @JsonKey(name: 'creator')
  final String creator;
  @override
  @JsonKey(name: 'content')
  final String content;
  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'isForce')
  final int isForce;
  @override
  @JsonKey(name: 'siteId')
  final int siteId;
  @override
  @JsonKey(name: 'status')
  final int status;
  @override
  @JsonKey(name: 'type')
  final int type;
  @override
  @JsonKey(name: 'updateTime')
  final String updateTime;
  @override
  @JsonKey(name: 'updater')
  final String updater;
  @override
  @JsonKey(name: 'url')
  final String url;
  @override
  @JsonKey(name: 'version')
  final String version;

  @override
  String toString() {
    return 'AppUpdate(createTime: $createTime, creator: $creator, content: $content, id: $id, isForce: $isForce, siteId: $siteId, status: $status, type: $type, updateTime: $updateTime, updater: $updater, url: $url, version: $version)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppUpdateImpl &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.creator, creator) || other.creator == creator) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isForce, isForce) || other.isForce == isForce) &&
            (identical(other.siteId, siteId) || other.siteId == siteId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            (identical(other.updater, updater) || other.updater == updater) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.version, version) || other.version == version));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, createTime, creator, content, id,
      isForce, siteId, status, type, updateTime, updater, url, version);

  /// Create a copy of AppUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppUpdateImplCopyWith<_$AppUpdateImpl> get copyWith =>
      __$$AppUpdateImplCopyWithImpl<_$AppUpdateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppUpdateImplToJson(
      this,
    );
  }
}

abstract class _AppUpdate implements AppUpdate {
  const factory _AppUpdate(
      {@JsonKey(name: 'createTime') final String createTime,
      @JsonKey(name: 'creator') final String creator,
      @JsonKey(name: 'content') final String content,
      @JsonKey(name: 'id') final int id,
      @JsonKey(name: 'isForce') final int isForce,
      @JsonKey(name: 'siteId') final int siteId,
      @JsonKey(name: 'status') final int status,
      @JsonKey(name: 'type') final int type,
      @JsonKey(name: 'updateTime') final String updateTime,
      @JsonKey(name: 'updater') final String updater,
      @JsonKey(name: 'url') final String url,
      @JsonKey(name: 'version') final String version}) = _$AppUpdateImpl;

  factory _AppUpdate.fromJson(Map<String, dynamic> json) =
      _$AppUpdateImpl.fromJson;

  @override
  @JsonKey(name: 'createTime')
  String get createTime;
  @override
  @JsonKey(name: 'creator')
  String get creator;
  @override
  @JsonKey(name: 'content')
  String get content;
  @override
  @JsonKey(name: 'id')
  int get id;
  @override
  @JsonKey(name: 'isForce')
  int get isForce;
  @override
  @JsonKey(name: 'siteId')
  int get siteId;
  @override
  @JsonKey(name: 'status')
  int get status;
  @override
  @JsonKey(name: 'type')
  int get type;
  @override
  @JsonKey(name: 'updateTime')
  String get updateTime;
  @override
  @JsonKey(name: 'updater')
  String get updater;
  @override
  @JsonKey(name: 'url')
  String get url;
  @override
  @JsonKey(name: 'version')
  String get version;

  /// Create a copy of AppUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppUpdateImplCopyWith<_$AppUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
