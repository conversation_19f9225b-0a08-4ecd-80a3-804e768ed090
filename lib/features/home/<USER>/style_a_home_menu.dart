import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../shared/app/utilities/easy_loading.dart';
import '../../../shared/logic/sys_settings/sys_settings_cubit.dart';

class StyleAHomeMenu extends StatefulWidget {
  const StyleAHomeMenu({super.key});

  @override
  State<StyleAHomeMenu> createState() => _StyleAHomeMenuState();
}

class _StyleAHomeMenuState extends State<StyleAHomeMenu> with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _animations = List.generate(
      4,
      (index) => CurvedAnimation(
        parent: _controller,
        curve: Interval(
          index * 0.2,
          0.6 + index * 0.2,
          curve: Curves.easeOut,
        ),
      ),
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(4),
        boxShadow: const [
          BoxShadow(
            color: Color(0x0F354677),
            offset: Offset(0, 4),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildMenuButton(
            context,
            animation: _animations[0],
            icon: 'assets/svg/home/<USER>',
            title: 'homeH5Title',
            onTap: () => context.verifyAuth(() => Navigator.pushNamed(context, routeAboutUs)),
          ),
          5.horizontalSpace,
          _buildMenuButton(
            context,
            animation: _animations[1],
            icon: 'assets/svg/home/<USER>',
            title: 'aiAnalysis',
            onTap: () => context.verifyAuth(() => Navigator.pushNamed(context, routeAIChat)),
          ),
          5.horizontalSpace,
          _buildMenuButton(
            context,
            animation: _animations[1],
            icon: 'assets/svg/home/<USER>',
            title: 'homeH2Title',
            onTap: () => context.verifyAuth(() => Navigator.pushNamed(context, routeMissionCenter)),
          ),
          5.horizontalSpace,
          _buildMenuButton(
            context,
            animation: _animations[2],
            icon: 'assets/svg/home/<USER>',
            title: 'homeH4Title',
            onTap: () => _launchServiceUrl(context),
          ),
        ],
      ),
    );
  }

  void _launchServiceUrl(BuildContext context) {
    context.verifyAuth(() {
      final state = context.read<SysSettingsCubit>().state;
      state.maybeWhen(
        loaded: (sysSettings) {
          final serviceUrl = sysSettings.service;
          if (serviceUrl != null && serviceUrl.isNotEmpty) {
            launchUrl(Uri.parse(serviceUrl), mode: LaunchMode.inAppBrowserView);
          } else {
            GPEasyLoading.showToast('something_went_wrong'.tr());
          }
        },
        orElse: () => GPEasyLoading.showToast('something_went_wrong'.tr()),
      );
    });
  }

  Widget _buildMenuButton(
    BuildContext context, {
    required Animation<double> animation,
    required String icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return FadeTransition(
      opacity: animation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(-0.5, 0),
          end: Offset.zero,
        ).animate(animation),
        child: Bounceable(
          onTap: onTap,
          child: Column(
            children: [
              IconButton(
                iconSize: 40.gr,
                style: IconButton.styleFrom(backgroundColor: context.theme.primaryColorLight.withValues(alpha: 0.1)),
                icon: IconHelper.loadAsset(
                  icon,
                  width: 40.gw,
                  height: 40.gh,
                  shouldEnableThemeGradient: true,
                ),
                onPressed: onTap,
              ),
              5.verticalSpace,
              Text(
                title.tr(),
                style: context.textTheme.active.fs12,
              )
            ],
          ),
        ),
      ),
    );
  }
}
