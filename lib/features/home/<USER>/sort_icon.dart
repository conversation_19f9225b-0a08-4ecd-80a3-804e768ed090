import 'package:flutter/widgets.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class SortIcon extends StatelessWidget {
  final SortType? sortValue;
  const SortIcon({super.key, this.sortValue});

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      _getSortIcon(),
      width: 14.gw,
      height: 14.gh,
    );
  }

  String _getSortIcon() => sortValue == null
      ? Assets.sortRestIcon
      : sortValue == SortType.ASC
          ? Assets.sortUpIcon
          : Assets.sortDownIcon;
}
