import 'package:flutter/material.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/home/<USER>/home_screen.dart';
import 'package:gp_stock_app/features/home/<USER>/style_a_home_screen.dart';

class HomeScreenEnter extends StatelessWidget {
  const HomeScreenEnter({super.key});

  @override
  Widget build(BuildContext context) {
    final errorWidget = Container(color: Colors.red, width: 1.gsw, height: 1.gsh);
    switch (AppConfig.instance.skinStyle) {
      case AppSkinStyle.kGP:
        switch (AppConfig.instance.colorSchemeStyle) {
          case ColorSchemeStyle.kDefault:
            return HomeScreen();
          case ColorSchemeStyle.kGolden:
          case ColorSchemeStyle.kOrange:
            return errorWidget;
        }
      case AppSkinStyle.kTemplateA:
        switch (AppConfig.instance.colorSchemeStyle) {
          case ColorSchemeStyle.kDefault:
            return StyleAHomeScreen();
          case ColorSchemeStyle.kGolden:
          case ColorSchemeStyle.kOrange:
            return errorWidget;
        }
      case AppSkinStyle.kTemplateB:
      case AppSkinStyle.kTemplateC:
      case AppSkinStyle.kTemplateD:
        return errorWidget;
    }
  }
}
