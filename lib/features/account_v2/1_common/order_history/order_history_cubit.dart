import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/contract.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/utils/extension/future_list_extensions.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/market_category_state.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import 'order_history_state.dart';

class OrderHistoryCubit extends Cubit<OrderHistoryState> {
  OrderHistoryCubit({MarketCategory? category, int? contractId}) : super(OrderHistoryState( category: category, contractId: contractId)) {
    for (var e in state.details.entries) {
      fetchMarketOrderList(type: e.key  , orderListState: e.value);
    }
  }

  CancelToken? _tradeCancelToken;
  CancelToken? _orderCancelToken;

  void updateSelIndex(int index) {
    emit(state.copyWith(selectedIndex: index));
  }

  /// 拉取订单列表：当前持仓、委托明细、成交明细
  /// Fetch market order list
  Future<void> fetchMarketOrderList({
    required OrderType type,
    required OrderListState orderListState,
    bool isLoadMore = false,
    bool isPolling = false,
  }) async {
    if (orderListState.status == DataStatus.loading) return;
    final newOrderListState = orderListState.copyWith(status: DataStatus.loading);
    updateOrderListState(type, newOrderListState);
    int page = 1;
    int pageSize = isPolling ? 100 : 20;
    if (isLoadMore) page = orderListState.page + 1;

    CancelToken? cancelToken = _resetCancelToken(type);

    final result = await ContractApi.getOrderList(
        page: page,
        pageSize: pageSize,
        commentAssetId: getIt<UserCubit>().state.accountInfo?.assetId.toString(),
        contractId: state.contractId,
        status: type == OrderType.trades ? 2 : null,
        dataType: state.category?.code,
        cancelToken: cancelToken);
    if (result == null) {
      final newOrderListState = orderListState.copyWith(status: DataStatus.failed, page: page, hasMoreData: true);
      updateOrderListState(type, newOrderListState);

      return;
    }
    final data = result.records;
    final records = isLoadMore && !isPolling ? orderListState.records.mergeDedupKeepLast(data, (e) => e.id) : data;
    final tmp = newOrderListState.copyWith(
      records: records,
      status: DataStatus.success,
      page: page,
      hasMoreData: data.length >= pageSize,
      isInitialLoad: false,
    );
    updateOrderListState(type, tmp);
  }

  CancelToken? _resetCancelToken(OrderType type) {
    switch (type) {
      case OrderType.trades:
        _tradeCancelToken?.cancel();
        return _tradeCancelToken = CancelToken();
      case OrderType.order:
        _orderCancelToken?.cancel();
        return _orderCancelToken = CancelToken();
      default:
        return null;
    }
  }

  /// 更新当前主Tab下某个明细类型的数据列表
  /// Update the data list for a specific detail type under the current main tab
  /// [type]：明细类型（如持仓、成交、委托）/ Detail type (e.g. holdings, trades, entrusts)
  /// [list]：新的数据列表 / New data list
  void updateOrderListState(OrderType type, OrderListState orderListState) {
    emit(state.updateDetail(type, orderListState));
  }
}
