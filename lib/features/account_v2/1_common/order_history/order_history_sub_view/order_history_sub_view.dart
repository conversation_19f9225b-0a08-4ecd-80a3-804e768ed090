import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/market_category_state.dart';
import 'package:gp_stock_app/features/account_v2/spot/sub_screen/widgets/list_view_cell/account_entrust_cell.dart';
import 'package:gp_stock_app/features/account_v2/spot/sub_screen/widgets/list_view_cell/account_trade_cell.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/pagination/common_refresher.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class OrderHistorySubView extends StatelessWidget {
  final OrderType type;
  final OrderListState orderListState;
  final Function(bool isLoadMore) onFetch;

  OrderHistorySubView({
    super.key,
    required this.type,
    required this.orderListState,
    required this.onFetch,
  });

  final RefreshController _refreshController = RefreshController();

  void _onRefresh() async {
    await onFetch.call(false);
    _refreshController.resetNoData();
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    /// 过滤首页为空时加载更多
    if (orderListState.records.isNotEmpty) {
      await onFetch.call(true);
    }
    if (orderListState.hasMoreData) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildTradesHeader(context, type),
        SizedBox(height: 1), // 分割线

        if (orderListState.records.isEmpty) ...[
          if (orderListState.isInitialLoad && orderListState.status == DataStatus.loading) ...[
            ///骨架图
            _buildShimmerWidget(type),
          ] else ...[
            /// 空视图
            Expanded(
              child: TableEmptyWidget(
                height: 40,
                width: 40,
                title: switch (type) {
                  OrderType.trades => "no_trades".tr(),
                  OrderType.order => "no_entrusts".tr(),
                  _ => "",
                },
                margin: EdgeInsets.symmetric(horizontal: 18.gw),
                radius: 10.gw,
              ),
            ),
          ]
        ],

        if (orderListState.records.isNotEmpty)
          Expanded(
              child: CommonRefresher(
            controller: _refreshController,
            enablePullUp: true,
            enablePullDown: true,
            onRefresh: _onRefresh,
            onLoading: _onLoading,
            child: ListView.builder(
              physics: const AlwaysScrollableScrollPhysics(),
              shrinkWrap: true,
              padding: EdgeInsets.zero,
              itemCount: orderListState.records.length,
              itemBuilder: (_, index) => _buildCell(context, orderListState.records[index], index),
            ),
          )),
      ],
    );
  }

  Widget _buildTradesHeader(BuildContext context, OrderType type) {
    final flexValues = type == OrderType.trades ? [6, 5, 5, 5] : [6, 5, 5, 5];

    final headerTitles = type == OrderType.trades
        ? [
            '${'name'.tr()}|${'code'.tr()}',
            '${'prices'.tr()}|${'quantity'.tr()}',
            'sumOfMoneySold'.tr(),
            '${'direction'.tr()}|${'time'.tr()}',
          ] // 成交明细
        : [
            '${'name'.tr()}|${'code'.tr()}',
            'orderPrice'.tr(),
            '${'completed'.tr()}|${'total'.tr()}',
            '${'direction'.tr()}|${'status'.tr()}',
          ]; // 委托明细

    return Container(
      height: 40,
      margin: EdgeInsets.symmetric(horizontal: 18.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.only(topLeft: Radius.circular(10.gr), topRight: Radius.circular(10.gr)),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 10.gw,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          for (var i = 0; i < headerTitles.length; i++) ...[
            if (i > 0) 5.horizontalSpace,
            Expanded(
              flex: flexValues[i],
              child: Tooltip(
                message: headerTitles[i],
                child: Text(
                  headerTitles[i],
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  textAlign: i == 0
                      ? TextAlign.left
                      : i == headerTitles.length - 1
                          ? TextAlign.end
                          : TextAlign.center,
                  style: context.textTheme.regular.fs12,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCell(BuildContext context, FTradeAcctOrderRecords item, int index) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 18.gw),
      child: switch (type) {
        OrderType.trades => AccountTradeCell(
            data: item,
            isLast: index == orderListState.records.length - 1,
            onTap: () {},
          ),
        OrderType.order => AccountEntrustCell(
            data: item,
            isLast: index == orderListState.records.length - 1,
            showCancelBtn: false,
            onTap: () {},
            onTapCancelBtn: () {},
          ),
        _ => SizedBox.shrink(),
      },
    );
  }

  Widget _buildShimmerWidget(OrderType type) {
    // 生成 3 个条目索引
    return Column(
      children: List.generate(3, (_) {
        return switch (type) {
          OrderType.trades => AccountTradeShimmerCell(),
          OrderType.order => AccountEntrustShimmerCell(),
        _ => SizedBox.shrink(),
        };
      }),
    );
  }
}
