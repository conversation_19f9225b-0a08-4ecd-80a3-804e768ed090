import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account_v2/1_common/order_history/order_history_sub_view/order_history_sub_view.dart';
import 'package:gp_stock_app/shared/widgets/page_view/direct_slide_page_view.dart';
import 'package:gp_stock_app/shared/widgets/tab/common_tab_bar.dart';

import 'order_history_cubit.dart';
import 'order_history_state.dart';

class OrderHistoryPage extends StatelessWidget {
  const OrderHistoryPage({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<OrderHistoryCubit>();
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: context.theme.cardColor,
        title: Text('transactionHistory'.tr()),
      ),
      body: BlocBuilder<OrderHistoryCubit, OrderHistoryState>(
        builder: (context, state) {
          return Column(
            children: [
              CommonTabBar(
                height: 55,
                padding: EdgeInsets.only(left: 14.gw),
                labelPadding: EdgeInsets.symmetric(horizontal: 10.gw),
                tabAlignment: TabAlignment.center,
                backgroundColor: context.theme.scaffoldBackgroundColor,
                data: state.details.entries.map((e) => "${tr(e.key.nameKey)}${e.value.countIfNotEmpty}").toList(),
                currentIndex: state.selectedIndex,
                onTap: (index) => cubit.updateSelIndex(index),
              ),
              Expanded(
                  child: DirectSlideView(
                pages: state.details.entries.map((e) {
                  return OrderHistorySubView(
                    type: e.key,
                    orderListState: e.value,
                    onFetch: (isLoadMore) =>
                        cubit.fetchMarketOrderList(type: e.key, orderListState: e.value, isLoadMore: isLoadMore),
                  );
                }).toList(),
                pageIndex: state.selectedIndex,
                onPageChanged: (int pageIndex) => cubit.updateSelIndex(pageIndex),
              ))
            ],
          );
        },
      ),
    );
  }
}
