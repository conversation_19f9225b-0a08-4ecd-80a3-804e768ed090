import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/market_category_state.dart';


class OrderHistoryState extends Equatable {

  /// 市场分类枚举
  /// Market category enum
  final MarketCategory? category;

  /// 合约id
  /// contractId
  final int? contractId;

  /// 当前选中的明细类型索引：0-holdings,1-trades,2-entrusts
  /// Currently selected detail type index
  final int selectedIndex;

  /// 各明细类型对应的子状态（含数据、页码、加载状态等）
  /// Map of detail type to its sub-state (data, page, status, etc.)
  final Map<OrderType, OrderListState> details;

  const OrderHistoryState({
    this.category,
    this.contractId,
    this.selectedIndex = 0,
    this.details = const {
      OrderType.trades: OrderListState(),
      OrderType.order: OrderListState(),
    },
  });

  OrderHistoryState copyWith({
    MarketCategory? category,
    int? contractId,
    int? selectedIndex,
    Map<OrderType, OrderListState>? details,
  }) {
    return OrderHistoryState(
      category: category ?? this.category,
      contractId: contractId ?? this.contractId,
      selectedIndex: selectedIndex ?? this.selectedIndex,
      details: details ?? this.details,
    );
  }

  /// 根据明细类型更新对应列表并返回新状态
  /// Return new state by updating list for given detail type
  OrderHistoryState updateDetail(
      OrderType type,
      OrderListState orderListState,
      ) {
    final newDetails = Map<OrderType, OrderListState>.from(details);
    newDetails[type] = orderListState;
    return copyWith(details: newDetails);
  }

  @override
  List<Object?> get props => [
    selectedIndex,
    details,
  ];
}