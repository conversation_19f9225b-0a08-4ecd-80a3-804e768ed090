import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/market_category_state.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AccountScreenStateV2 extends Equatable {
  /// 顶部 TabBar - 现货/合约
  final List<TradingAccountType> tabBarList;

  /// 顶部 TabBar.index
  final int tradingTabBarCurrentIndex;

  /// 现货账户-顶部TabBar-不同Category：【A股、港股、美股、股指、期货】
  final List<MarketCategoryState> spotViewModels;

  /// 现货账户-顶部TabBar-选中Index
  final int spotScreenCurrentIndex;

  /// 现货账户-选中的item, 目前用于持续更新 「持仓追加保证金dialog -可用保证金」
  final FTradeAcctOrderRecords? currentSelectSpotOrder;

  /// 合约账户-列表页码
  final int contractSummaryPageNum;

  /// 合约账户-是否有更多数据
  final bool contractSummaryHaveMoreData;

  /// 合约账户-合约概括列表
  final List<ContractSummaryPageRecord> contractSummaryList;

  /// 合约账户-网络状态
  final DataStatus contractSummaryStatus;

  /// 开仓资产
  final List<int> marketOpenAsset;

  const AccountScreenStateV2({
    this.tabBarList = const [TradingAccountType.Spot, TradingAccountType.Contract],
    this.tradingTabBarCurrentIndex = 0,
    this.spotViewModels = const [],
    this.spotScreenCurrentIndex = 0,
    this.currentSelectSpotOrder,
    this.contractSummaryPageNum = 0,
    this.contractSummaryHaveMoreData = true,
    this.contractSummaryList = const [],
    this.contractSummaryStatus = DataStatus.idle,
    this.marketOpenAsset = const [],
  });

  AccountScreenStateV2 copyWith({
    List<TradingAccountType>? tabBarList,
    int? tradingTabBarCurrentIndex,
    List<MarketCategoryState>? spotViewModels,
    int? spotScreenCurrentIndex,
    ValueGetter<FTradeAcctOrderRecords?>? currentSelectSpotOrder,
    int? contractSummaryPageNum,
    bool? contractSummaryHaveMoreData,
    List<ContractSummaryPageRecord>? contractSummaryList,
    DataStatus? contractSummaryStatus,
    List<int>? marketOpenAsset,
  }) {
    return AccountScreenStateV2(
      tabBarList: tabBarList ?? this.tabBarList,
      tradingTabBarCurrentIndex: tradingTabBarCurrentIndex ?? this.tradingTabBarCurrentIndex,
      spotViewModels: spotViewModels ?? this.spotViewModels,
      spotScreenCurrentIndex: spotScreenCurrentIndex ?? this.spotScreenCurrentIndex,
      currentSelectSpotOrder: currentSelectSpotOrder != null ? currentSelectSpotOrder() : this.currentSelectSpotOrder,
      contractSummaryPageNum: contractSummaryPageNum ?? this.contractSummaryPageNum,
      contractSummaryHaveMoreData: contractSummaryHaveMoreData ?? this.contractSummaryHaveMoreData,
      contractSummaryList: contractSummaryList ?? this.contractSummaryList,
      contractSummaryStatus: contractSummaryStatus ?? this.contractSummaryStatus,
      marketOpenAsset: marketOpenAsset ?? this.marketOpenAsset,
    );
  }

  @override
  List<Object?> get props => [
        tabBarList,
        tradingTabBarCurrentIndex,
        spotViewModels,
        spotScreenCurrentIndex,
        currentSelectSpotOrder,
        contractSummaryList,
        contractSummaryStatus,
        marketOpenAsset,
      ];
}
