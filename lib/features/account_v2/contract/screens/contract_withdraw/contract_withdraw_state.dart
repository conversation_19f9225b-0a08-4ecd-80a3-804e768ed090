part of 'contract_withdraw_cubit.dart';

class ContractWithdrawState extends Equatable {
  final int contractId;
  final DataStatus withdrawStatus;
  final DataStatus withdrawAmountStatus;
  final String? errorMessage;
  final ProfitWithdrawalConfigEntity? withdrawConfig;

  const ContractWithdrawState({
    required this.contractId,
    this.withdrawStatus = DataStatus.idle,
    this.withdrawAmountStatus = DataStatus.idle,
    this.errorMessage,
    this.withdrawConfig,
  });

  @override
  List<Object?> get props => [withdrawStatus, withdrawAmountStatus, errorMessage, withdrawConfig];

  ContractWithdrawState copyWith({
    int? contractId,
    DataStatus? withdrawStatus,
    DataStatus? withdrawAmountStatus,
    String? errorMessage,
    ProfitWithdrawalConfigEntity? withdrawConfig,
  }) {
    return ContractWithdrawState(
      contractId: contractId ?? this.contractId,
      withdrawStatus: withdrawStatus ?? this.withdrawStatus,
      withdrawAmountStatus: withdrawAmountStatus ?? this.withdrawAmountStatus,
      errorMessage: errorMessage ?? this.errorMessage,
      withdrawConfig: withdrawConfig ?? this.withdrawConfig,
    );
  }
}
