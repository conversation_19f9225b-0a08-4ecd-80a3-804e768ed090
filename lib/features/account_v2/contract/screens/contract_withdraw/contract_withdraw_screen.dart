import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';

import 'contract_withdraw_cubit.dart';

class ContractWithdrawScreen extends StatefulWidget {
  const ContractWithdrawScreen({super.key});

  @override
  State<ContractWithdrawScreen> createState() => _ContractWithdrawScreenState();
}

class _ContractWithdrawScreenState extends State<ContractWithdrawScreen> {
  final applyAmountController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('contractFundWithdrawal'.tr()),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: BlocConsumer<ContractWithdrawCubit, ContractWithdrawState>(
        listener: (context, state) {
          if (state.withdrawStatus == DataStatus.success) {
            GPEasyLoading.showSuccess(message: 'withdrawalSuccessful'.tr());
            Navigator.pop(context, true);
          } else if (state.withdrawStatus == DataStatus.failed) {
            GPEasyLoading.showToast(state.errorMessage ?? 'withdrawalFailed'.tr());
          } else if (state.withdrawStatus == DataStatus.loading) {
            GPEasyLoading.showLoading(message: 'loading'.tr());
          }
        },
        builder: (context, state) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                ShadowBox(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildAmountRow(context, 'withdrawableAmount'.tr(), state.withdrawConfig?.amount ?? 0.00),
                        const SizedBox(height: 12),
                        _buildAmountRow(
                            context, 'minimumAmount'.tr(), state.withdrawConfig?.minProfitWithdrawalAmount ?? 0.00),
                        const SizedBox(height: 12),
                        _buildAmountRow(
                            context, 'maximumAmount'.tr(), state.withdrawConfig?.maxProfitWithdrawalAmount ?? 0.00),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                ShadowBox(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 12,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          'withdrawAmount'.tr(),
                          style: context.textTheme.regular.fs16,
                        ),
                      ),
                      10.verticalSpace,
                      TextFieldWidget(
                        textInputType: TextInputType.number,
                        controller: applyAmountController,
                        hintText: 'enterAmount'.tr(),
                        inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^[0-9.]*'))],
                        onChanged: (value) => setState(() {}),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                CustomMaterialButton(
                  onPressed: applyAmountController.text.isEmpty || state.withdrawStatus == DataStatus.loading
                      ? null
                      : () =>
                          context.read<ContractWithdrawCubit>().submitWithdraw(applyAmount: applyAmountController.text),
                  buttonText: 'submit'.tr(),
                  borderRadius: 5.gr,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildAmountRow(BuildContext context, String label, double amount) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: context.textTheme.regular.fs16),
        FlipText(
          amount,
          style: context.textTheme.primary.fs16.w700,
        ),
      ],
    );
  }
}
