part of 'apply_trial_contract_cubit.dart';

class ApplyTrialContractState extends Equatable {
  final ApplyTrialContractConfigEntity? contractConfig;
  final DataStatus contractActivityFetchStatus;
  final DataStatus applyExperienceContractStatus;
  final DataStatus applyBonusContractStatus;
  final ApplyTrialContractConfigActivityRiskMap? selectedMarket;
  final int? selectedAmount;
  final bool isAgree;
  final ContractModelAmount? contractModelAmount;
  final String? currency;
  final MainContractType? contractType;
  final ContractType? contractSubType;
  final ContractApplyAmountEntity? bonusContractCalculation;

  const ApplyTrialContractState({
    this.contractConfig,
    this.contractActivityFetchStatus = DataStatus.idle,
    this.applyExperienceContractStatus = DataStatus.idle,
    this.applyBonusContractStatus = DataStatus.idle,
    this.selectedMarket,
    this.selectedAmount,
    this.isAgree = true,
    this.contractModelAmount,
    this.contractType,
    this.currency,
    this.contractSubType,
    this.bonusContractCalculation,
  });

  @override
  List<Object?> get props => [
        contractConfig,
        contractActivityFetchStatus,
        applyExperienceContractStatus,
        applyBonusContractStatus,
        isAgree,
        selectedMarket,
        selectedAmount,
        contractModelAmount,
        currency,
        contractSubType,
        contractType,
        bonusContractCalculation
      ];

  ApplyTrialContractState copyWith({
    ApplyTrialContractConfigEntity? contractConfig,
    DataStatus? contractActivityFetchStatus,
    DataStatus? applyExperienceContractStatus,
    DataStatus? applyBonusContractStatus,
    ApplyTrialContractConfigActivityRiskMap? selectedMarket,
    int? selectedAmount,
    String? error,
    bool? isAgree,
    ContractModelAmount? contractModelAmount,
    String? currency,
    MainContractType? contractType,
    ContractApplyAmountEntity? bonusContractCalculation,
    ContractType? contractSubType,
  }) =>
      ApplyTrialContractState(
        contractConfig: contractConfig ?? this.contractConfig,
        contractActivityFetchStatus: contractActivityFetchStatus ?? this.contractActivityFetchStatus,
        applyExperienceContractStatus: applyExperienceContractStatus ?? this.applyExperienceContractStatus,
        applyBonusContractStatus: applyBonusContractStatus ?? this.applyBonusContractStatus,
        selectedMarket: selectedMarket ?? this.selectedMarket,
        selectedAmount: selectedAmount ?? this.selectedAmount,
        isAgree: isAgree ?? this.isAgree,
        contractModelAmount: contractModelAmount ?? this.contractModelAmount,
        currency: currency ?? this.currency,
        contractType: contractType ?? this.contractType,
        bonusContractCalculation: bonusContractCalculation ?? this.bonusContractCalculation,
        contractSubType: contractSubType ?? this.contractSubType,
      );
}
