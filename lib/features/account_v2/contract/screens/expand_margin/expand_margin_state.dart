part of 'expand_margin_cubit.dart';

class ExpandMarginState extends Equatable {
  final int contractId;
  final ContractAction contractActionType;
  final ContractType contractType;
  final ContractMarginEntity? marginCallResponse;
  final DataStatus marginCallStatus;
  final DataStatus applyMarginCallStatus;
  final int? selectedAmount;
  final ContractModelAmount? contractModelAmount;
  final bool isAgree;
  final String? error;

  const ExpandMarginState({
    required this.contractId,
    required this.contractActionType,
    required this.contractType,
    this.marginCallResponse,
    this.marginCallStatus = DataStatus.idle,
    this.applyMarginCallStatus = DataStatus.idle,
    this.error,
    this.selectedAmount,
    this.contractModelAmount,
    this.isAgree = true,
  });

  ExpandMarginState copyWith({
    int? contractId,
    ContractAction? contractActionType,
    ContractType? contractType,
    int? selectedAmount,
    ContractMarginEntity? marginCallResponse,
    DataStatus? marginCallStatus,
    DataStatus? applyMarginCallStatus,
    String? error,
    bool? isAgree,
    ContractModelAmount? contractModelAmount,
  }) {
    return ExpandMarginState(
      contractId: contractId ?? this.contractId,
      contractActionType: contractActionType ?? this.contractActionType,
      contractType: contractType ?? this.contractType,
      selectedAmount: selectedAmount ?? this.selectedAmount,
      marginCallResponse: marginCallResponse ?? this.marginCallResponse,
      marginCallStatus: marginCallStatus ?? this.marginCallStatus,
      applyMarginCallStatus: applyMarginCallStatus ?? this.applyMarginCallStatus,
      error: error ?? this.error,
      isAgree: isAgree ?? this.isAgree,
      contractModelAmount: contractModelAmount ?? this.contractModelAmount,
    );
  }

  @override
  List<Object?> get props => [
        contractId,
        contractActionType,
        contractType,
        marginCallResponse,
        marginCallStatus,
        applyMarginCallStatus,
        error,
        selectedAmount,
        contractModelAmount,
        isAgree,
      ];
}
