import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/exchange_rate/exchange_rate_cubit.dart';
import 'package:gp_stock_app/shared/widgets/alert_dilaog/custom_alert_dialog.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';

import 'expand_margin_cubit.dart';

/// This is a dialog that shows the summary of the contract
/// of type [ContractAction.marginExpand] and [ContractAction.replenish]
class SummaryDialog extends StatelessWidget {
  final ContractAction contractType;

  const SummaryDialog({super.key, required this.contractType});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: CustomAlertDialog(
        hideActionButton: true,
        child: SizedBox(
          child: BlocBuilder<ExpandMarginCubit, ExpandMarginState>(
            builder: (context, state) {
              String getCurrency(String? marketType) {
                return switch (marketType) {
                  "CN" => "CNY",
                  "HK" => "HKD",
                  "US" => "USD",
                  _ => "CNY" // Default
                };
              }

              final currency = getCurrency(state.marginCallResponse?.marketType);
              final principal = contractType == ContractAction.replenish
                  ? state.marginCallResponse?.initCash
                  : state.selectedAmount?.toDouble() ?? 0;
              final rate = context.read<ExchangeRateCubit>().getCurrencyRateConfig(currency).rate;
              return Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    contractType == ContractAction.replenish
                        ? 'supplementLoss'.tr()
                        : contractType == ContractAction.marginExpand
                            ? 'expandMargin'.tr()
                            : 'renewContract'.tr(),
                    style: context.textTheme.primary.fs16.w600,
                  ),
                  16.verticalSpace,
                  AmountRow(
                    title: 'contractType'.tr(),
                    value: state.marginCallResponse?.contractTypeText?.tr(),
                  ),
                  10.verticalSpace,
                  AmountRow(
                    title: 'period'.tr(),
                    value: state.marginCallResponse?.periodTypeText?.tr(),
                  ),
                  10.verticalSpace,
                  AmountRow(
                    title: 'contractMultiple'.tr(),
                    value: '${state.marginCallResponse?.multiple.toString() ?? ''}${'xTimes'.tr()}',
                  ),
                  10.verticalSpace,
                  AmountRow(
                    title: contractType == ContractAction.replenish ? 'marginCall'.tr() : 'contractMargin'.tr(),
                    amount: state.selectedAmount?.toDouble() ?? 0,
                    currency: currency,
                  ),
                  10.verticalSpace,
                  if (contractType != ContractAction.replenish) ...[
                    AmountRow(
                      title: 'totalMargin'.tr(),
                      amount: state.contractModelAmount?.totalTadingFunds ?? 0,
                      currency: currency,
                    ),
                    10.verticalSpace,
                    AmountRow(
                      title: 'lossWarningLine'.tr(),
                      amount: state.contractModelAmount?.lossWarningLine ?? 0,
                      currency: currency,
                    ),
                    10.verticalSpace,
                    AmountRow(
                      title: 'liquidationLine'.tr(),
                      amount: state.contractModelAmount?.lossFlatLine ?? 0,
                      currency: currency,
                    ),
                    10.verticalSpace,
                    AmountRow(
                      title: 'capitalInterestRate'.tr(),
                      amount: state.contractModelAmount?.interestRate ?? 0,
                      currency: '$currency${'period_${state.marginCallResponse?.periodType}_rate'.tr()}',
                    ),
                    10.verticalSpace,
                  ],
                  AmountRow(
                    title: 'actualPaymentAmount'.tr(),
                    amount: contractType == ContractAction.replenish
                        ? (state.selectedAmount?.toDouble() ?? 0) / rate
                        : ((principal ?? 0) + (state.contractModelAmount?.interestRate ?? 0)) / rate,
                    currency: 'CNY',
                    prefix: currency != 'CNY' ? '≈ ' : '',
                  ),
                  16.verticalSpace,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                          child: CommonButton(
                        title: 'cancel'.tr(),
                        style: CommonButtonStyle.outlined,
                        onPressed: () => Navigator.pop(context),
                      )),
                      12.horizontalSpace,
                      Expanded(
                        child: BlocConsumer<ExpandMarginCubit, ExpandMarginState>(
                          listener: (context, state) {
                            if (state.applyMarginCallStatus == DataStatus.success) {
                              GPEasyLoading.showToast('success'.tr());
                              Navigator.pop(context);
                              Navigator.pop(context, true);
                            } else if (state.applyMarginCallStatus == DataStatus.failed) {
                              GPEasyLoading.showToast(state.error ?? 'errorMsg'.tr());
                              Navigator.pop(context);
                            }
                          },
                          builder: (context, state) {
                            return CommonButton(
                              title: 'submit'.tr(),
                              showLoading: state.applyMarginCallStatus == DataStatus.loading,
                              onPressed: () => (contractType == ContractAction.replenish ||
                                      contractType == ContractAction.marginExpand)
                                  ? context.read<ExpandMarginCubit>().addExpandMargin(contractType.value)
                                  : context.read<ExpandMarginCubit>().renewalContract(),
                            );
                          },
                        ),
                      ),
                    ],
                  )
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
