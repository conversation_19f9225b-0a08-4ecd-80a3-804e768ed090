import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/apis/contract.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/features/contract/domain/models/contract_model.dart';
import 'package:gp_stock_app/features/contract/domain/models/expand_margin/margin_call_response.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 'expand_margin_state.dart';

class ExpandMarginCubit extends Cubit<ExpandMarginState> {
  ExpandMarginCubit({
    required int contractId,
    required ContractAction contractActionType,
    required ContractType contractType,
  }) : super(ExpandMarginState(
          contractId: contractId,
          contractActionType: contractActionType,
          contractType: contractType,
        )) {
    getMarginCall();
  }

  Future<void> getMarginCall() async {
    emit(state.copyWith(marginCallStatus: DataStatus.loading));

    final res = await ContractApi.fetchContractMarginDetail(contractId: state.contractId);
    if (res == null) {
      emit(state.copyWith(marginCallStatus: DataStatus.failed));
      return;
    }
    emit(state.copyWith(marginCallStatus: DataStatus.success, marginCallResponse: res));
    final isBonus = res.contractType == ContractType.bonus;
    final amounts = isBonus ? res.bonusAmountList : res.amountList;

    if (amounts.isEmpty) {
      setSelectedAmount(1000);
      return;
    }

    final selectedAmount = isBonus
        ? (amounts.firstOrNull as num?)?.toInt()
        : (amounts.firstOrNull as MarginCallAmount?)?.applyAmount?.toInt();

    emit(state.copyWith(selectedAmount: selectedAmount));
    calculateContractAmount();
  }

  void setSelectedAmount(int? value) {
    if (value == null || value <= 0) return;
    emit(state.copyWith(selectedAmount: value));
    calculateContractAmount();
  }

  void setIsAgree(bool value) => emit(state.copyWith(isAgree: value));

  void calculateContractAmount() async {
    final amount = state.selectedAmount?.toDouble();
    try {
      final principal = amount ?? 0;
      final multiple = state.marginCallResponse?.multiple ?? 1;

      double adjustedPrincipal = principal;

      // Calculate leverage amount
      final multipleAmount = adjustedPrincipal * multiple;

      // Total trading funds
      final total = multipleAmount + adjustedPrincipal;

      // Warning loss line
      final warnValue = state.marginCallResponse?.warnValue ?? 0;
      final warn = ((principal * warnValue) / 100) + multipleAmount;

      // Close loss line
      final closeValue = state.marginCallResponse?.closeValue ?? 0;
      final close = ((principal * (100 - closeValue)) / 100) + multipleAmount;

      // Calculate interest
      final interestRate = state.marginCallResponse?.interestRate ?? 0;

      final percent = ((state.contractType == ContractAction.replenish
                  ? state.marginCallResponse?.totalFinance ?? 0
                  : multipleAmount) *
              interestRate) /
          100;
      final deductionAmount = adjustedPrincipal + percent;

      // Update contract model amount
      final contractModelAmount = ContractModelAmount(
        totalTadingFunds: total,
        lossWarningLine: warn,
        lossFlatLine: close,
        interestRate: percent,
        deductionAmount: deductionAmount,
        actualAmount: adjustedPrincipal,
        intrestDeductionAmount: 0,
        multipleAmount: multipleAmount,
      );

      emit(state.copyWith(contractModelAmount: contractModelAmount));
    } catch (e) {
      emit(state.copyWith(error: 'Error calculating contract amount: ${e.toString()}'));
    }
  }

  Future<void> addExpandMargin(int contractType) async {
    final marginModel = state.marginCallResponse;
    if (marginModel == null) return;
    emit(state.copyWith(applyMarginCallStatus: DataStatus.loading));

    final flag = await ContractApi.operateExpandMargin(
      contractId: marginModel.id,
      applyAmount: state.selectedAmount?.toDouble() ?? 0,
      type: contractType,
    );

    emit(state.copyWith(applyMarginCallStatus: flag ? DataStatus.success : DataStatus.failed));
  }

  Future<void> renewalContract() async {
    final marginModel = state.marginCallResponse;
    if (marginModel == null) return;
    emit(state.copyWith(applyMarginCallStatus: DataStatus.loading));

    final flag = await ContractApi.operateRenewalContract(contractId: marginModel.id);
    emit(state.copyWith(applyMarginCallStatus: flag ? DataStatus.success : DataStatus.failed));
  }
}
