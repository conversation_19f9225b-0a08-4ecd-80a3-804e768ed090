import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/market_category_state.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AccountContractDetailState extends Equatable {
  final ContractSummaryPageRecord data;
  final DataStatus summaryDataState;
  final MarketCategoryState viewModel;

  AccountContractDetailState({
    MarketCategory? category,
    required this.data,
    this.summaryDataState = DataStatus.idle,
    MarketCategoryState? viewModel,
  }) : viewModel = viewModel ?? MarketCategoryState(category: MarketCategory.fromMarketType(data.marketType));

  AccountContractDetailState copyWith({
    ContractSummaryPageRecord? data,
    DataStatus? summaryDataState,
    MarketCategoryState? viewModel,
  }) {
    return AccountContractDetailState(
      data: data ?? this.data,
      summaryDataState: summaryDataState ?? this.summaryDataState,
      viewModel: viewModel ?? this.viewModel,
    );
  }


  @override
  List<Object?> get props => [
        data,
        viewModel,
      ];
}
