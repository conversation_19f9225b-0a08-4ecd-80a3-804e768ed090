import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class ContractInfoV2State extends Equatable {
  final ContractSummaryPageRecord data;
  final DataStatus summaryDataState;

  const ContractInfoV2State({
    required this.data,
    this.summaryDataState = DataStatus.idle,
  });

  ContractInfoV2State copyWith({
    ContractSummaryPageRecord? data,
    DataStatus? summaryDataState,
  }) {
    return ContractInfoV2State(
      data: data ?? this.data,
      summaryDataState: summaryDataState ?? this.summaryDataState,
    );
  }

  @override
  List<Object?> get props => [
    data,
  ];
}
