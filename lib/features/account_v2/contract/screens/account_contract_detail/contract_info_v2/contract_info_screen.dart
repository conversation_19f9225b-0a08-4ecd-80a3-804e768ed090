import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/convert_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../../../../account/domain/models/account_summary/contract_summary_response.dart';
/// FIXME !!!!! Delete me later
/// 合约详细信息页
class ContractInfoScreen extends StatefulWidget {
  final ContractSummaryData contractSummary;

  const ContractInfoScreen({super.key, required this.contractSummary});

  @override
  State<ContractInfoScreen> createState() => _ContractInfoScreenState();
}

class _ContractInfoScreenState extends State<ContractInfoScreen> {
  late int? _contractSummaryID;
  late ContractSummaryData _contractSummaryData;
  bool _isInitialLoading = true;

  @override
  void initState() {
    super.initState();
    _contractSummaryID = widget.contractSummary.id;

    if (_contractSummaryID != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<AccountCubit>().getCurrentContractSummary(_contractSummaryID!);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AccountCubit, AccountState>(
      listenWhen: (previous, current) =>
          current.contractSummaryFetchStatus == DataStatus.success &&
          current.currentContractSummary != null &&
          current.currentContractSummary?.id == _contractSummaryID,
      listener: (context, state) {
        if (state.currentContractSummary != null) {
          setState(() {
            _contractSummaryData = state.currentContractSummary!;
            _isInitialLoading = false;
          });
        }
      },
      buildWhen: (previous, current) =>
          previous.contractSummaryFetchStatus != current.contractSummaryFetchStatus ||
          (current.currentContractSummary != null && current.currentContractSummary?.id == _contractSummaryID),
      builder: (context, state) {
        // Only show shimmer during initial loading, not during polling updates
        if (_isInitialLoading || state.contractSummaryFetchStatus == DataStatus.loading) {
          return _buildShimmerLayout(context);
        }

        final totalTradingCapitalRow = _buildInfoRowEx('totalTradingCapital', _contractSummaryData.totalPower); // 总操盘资金
        final initialMarginRow = _buildInfoRowEx('initialMargin', _contractSummaryData.initCash); // 起始保证金
        final lossWarningLineRow = _buildInfoRowEx('lossWarningLine', _contractSummaryData.warnRemindAmount); // 亏损警戒线
        final liquidationLineRow = _buildInfoRowEx('liquidationLine', _contractSummaryData.closeRemindAmount); // 亏损平仓线
        final interestRateInterestRow =
            _buildInfoRowEx('interestRateInterest', _contractSummaryData.interestRate, isPercent: true); // 利率利息
        final interestAmountRow = _buildInfoRowEx('interestAmount', _contractSummaryData.receivableInterest); // 累计利息
        final marketValueRow = _buildInfoRowEx('marketValue', _contractSummaryData.positionAmount); // 市值
        final expandedContractRow = _buildInfoRowEx('expandedContract', _contractSummaryData.expendAmount); // 扩大保证金
        final supplementLossRow = _buildInfoRowEx('supplementLoss', _contractSummaryData.coverLossAmount); // 追加保证金
        final floatingProfitLossRow =
            _buildInfoRowEx('positionProfitLoss', _contractSummaryData.accountWinAmount); // 持仓盈亏
        final profitLossRatioRow =
            _buildInfoRowEx('profitLossRatio', _contractSummaryData.winRate, isPercent: true); // 亏损率
        final withdrawalAmountRow = _buildInfoRowEx('withdrawalAmount', _contractSummaryData.withdrawAmount); // 提现金额
        final frozenRow = _buildInfoRowEx('frozen', _contractSummaryData.freezePower); // 冻结金额
        // final liquidationAmountRow = _buildInfoRowEx('liquidationAmount', _contractSummaryData.negativeAmount); // 穿仓金额

        // 体验和彩金 _contractSummaryData.type == 2 || _contractSummaryData.type == 3
        var renewalStatusRow = _buildInfoRowEx('contractRenewalStatus', "maturitySettlement".tr());
        if (_contractSummaryData.type == 1) {
          // 普通合约 general contract
          renewalStatusRow = _buildInfoRowEx(
            'contractRenewalStatus',
            _contractSummaryData.isAutoRenew ? "autoRenewal".tr() : "maturitySettlement".tr(),
          );
        }
        final giftAmountRow = _buildInfoRowEx('giftAmount', _contractSummaryData.giveAmount); // 彩金金额
        final contractNetAssetsRow =
            _buildInfoRowEx('contractNetAssets', _contractSummaryData.contractAssetAmount); // 合约净资产
        final distanceToWarningLineRow =
            _buildInfoRowEx('distanceToWarningLine', _contractSummaryData.gapWarnRemindAmount); // 距离预警线金额
        final distanceToLiquidationLineRow =
            _buildInfoRowEx('distanceToLiquidationLine', _contractSummaryData.gapCloseRemindAmount); // 距离强平线金额

        List<Widget> rows = [];

        if (_contractSummaryData.type == 2) {
          // 体验类型
          rows = [
            totalTradingCapitalRow, // 总操盘资金
            _buildInfoRowEx('giftAmount', _contractSummaryData.totalFinance), // 彩金资金
            lossWarningLineRow, // 亏损警戒线
            liquidationLineRow, // 亏损平仓线
            // marketValueRow, // 市值
            floatingProfitLossRow, // 浮动盈亏
            profitLossRatioRow, // 亏损率 base rate 0.2  * config
            frozenRow, // 冻结金额
            contractNetAssetsRow, // 合约净资产
            distanceToWarningLineRow, // 距离预警线金额
            distanceToLiquidationLineRow, // 距离强平线金额
            renewalStatusRow, // 合约续期状态
          ];
        } else {
          // 普通类型
          rows = [
            totalTradingCapitalRow, // 总操盘资金
            initialMarginRow, // 起始保证金
            lossWarningLineRow, // 亏损警戒线
            liquidationLineRow, // 亏损平仓线
            interestRateInterestRow, // 利率利息
            interestAmountRow, // 利息总额
            marketValueRow, // 市值
            expandedContractRow, // 扩大保证金
            supplementLossRow, // 追加保证金
            floatingProfitLossRow, // 浮动盈亏
            profitLossRatioRow, // 亏损率
            withdrawalAmountRow, // 提现金额
            frozenRow, // 冻结金额
            // liquidationAmountRow, // 穿仓金额
            contractNetAssetsRow, // 合约净资产
            distanceToWarningLineRow, // 距离预警线金额
            distanceToLiquidationLineRow, // 距离强平线金额
            renewalStatusRow, // 合约续期状态
          ];

          // 彩金类型
          if (_contractSummaryData.type == 3) {
            rows.insert(2, giftAmountRow); // 插入到亏损警戒线之前
          }
        }

        return _buildContentLayout(context, rows);
      },
    );
  }

  Widget _buildContentLayout(BuildContext context, List<Widget> rows) {
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: context.theme.cardColor,
        title: Text('contractDetails'.tr()),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(12.gr),
        child: Column(
          spacing: 12.gh,
          children: [
            ShadowBox(
              child: Column(
                children: [
                  _buildInfoRowEx('contractType', getContractLabel(_contractSummaryData)),
                  _buildInfoRowEx('period', getPeriodLabel(_contractSummaryData.periodType ?? 0)),
                  _buildInfoRowEx('date',
                      '${ConvertHelper.formatDateGeneral(_contractSummaryData.openTime ?? '')} - ${ConvertHelper.formatDateGeneral(_contractSummaryData.expireTime ?? '')}'),
                  _buildInfoRowEx('contractMultiple', '${_contractSummaryData.multiple}${'x'.tr()}'),
                ],
              ),
            ),
            ShadowBox(
              child: Column(
                children: rows,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Shimmer layout for loading state
  Widget _buildShimmerLayout(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: context.theme.cardColor,
        title: Text('contractDetails'.tr()),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.all(16.gr),
              decoration: BoxDecoration(
                color: context.theme.cardColor,
                borderRadius: BorderRadius.circular(12.gr),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(16.gr),
                child: Column(
                  children: List.generate(4, (index) => _buildShimmerRow()),
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.all(16.gr),
              decoration: BoxDecoration(
                color: context.theme.cardColor,
                borderRadius: BorderRadius.circular(12.gr),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(16.gr),
                child: Column(
                  children: List.generate(12, (index) => _buildShimmerRow()),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerRow() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.gh),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShimmerWidget(
            height: 16.gh,
            width: 100.gw,
            radius: 4.gr,
            color: context.theme.scaffoldBackgroundColor,
          ),
          ShimmerWidget(
            height: 16.gh,
            width: 80.gw,
            radius: 4.gr,
            color: context.theme.scaffoldBackgroundColor,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRowEx(String labelKey, dynamic value, {bool isPercent = false}) {
    String textValue;

    if (value is num) {
      textValue = value.toStringAsFixed(2);
      if (isPercent) textValue += '%';
    } else if (value != null) {
      textValue = value.toString();
    } else {
      textValue = '0.00';
    }

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.gh),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            labelKey.tr(), // 内部自动翻译
            style: context.textTheme.regular,
          ),
          Expanded(
            child: Text(
              textValue,
              style: context.textTheme.primary.w700.ffAkz,
              maxLines: 2,
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}
