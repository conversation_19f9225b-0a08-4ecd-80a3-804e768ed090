import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/convert_helper.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/functions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/navigator.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';

import 'terminate_contract_cubit.dart';
import 'terminate_contract_state.dart';

/// 终止合约
class TerminateContractPage extends StatelessWidget {
  const TerminateContractPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: context.theme.cardColor,
        title: Text('terminateContract'.tr()),
        centerTitle: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.gr),
        child: BlocBuilder<TerminateContractCubit, TerminateContractState>(
          builder: (context, state) {
            return Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        _buildContractInfoSection(context: context, contractSummary: state.model),
                        16.verticalSpace,
                        _buildAmountSection(context: context, contractSummary: state.model),
                      ],
                    ),
                  ),
                ),
                24.verticalSpace,
                _buildTerminateButton(context: context, contractSummary: state.model),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildContractInfoSection(
      {required BuildContext context, required ContractSummaryPageRecord contractSummary}) {
    String formatDateRange() {
      try {
        return '${ConvertHelper.formatDateGeneral(contractSummary.openTime)} - ${ConvertHelper.formatDateGeneral(
            contractSummary.expireTime)}';
      } catch (e) {
        return ConvertHelper.formatDateGeneral(contractSummary.openTime);
      }
    }

    return ShadowBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AmountRow(
            title: 'contractType'.tr(),
            value: getContractName(
              marketType: contractSummary.marketType,
              type: contractSummary.type,
              periodType: contractSummary.periodType,
              multiple: contractSummary.multiple,
              id: contractSummary.id,
            ),
          ),
          12.verticalSpace,
          AmountRow(
            title: 'period'.tr(),
            value: getPeriodType(contractSummary.periodType),
          ),
          12.verticalSpace,
          AmountRow(
            title: 'date'.tr(),
            value: formatDateRange(),
          ),
          12.verticalSpace,
          AmountRow(
            title: 'contractMultiple'.tr(),
            value: '${contractSummary.multiple}${'times'.tr()}',
          ),
        ],
      ),
    );
  }

// Helper function to get period type text
  String getPeriodType(int? period) =>
      switch (period) {
        1 => 'daily'.tr(),
        2 => 'weekly'.tr(),
        3 => 'monthly'.tr(),
        _ => 'daily'.tr(),
      };

  Widget _buildAmountSection({required BuildContext context, required ContractSummaryPageRecord contractSummary}) {
    return ShadowBox(
      child: Column(
        children: [
          AmountRow(
            title: 'totalMargin'.tr(),
            amount: contractSummary.totalPower,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'initialMargin'.tr(),
            amount: contractSummary.initCash,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'warningLine'.tr(),
            amount: contractSummary.warnRemindAmount,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'stopLossLine'.tr(),
            amount: contractSummary.closeRemindAmount,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'interestAmount'.tr(),
            amount: contractSummary.interestAmount,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'marketValue'.tr(),
            amount: contractSummary.positionAmount,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'expandedMargin'.tr(),
            amount: contractSummary.expendAmount,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'supplementLoss'.tr(),
            amount: contractSummary.coverLossAmount,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'floatingProfitLoss'.tr(),
            amount: contractSummary.winAmount,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'withdrawAmount'.tr(),
            amount: contractSummary.withdrawAmount,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'frozenAmount'.tr(),
            amount: contractSummary.freezePower,
          ),
        ],
      ),
    );
  }

  Widget _buildTerminateButton({required BuildContext context, required ContractSummaryPageRecord contractSummary}) {
    return BlocConsumer<TerminateContractCubit, TerminateContractState>(
      listenWhen: (previous, current) => previous.netStatus != current.netStatus,
      listener: (context, state) {
        if (state.netStatus == DataStatus.success) {
          context.read<AccountCubit>().getContractSummary();
          Navigator.popUntil(navigatorKey.currentContext!, (route) => route.isFirst);
        }
      },
      builder: (context, state) {
        return SizedBox(
          width: double.infinity,
          child: CommonButton(
            onPressed: () =>
                context.read<TerminateContractCubit>().terminateContract(contractId: contractSummary.id.toString()),
            title: 'terminate'.tr(),
            style: CommonButtonStyle.stockRed,
            showLoading: state.netStatus.isLoading,
          ),
        );
      },
    );
  }
}
