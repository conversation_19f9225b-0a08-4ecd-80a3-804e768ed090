import 'package:bloc/bloc.dart';
import 'package:gp_stock_app/core/models/apis/contract.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import 'terminate_contract_state.dart';

class TerminateContractCubit extends Cubit<TerminateContractState> {
  TerminateContractCubit({required ContractSummaryPageRecord model }) : super(TerminateContractState(model: model));

  Future<void> terminateContract({required String contractId}) async {
    emit(state.copyWith(netStatus: DataStatus.loading));
    final flag = await ContractApi.terminateContract(contractId: contractId);
    if (flag) {
      emit(state.copyWith(netStatus: DataStatus.success));
    } else {
      emit(state.copyWith(netStatus: DataStatus.failed));
    }
  }
}
