import 'package:bloc/bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/contract.dart';
import 'package:gp_stock_app/core/models/entities/account.dart';
import 'package:gp_stock_app/shared/services/polling/polling_sevice_v2.dart';

import 'account_position_detail_state.dart';

class AccountPositionDetailCubit extends Cubit<AccountPositionDetailState> {
  AccountPositionDetailCubit({PositionEntity? model, int? id})
      : super(AccountPositionDetailState(model: model, id: id)) {
    startPolling();
  }

  void startPolling() {
    getIt<PollingServiceV2>().startPolling(
        id: kGPAccountPositionDetailPolling,
        onPoll: () async {
          await fetchData();
          return true;
        },
        shouldStop: () => isClosed);
  }

  Future<void> fetchData() async {
    if (state.id == null && state.model == null) {
      throw Exception('AccountContractDetailCubit: `id` 和 `model` 不可同时为 null');
    }
    final id = state.id ?? state.model!.id;
    final res = await ContractApi.fetchPositionDetail(id: id);
    emit(state.copyWith(model: res));
  }

  @override
  Future<void> close() {
    print("👉AccountPositionDetailCubit.close()");
    return super.close();
  }
}
