import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/market_category_state.dart';

class OrderTableHeader extends StatelessWidget {
  final OrderType type;

  const OrderTableHeader({super.key, required this.type});

  @override
  Widget build(BuildContext context) {
    if (type == OrderType.positions) return SizedBox.shrink();

    final flexValues = type == OrderType.trades ? [6, 5, 5, 5] : [6, 5, 5, 5, 3];

    final headerTitles = type == OrderType.trades
        ? [
            '${'name'.tr()}|${'code'.tr()}',
            '${'prices'.tr()}|${'quantity'.tr()}',
            'sumOfMoneySold'.tr(),
            '${'direction'.tr()}|${'time'.tr()}',
          ] // 成交明细
        : [
            '${'name'.tr()}|${'code'.tr()}',
            'orderPrice'.tr(),
            '${'completed'.tr()}|${'total'.tr()}',
            '${'direction'.tr()}|${'status'.tr()}',
            ('operate'.tr()),
          ]; // 委托明细

    return Padding(
      padding: const EdgeInsets.only(bottom: 1), /// bottom divider
      child: Container(
        height: 40,
        margin: EdgeInsets.symmetric(horizontal: 18.gw),
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: BorderRadius.only(topLeft: Radius.circular(10.gr), topRight: Radius.circular(10.gr)),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 10.gw,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            for (var i = 0; i < headerTitles.length; i++) ...[
              if (i > 0) 5.horizontalSpace,
              Expanded(
                flex: flexValues[i],
                child: Tooltip(
                  message: headerTitles[i],
                  child: Text(
                    headerTitles[i],
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                    textAlign: i == 0
                        ? TextAlign.left
                        : i == headerTitles.length - 1
                            ? TextAlign.end
                            : TextAlign.center,
                    style: context.textTheme.regular.fs12,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
