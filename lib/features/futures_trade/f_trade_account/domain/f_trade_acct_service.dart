import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';

class FTradeAcctService {
  /// 期货委托订单数据
  static Future<FTradeAcctOrderModel?> fetchFTradeOrderPage(
      {required String instrument, required int pageNumber, required int pageSize}) async {
    String market = '';
    String securityType = '';
    String symbol = '';
    if (instrument.isNotEmpty && instrument.contains('|')) {
      List<String> parts = instrument.split('|');
      if (parts.length >= 3) {
        market = parts[0];
        securityType = parts[1];
        symbol = parts[2];
      } else {
        assert(false, 'Error instrument');
      }
    }
    final Response response = await NetworkProvider().get(
      ApiEndpoints.getOrderList,
      isAuthRequired: true,
      queryParameters: {
        'market': market,
        'securityType': securityType,
        'symbol': symbol,
        'pageNumber': pageNumber,
        'dataType': 5,
        'status': 0,
        'pageSize': pageSize,
      },
    );
    final responseModel = NetworkHelper.mappingResponseData<FTradeAcctOrderModel>(response);
    return responseModel.data;
  }

  /// 期货仓位数据
  static Future<FTradeAcctOrderModel?> fetchFTradePositionPage(
      {required String instrument, required int pageNumber, required int pageSize}) async {
    String market = '';
    String securityType = '';
    String symbol = '';
    if (instrument.isNotEmpty && instrument.contains('|')) {
      List<String> parts = instrument.split('|');
      if (parts.length >= 3) {
        market = parts[0];
        securityType = parts[1];
        symbol = parts[2];
      } else {
        assert(false, 'Error instrument');
      }
    }
    final Response response = await NetworkProvider().get(
      ApiEndpoints.getPositionList,
      isAuthRequired: true,
      queryParameters: {
        'market': market,
        'securityType': securityType,
        'symbol': symbol,
        'pageNumber': pageNumber,
        'dataType': 5,
        'pageSize': pageSize,
      },
    );
    final responseModel = NetworkHelper.mappingResponseData<FTradeAcctOrderModel>(response);
    return responseModel.data;
  }
}
