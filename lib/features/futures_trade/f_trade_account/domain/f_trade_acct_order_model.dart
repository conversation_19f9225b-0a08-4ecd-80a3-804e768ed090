import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/features/account/domain/models/order/order_response.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/f_trade_acct_order_model.g.dart';
import 'dart:convert';

import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
export 'package:gp_stock_app/generated/json/f_trade_acct_order_model.g.dart';

@JsonSerializable()
class FTradeAcctOrderModel {
  int current = 0;
  bool hasNext = false;
  List<FTradeAcctOrderRecords> records = [];
  int total = 0;

  FTradeAcctOrderModel();

  factory FTradeAcctOrderModel.fromJson(Map<String, dynamic> json) => $FTradeAcctOrderModelFromJson(json);

  Map<String, dynamic> toJson() => $FTradeAcctOrderModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

extension FTradeOrderRecordListExtension on List<FTradeAcctOrderRecords> {
  /// 第一个做多订单
  FTradeAcctOrderRecords? findFirstLongOrder() {
    for (var record in this) {
      if (record.tradeType == 1) {
        return record;
      }
    }
    return null;
  }

  /// 第一个做空订单
  FTradeAcctOrderRecords? findFirstShortOrder() {
    for (var record in this) {
      if (record.tradeType == 2) {
        return record;
      }
    }
    return null;
  }
}

@JsonSerializable()
class FTradeAcctOrderRecords {
  /// 买入平均价格
  double buyAvgPrice = 0.0;

  /// 历史持仓总量
  double buyTotalNum = 0.0;

  /// 持仓总数
  double positionTotalNum = 0.0;

  /// 持仓成本价
  double costPrice = 0.0;

  /// 创建时间
  String createTime = '';

  /// 账户币种
  String currency = '';

  /// 交易方向 1 买入 2 卖出
  int direction = 0;

  /// 交易类型 1 做多 2 做空
  int tradeType = 0;

  /// 不可用数量
  double disableNum = 0.0;

  /// 持仓总共扣的手续费
  double feeAmount = 0.0;

  /// 持仓保证金
  double marginAmount = 0.0;

  /// 浮动盈亏
  double floatingProfitLoss = 0.0;

  /// 盈亏率
  double floatingProfitLossRate = 0.0;

  /// ID
  int id = 0;

  /// 市场
  String market = 'N/A';

  /// 当前市值
  double marketValue = 0.0;

  /// 可用数量
  double restNum = 0.0;

  /// 可用保证金
  double availableMargin = 0.0;

  /// 股票类型
  String securityType = '';

  /// 当前股票最新价格
  double stockPrice = 0.0;

  /// 股票代码
  String symbol = 'N/A';

  /// 股票名称
  String symbolName = 'N/A';

  /// 交易单位，杠杆，比如：股指交易 30元/点
  double tradeUnit = 0.0;

  /// 类型 1：股票 2：股指
  int type = 0;

  /// 委托状态 0=>委托中 1=>委托撤销 2=>订单成交成功 3=>合约到期自动撤销
  int status = 0;

  /// 	委托价格
  double tradePrice = 0.0;

  /// 成交股数
  double dealNum = 0.0;

  /// 委托股数
  double tradeNum = 0.0;

  /// 取消时间
  String cancelTime = '';

  /// 成交价格
  double dealPrice = 0.0;

  /// 成交时间
  String dealTime = '';

  /// 市价单 限价单
  int priceType = -1;

  /// 交易时间
  String tradeTime = '';

  /// 盈利亏损金额
  double winAmount = 0.0;

  /// 交易手续费
  double tradeFee = 0.0;

  /// 合约id
  int contractId = 0;

  /// 合约类型
  int contractType = 0;

  ///
  int periodType = 0;

  ///
  int multiple = 0;

  ///
  int contractAccountId = 0;

  ///
  double transactionAmount = 0.0;

  /// 产品编码:期货使用
  String productCode = '';

  FTradeListItemModel toFTradeListItemModel() {
    return FTradeListItemModel()
      ..latestPrice = stockPrice
      ..productCode = productCode
      ..symbol = symbol
      // ..chg = chg
      // ..isMain = isMain
      // ..precision = precision
      // ..gain = gain
      ..market = market
      // ..volume = volume
      ..securityType = securityType
      ..name = symbolName;
    // ..isContinuous = isContinuous
    // ..position = position
    // ..prevPosition = prevPosition
  }

  OrderRecord toOrderRecord() {
    return OrderRecord(
      id: id,
      cancelTime: cancelTime,
      currency: currency,
      dealNum: dealNum,
      dealPrice: dealPrice,
      dealTime: dealTime,
      direction: direction,
      market: market,
      priceType: priceType,
      status: status,
      symbol: symbol,
      symbolName: symbolName,
      tradeNum: tradeNum,
      tradePrice: tradePrice,
      tradeTime: tradeTime,
      tradeType: tradeType,
      transactionAmount: transactionAmount,
      type: type,
      buyAvgPrice: buyAvgPrice,
      buyTotalNum: buyTotalNum,
      costPrice: costPrice,
      disableNum: disableNum,
      floatingProfitLoss: floatingProfitLoss,
      floatingProfitLossRate: floatingProfitLossRate,
      marketValue: marketValue,
      restNum: restNum,
      securityType: securityType,
      createTime: createTime,
      stockPrice: stockPrice,
      contractAccountId: contractAccountId,
      periodType: periodType,
      multiple: multiple,
      winAmount: winAmount,
      tradeFee: tradeFee,
      contractId: contractId,
      contractType: contractType,
      productCode: productCode,
    );
  }

  String displayByOneRow() {
    return ' $id | ${'averagePrice'.tr()} ${buyAvgPrice.toStringAsFixed(2)} | ${'total'.tr()} $positionTotalNum | ${'available'.tr()} $restNum';
  }

  String makeInstrument() {
    return '$market|$securityType|$symbol';
  }

  String get instrument => '$market|$securityType|$symbol';

  Instrument get instrumentInfo => Instrument(instrument: instrument);

  bool get isIndex => securityType == '2';
  bool get isCnFTrade => securityType == '4';

  FTradeAcctOrderRecords();

  factory FTradeAcctOrderRecords.fromJson(Map<String, dynamic> json) => $FTradeAcctOrderRecordsFromJson(json);

  Map<String, dynamic> toJson() => $FTradeAcctOrderRecordsToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
