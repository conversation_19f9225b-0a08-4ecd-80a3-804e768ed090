part of 'f_trade_acct_entrust_cubit.dart';

class FTradeAcctEntrustState extends Equatable {
  final DataStatus dataStatus;
  final FTradeAcctOrderModel? orderModel;

  const FTradeAcctEntrustState({
    this.dataStatus = DataStatus.idle,
    this.orderModel,
  });

  FTradeAcctEntrustState copyWith({
    required DataStatus status,
    FTradeAcctOrderModel? orderModel,
  }) {
    return FTradeAcctEntrustState(
      dataStatus: status,
      orderModel: orderModel ?? this.orderModel,
    );
  }

  @override
  List<Object?> get props => [dataStatus, orderModel];
}
