part of 'f_trade_acct_position_cubit.dart';

class FTradeAcctPositionState extends Equatable {
  final DataStatus dataStatus;
  final FTradeAcctOrderModel? orderModel;

  const FTradeAcctPositionState({
    this.dataStatus = DataStatus.idle,
    this.orderModel,
  });

  FTradeAcctPositionState copyWith({
    required DataStatus status,
    FTradeAcctOrderModel? orderModel,
  }) {
    return FTradeAcctPositionState(
      dataStatus: status,
      orderModel: orderModel ?? this.orderModel,
    );
  }

  @override
  List<Object?> get props => [dataStatus, orderModel];
}
