import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/f_trade_state_model.g.dart';
import 'dart:convert';

import 'package:gp_stock_app/shared/app/extension/time_zone.dart';
export 'package:gp_stock_app/generated/json/f_trade_state_model.g.dart';

@JsonSerializable()
class FTradeStateModel {
  String statusStr = '';
  int closeTime = 0;
  int openTime = 0;
  // 2=>交易中 4=>已收盘 6=>交易时段间休市 0=>待开盘
  int status = 0;

  String statusDisplay() {
    String text = '--';
    if (status == 0) {
      text = 'f_market_awaiting_open'.tr();
    }
    if (status == 2) {
      text = 'f_market_trading'.tr();
    }
    if (status == 4) {
      text = 'f_market_closed'.tr();
    }
    if (status == 6) {
      text = 'f_market_halted'.tr();
    }
    return text;
  }

  String timestampDisplay({required int timestamp, required String market}) {
    const defaultDisplay = '';

    String format(int time) {
      if (time == 0) return defaultDisplay;
      return TimeZoneHelper.formatTimeInZone(
        time,
        countryTimeZones[market] ?? 'Asia/Shanghai',
        format: TimeFormat.full,
      );
    }

    switch (status) {
      case 0: // 待开盘
      case 6: // 休市
        return format(openTime);
      case 2: // 交易中
        return format(timestamp);
      case 4: // 已收盘
        return format(closeTime);
      default:
        return defaultDisplay;
    }
  }

  FTradeStateModel();

  factory FTradeStateModel.fromJson(Map<String, dynamic> json) => $FTradeStateModelFromJson(json);

  Map<String, dynamic> toJson() => $FTradeStateModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
