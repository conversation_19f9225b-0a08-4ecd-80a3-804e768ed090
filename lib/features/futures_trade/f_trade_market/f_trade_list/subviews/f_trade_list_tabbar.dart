import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/features/market/widgets/market_table_header.dart';

class FTradeListTabbar extends StatelessWidget {
  final int selectedTabIdx;
  final void Function(int) onTap;

  const FTradeListTabbar({
    super.key,
    required this.selectedTabIdx,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return AnimationLimiter(
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 300),
            childAnimationBuilder: (widget) => SlideAnimation(
              horizontalOffset: 50.0,
              child: FadeInAnimation(
                child: widget,
              ),
            ),
            children: _marketTabbar(),
          ),
        ),
      ),
    );
  }

  List<Widget> _marketTabbar() => [
        _marketTabBtn(FTradeMarketType.dalian.name, FTradeMarketType.dalian.idx),
        25.horizontalSpace,
        _marketTabBtn(FTradeMarketType.zhengzhou.name, FTradeMarketType.zhengzhou.idx),
        25.horizontalSpace,
        _marketTabBtn(FTradeMarketType.shanghai.name, FTradeMarketType.shanghai.idx),
        25.horizontalSpace,
        _marketTabBtn(FTradeMarketType.shanghaiEnergy.name, FTradeMarketType.shanghaiEnergy.idx),
        25.horizontalSpace,
        _marketTabBtn(FTradeMarketType.china.name, FTradeMarketType.china.idx),
      ];

  Widget _marketTabBtn(String label, int selfIdx) => MarketTableHeader(
        title: label.tr(),
        isSelected: selfIdx == selectedTabIdx,
        onTap: () => onTap(selfIdx),
      );
}
