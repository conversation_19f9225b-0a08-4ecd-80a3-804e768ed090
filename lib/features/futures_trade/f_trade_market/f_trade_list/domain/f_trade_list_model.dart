import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';
import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/f_trade_list_model.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/f_trade_list_model.g.dart';

@JsonSerializable()
class FTradeListModel {
  /// 总的期数数量
  int totalNum = 0;
  List<FTradeListItemModel> list = [];

  FTradeListModel();

  factory FTradeListModel.fromJson(Map<String, dynamic> json) => $FTradeListModelFromJson(json);

  Map<String, dynamic> toJson() => $FTradeListModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class FTradeListItemModel {
  /// 最新价
  double latestPrice = 0.0;

  /// 证券代码 "08619"
  String symbol = 'N/A';

  /// 产品代码
  String productCode = '';

  /// 涨跌额
  double chg = 0.0;

  /// 是否主力
  bool isMain = false;

  /// 保留位数
  int precision = 0;

  /// 涨跌额
  double gain = 0.00;

  /// 证券市场 "HKEX"
  String market = '';

  /// 成交量
  int volume = 0;

  /// 证券类型 股票=>1 期货=>22
  String securityType = '';

  /// 名称
  String name = 'N/A';

  /// 是否主连
  bool isContinuous = false;

  /// 持仓量
  int position = 0;

  /// 昨持仓
  int prevPosition = 0;

  FTradeListItemModel();

  factory FTradeListItemModel.fromJson(Map<String, dynamic> json) => $FTradeListItemModelFromJson(json);

  FTradeInfoModel toFTradeInfoModel() {
    return FTradeInfoModel()
      ..latestPrice = latestPrice
      ..productCode = productCode
      ..symbol = symbol
      ..chg = chg
      ..isMain = isMain
      ..precision = precision
      ..gain = gain
      ..market = market
      ..volume = volume
      ..securityType = securityType
      ..name = name
      ..isContinuous = isContinuous
      ..position = position
      ..prevPosition = prevPosition;
  }

  Map<String, dynamic> toJson() => $FTradeListItemModelToJson(this);

  String makeInstrument() {
    return '$market|$securityType|$symbol';
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
