import 'package:gp_stock_app/core/utils/base_screen_data_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';

/// 5个交易所数据的缓存
/// 来源 http
class FTradeListAllList {
  /// 大商所
  List<FTradeListItemModel> fDalianList = [];
  int fDalianTotalNum = 0;

  /// 鄭商所
  List<FTradeListItemModel> fZhengzhouList = [];
  int fZhengzhouTotalNum = 0;

  /// 上期所
  List<FTradeListItemModel> fShanghaiList = [];
  int fShanghaiTotalNum = 0;

  /// 上期能源
  List<FTradeListItemModel> eShanghaiList = [];
  int eShanghaiTotalNum = 0;

  /// 中金所
  List<FTradeListItemModel> fChinaList = [];
  int fChinaTotalNum = 0;

  FTradeListAllList deepCopy() {
    final copy = FTradeListAllList();

    copy.fDalianList = fDalianList.map((e) => FTradeListItemModel.fromJson(e.toJson())).toList();
    copy.fDalianTotalNum = fDalianTotalNum;

    copy.fZhengzhouList = fZhengzhouList.map((e) => FTradeListItemModel.fromJson(e.toJson())).toList();
    copy.fZhengzhouTotalNum = fZhengzhouTotalNum;

    copy.fShanghaiList = fShanghaiList.map((e) => FTradeListItemModel.fromJson(e.toJson())).toList();
    copy.fShanghaiTotalNum = fShanghaiTotalNum;

    copy.eShanghaiList = eShanghaiList.map((e) => FTradeListItemModel.fromJson(e.toJson())).toList();
    copy.eShanghaiTotalNum = eShanghaiTotalNum;

    copy.fChinaList = fChinaList.map((e) => FTradeListItemModel.fromJson(e.toJson())).toList();
    copy.fChinaTotalNum = fChinaTotalNum;

    return copy;
  }
}

/// 5个交易所数据的缓存
/// 设置: 缓存变量 只缓存第一页的数据 设置有效期 5分钟
///
/// 生命周期起点: 从开始访问页面创建
/// 生命周期更新: HTTP/WS 更新这个数据, 重置有效期
/// 生命周期结束: 用户点击底部大按钮(首页,合约,交易等)的时候,检查是否过期,过期就删除
class FTradeListRepository extends BaseScreenDataRepository<FTradeListAllList> {
  FTradeListRepository._internal();
  static final FTradeListRepository _instance = FTradeListRepository._internal();
  factory FTradeListRepository() => _instance;

  (int, List<FTradeListItemModel>) loadCacheData(int fExchangeIdx) {
    List<FTradeListItemModel> items = [];
    int itemsTotl = 0;
    switch (fExchangeIdx) {
      case 0:
        items = cachedData?.fDalianList ?? [];
        itemsTotl = cachedData?.fChinaTotalNum ?? 0;
        break;
      case 1:
        items = cachedData?.fZhengzhouList ?? [];
        itemsTotl = cachedData?.fZhengzhouTotalNum ?? 0;
        break;
      case 2:
        items = cachedData?.fShanghaiList ?? [];
        itemsTotl = cachedData?.fShanghaiTotalNum ?? 0;
        break;
      case 3:
        items = cachedData?.eShanghaiList ?? [];
        itemsTotl = cachedData?.eShanghaiTotalNum ?? 0;
        break;
      case 4:
        items = cachedData?.fChinaList ?? [];
        itemsTotl = cachedData?.fChinaTotalNum ?? 0;
        break;
      default:
        assert(false, '未定义的 fExchangeIdx');
        return (0, []);
    }
    return (itemsTotl, items);
  }

  void saveCacheData(int fExchangeIdx, (int, List<FTradeListItemModel>) data) {
    late final FTradeListAllList newData;
    if (cachedData != null) {
      newData = cachedData!.deepCopy();
    } else {
      newData = FTradeListAllList();
    }
    switch (fExchangeIdx) {
      case 0:
        newData.fDalianTotalNum = data.$1;
        newData.fDalianList = data.$2;
        break;
      case 1:
        newData.fZhengzhouTotalNum = data.$1;
        newData.fZhengzhouList = data.$2;
        break;
      case 2:
        newData.fShanghaiTotalNum = data.$1;
        newData.fShanghaiList = data.$2;
        break;
      case 3:
        newData.eShanghaiTotalNum = data.$1;
        newData.eShanghaiList = data.$2;
        break;
      case 4:
        newData.fChinaTotalNum = data.$1;
        newData.fChinaList = data.$2;
        break;
      default:
        assert(false, '未定义的 fExchangeIdx');
        return;
    }
    updateCache(newData);
  }
}
