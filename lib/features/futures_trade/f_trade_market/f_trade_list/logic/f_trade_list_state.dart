part of 'f_trade_list_cubit.dart';

class FTradeListState extends Equatable {
  final List<FTradeListItemModel> items;
  final int totalNum;
  final DataStatus dataStatus;

  const FTradeListState({
    this.items = const [],
    this.totalNum = 0,
    this.dataStatus = DataStatus.idle,
  });

  FTradeListState copyWith({
    required List<FTradeListItemModel> items,
    required int totalNum,
    required DataStatus status,
  }) {
    return FTradeListState(
      items: items,
      totalNum: totalNum,
      dataStatus: status,
    );
  }

  @override
  List<Object?> get props => [items, dataStatus];
}
