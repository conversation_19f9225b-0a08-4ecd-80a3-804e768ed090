import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_depth_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_tick_model.dart';

class FTradeKLineScrollService {
  /// 期货信息
  static Future<FTradeInfoModel?> fetchFTradeInfo(CancelToken? cancelToken, {required String instrument}) async {
    final Response response = await NetworkProvider().get(
      ApiEndpoints.getFuturesMarketStockInfo,
      queryParameters: {'instrument': instrument},
      cancelToken: cancelToken,
    );
    final responseModel = NetworkHelper.mappingResponseData<FTradeInfoModel>(response);
    return responseModel.data;
  }

  /// 期货K线数据
  ///
  /// period=> 1min/5min/15min/30min/60min/day/week/month/year
  static Future<FTradeKLineModel?> fetchFTradeKLine(
    CancelToken? cancelToken, {
    required String instrument,
    required String period,
  }) async {
    final Response response = await NetworkProvider().get(
      ApiEndpoints.getFuturesMarketKLine,
      queryParameters: {'instrument': instrument, 'period': period},
      cancelToken: cancelToken,
    );
    final responseModel = NetworkHelper.mappingResponseData<FTradeKLineModel>(response);
    return responseModel.data;
  }

  /// 期货分时数据
  ///
  /// period=> day/5day
  static Future<FTradeKLineModel?> fetchFTradeTimeLine(CancelToken? cancelToken,
      {required String instrument, required String period}) async {
    final Response response = await NetworkProvider().get(
      ApiEndpoints.getFuturesMarketTimeLine,
      queryParameters: {'instrument': instrument, 'period': period},
      cancelToken: cancelToken,
    );
    final responseModel = NetworkHelper.mappingResponseData<FTradeKLineModel>(response);
    return responseModel.data;
  }

  /// 逐笔成交数据
  static Future<FTradeTickModel?> fetchTickList({required String instrument, required int pageNumber}) async {
    final Response response = await NetworkProvider().get(
      ApiEndpoints.getFuturesMarketTickList,
      queryParameters: {'instrument': instrument, 'pageSize': 100, 'pageNumber': pageNumber},
    );
    final responseModel = NetworkHelper.mappingResponseData<FTradeTickModel>(response);
    return responseModel.data;
  }

  /// 期货深度交易数据
  static Future<FTradeDepthModel?> fetchFTradeDepth(CancelToken? cancelToken, {required String instrument}) async {
    final Response response = await NetworkProvider().get(
      ApiEndpoints.getFuturesMarketDepthL2,
      queryParameters: {'instrument': instrument, 'depth': 10},
      cancelToken: cancelToken,
    );
    final responseModel = NetworkHelper.mappingResponseData<FTradeDepthModel>(response);
    return responseModel.data;
  }

}
