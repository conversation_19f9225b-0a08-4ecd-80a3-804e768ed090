import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/f_trade_depth_model.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/f_trade_depth_model.g.dart';

@JsonSerializable()
class FTradeDepthModel {
  /// 证券市场
  String market = '';

  /// 最新价
  double latestPrice = 0.0;

  /// 证券代码
  String symbol = '';

  /// 证券类型
  String securityType = '';

  /// 收盘价
  double close = 0.0;

  /// 卖盘
  List<FTradeDepthAsk> ask = [];

  /// 买盘
  List<FTradeDepthBid> bid = [];

  FTradeDepthModel();

  factory FTradeDepthModel.fromJson(Map<String, dynamic> json) => $FTradeDepthModelFromJson(json);

  Map<String, dynamic> toJson() => $FTradeDepthModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class FTradeDepthAsk {
  /// 排序序列号
  int no = 0;

  /// 量
  int vol = 0;

  /// 价格
  double price = 0.0;

  /// 档位
  int depthNo = 0;

  FTradeDepthAsk();

  factory FTradeDepthAsk.fromJson(Map<String, dynamic> json) => $FTradeDepthAskFromJson(json);

  Map<String, dynamic> toJson() => $FTradeDepthAskToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class FTradeDepthBid {
  /// 排序序列号
  int no = 0;

  /// 量
  int vol = 0;

  /// 价格
  double price = 0.0;

  /// 档位
  int depthNo = 0;

  FTradeDepthBid();

  FTradeDepthAsk toAsk() {
    final temp = FTradeDepthAsk();
    temp.no = no;
    temp.vol = vol;
    temp.price = price;
    temp.depthNo = depthNo;
    return temp;
  }

  factory FTradeDepthBid.fromJson(Map<String, dynamic> json) => $FTradeDepthBidFromJson(json);

  Map<String, dynamic> toJson() => $FTradeDepthBidToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
