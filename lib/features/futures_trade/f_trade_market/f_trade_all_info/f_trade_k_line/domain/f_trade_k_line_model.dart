import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/f_trade_k_line_model.g.dart';
import 'dart:convert';

import 'package:k_chart_plus/entity/k_line_entity.dart';
export 'package:gp_stock_app/generated/json/f_trade_k_line_model.g.dart';

@JsonSerializable()
class FTradeKLineModel {
  late FTradeInfoKLineModel detail;
  List<FTradeKLineItem> list = [];

  FTradeKLineModel();

  factory FTradeKLineModel.fromJson(Map<String, dynamic> json) => $FTradeKLineModelFromJson(json);

  Map<String, dynamic> toJson() => $FTradeKLineModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class FTradeInfoKLineModel {
  /// 证券代码
  String symbol = '';

  /// 证券市场
  String market = '';

  /// 证券类型
  String securityType = '';

  /// 最新价
  double latestPrice = 0.0;

  /// 收盘价
  double close = 0.0;

  /// 开盘价
  double open = 0.0;

  /// 最高价
  double high = 0.0;

  /// 最低价
  double low = 0.0;

  /// 证券名称
  String name = '';

  FTradeInfoKLineModel();

  factory FTradeInfoKLineModel.fromJson(Map<String, dynamic> json) => $FTradeInfoKLineModelFromJson(json);

  Map<String, dynamic> toJson() => $FTradeInfoKLineModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class FTradeKLineItem {
  /// 时间
  int time = 0;

  /// 均价
  double price = 0.0;

  /// 成交量
  double volume = 0.0;

  /// 开盘
  double open = 0.0;

  /// 收盘
  double close = 0.0;

  /// 最高
  double high = 0.0;

  /// 最低
  double low = 0.0;

  /// 成交额
  double amount = 0.0;

  FTradeKLineItem();

  factory FTradeKLineItem.fromJson(Map<String, dynamic> json) => $FTradeKLineItemFromJson(json);

  KLineEntity toKLineEntity({
    required bool isTimeOrKline,
    required double openPrice,
    required int timeOffsetHours,
  }) {
    final adjustedTime = DateTime.fromMillisecondsSinceEpoch(
      time * 1000,
      isUtc: true,
    ).add(Duration(hours: timeOffsetHours)).millisecondsSinceEpoch;

    return KLineEntity.fromCustom(
      time: adjustedTime,
      close: isTimeOrKline ? price : close,
      open: openPrice,
      high: isTimeOrKline ? price : high,
      low: isTimeOrKline ? price : low,
      vol: volume,
      amount: price,
    );
  }

  Map<String, dynamic> toJson() => $FTradeKLineItemToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
