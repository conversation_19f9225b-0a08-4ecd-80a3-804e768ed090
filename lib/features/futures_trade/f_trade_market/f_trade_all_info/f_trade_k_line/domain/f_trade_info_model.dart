import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/f_trade_info_model.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/f_trade_info_model.g.dart';

@JsonSerializable()
class FTradeInfoModel {
  /// 证券代码
  String symbol = '';

  /// 保留位数
  int precision = 0;

  /// 结算价
  double settle = 0.0;

  /// 证券状态：1 正常交易，9 退市
  int securityStatus = 0;

  /// 涨跌幅
  double gain = 0.0;

  /// 最高价
  double high = 0.0;

  /// 最低价
  double low = 0.0;

  /// 合约乘数（合约规模）
  int contractSize = 0;

  /// 币种
  String currency = '';

  /// 是否是连续合约
  bool isContinuous = false;

  /// 收盘价
  double close = 0.0;

  /// 最新价
  double latestPrice = 0.0;

  /// 成交额
  int amount = 0;

  /// 涨跌额
  double chg = 0.0;

  /// 是否是主力合约
  bool isMain = false;

  /// 每手股数
  int lotSize = 0;

  /// 涨停价（整数型）
  int priceUpLimited = 0;

  /// 跌停价
  double priceDownLimited = 0.0;

  /// 证券市场
  String market = '';

  /// 成交量
  int volume = 0;

  /// 到期时间（期货合约）
  int expireTime = 0;

  /// 产品代码
  String productCode = '';

  /// 证券类型
  String securityType = '';

  /// 最后一笔成交时间（时间戳）
  int lastTradeTime = 0;

  /// 证券名称
  String name = '';

  /// 最新时间（时间戳）
  int latestTime = 0;

  /// 持仓量
  int position = 0;

  /// 合约代码
  String contractCode = '';

  /// 上一个交易日持仓量
  int prevPosition = 0;

  /// 开盘价
  double open = 0.0;

  FTradeInfoModel();

  factory FTradeInfoModel.fromJson(Map<String, dynamic> json) => $FTradeInfoModelFromJson(json);

  Map<String, dynamic> toJson() => $FTradeInfoModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
