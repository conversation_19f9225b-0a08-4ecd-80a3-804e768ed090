import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/f_trade_tick_model.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/f_trade_tick_model.g.dart';

@JsonSerializable()
class FTradeTickModel {
  int current = 0;
  int total = 0;
  List<FTradeTickRecords> records = [];

  /// 是否有下一页
  bool hasNext = false;

  FTradeTickModel();

  factory FTradeTickModel.fromJson(Map<String, dynamic> json) => $FTradeTickModelFromJson(json);

  Map<String, dynamic> toJson() => $FTradeTickModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class FTradeTickRecords {
  /// 交易量
  double tradeVolume = 0.0;

  /// 交易时间 1749016041
  double tradeTime = 0.0;

  /// 时间 "2025-06-04T13:47:21.653312+08:00"
  String time = '';

  /// 交易价格
  double tradePrice = 0.0;

  /// 方向 N|B|S
  String direction = '';

  FTradeTickRecords();

  factory FTradeTickRecords.fromJson(Map<String, dynamic> json) => $FTradeTickRecordsFromJson(json);

  Map<String, dynamic> toJson() => $FTradeTickRecordsToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
