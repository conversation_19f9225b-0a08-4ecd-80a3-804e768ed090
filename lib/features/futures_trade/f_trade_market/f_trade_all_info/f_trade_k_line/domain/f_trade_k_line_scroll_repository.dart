import 'package:gp_stock_app/core/utils/base_screen_data_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_depth_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_tick_model.dart';

class FTradeKLineCache {
  String? instrument;
  //
  FTradeInfoModel? fTradeInfoModel;
  // key=> t_day and k_+1min/5min/15min/30min/60min/day/week/month/year
  Map<String, FTradeKLineModel> kLineMap = {};
  //
  FTradeDepthModel? fTradeDepthModel;
  //
  List<FTradeTickRecords> tickItems = [];

  FTradeKLineCache deepCopy() {
    final copy = FTradeKLineCache();

    copy.instrument = instrument;

    if (fTradeInfoModel != null) {
      copy.fTradeInfoModel = FTradeInfoModel.fromJson(fTradeInfoModel!.toJson());
    }

    copy.kLineMap = {for (var entry in kLineMap.entries) entry.key: FTradeKLineModel.fromJson(entry.value.toJson())};

    if (fTradeDepthModel != null) {
      copy.fTradeDepthModel = FTradeDepthModel.fromJson(fTradeDepthModel!.toJson());
    }

    copy.tickItems = tickItems.map((record) => FTradeTickRecords.fromJson(record.toJson())).toList();

    return copy;
  }
}

/// 设置: 缓存用户最近6条K线页面的数据 有效期持续到应用关闭
class FTradeKLineScrollRepository extends BaseScreenDataRepository<List<FTradeKLineCache>> {
  FTradeKLineScrollRepository._internal();
  static final FTradeKLineScrollRepository _instance = FTradeKLineScrollRepository._internal();
  factory FTradeKLineScrollRepository() => _instance;

  final int _maxCount = 6;

  FTradeKLineCache? loadCacheData(String instrument) {
    if (cachedData == null) {
      return null;
    }
    FTradeKLineCache? kLineCache;
    try {
      kLineCache = cachedData!.firstWhere((p) => p.instrument == instrument);
    } catch (e) {
      kLineCache = null;
    }
    return kLineCache;
  }

  void saveCacheData(String instrument, FTradeKLineCache data) {
    if (cachedData == null) {
      updateCache([data]);
      return;
    }
    late final List<FTradeKLineCache> newList = _deepCopyList(cachedData!);
    int existingIndex = newList.indexWhere((item) => item.instrument == instrument);

    if (existingIndex != -1) {
      newList.removeAt(existingIndex);
    } else if (newList.length >= _maxCount) {
      newList.removeAt(0);
    }
    newList.add(data);
    updateCache(newList);
  }

  List<FTradeKLineCache> _deepCopyList(List<FTradeKLineCache> originalList) {
    return originalList.map((item) => item.deepCopy()).toList();
  }
}
