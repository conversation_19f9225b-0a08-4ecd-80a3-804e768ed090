part of 'f_trade_k_line_cubit.dart';

class FTradeKLineState extends Equatable {
  final FTradeInfoModel? fTradeInfoModel;
  final FTradeStateModel? fTradeStateModel;
  final bool isTimeOrKline;
  final String period;
  // key=> t_day k_day and 1min/5min/15min/30min/60min/day/week/month/year
  final Map<String, FTradeKLineModel> kLineMap;
  final DataStatus kLineDataStatus;
  final FTradeTickModel fTradeTickModel;
  final FTradeDepthModel? fTradeDepthModel;

  FTradeKLineCache toFTradeKLineCache({required String instrument}) {
    var cache = FTradeKLineCache();
    cache.instrument = instrument;
    cache.fTradeInfoModel = fTradeInfoModel;
    cache.fTradeDepthModel = fTradeDepthModel;
    // 更新缓存第一页100条数据
    cache.tickItems = fTradeTickModel.records.sublist(0, min(fTradeTickModel.records.length, 100));
    cache.kLineMap = kLineMap;
    return cache;
  }

  const FTradeKLineState({
    this.fTradeInfoModel,
    this.fTradeStateModel,
    this.isTimeOrKline = true,
    this.period = 'day',
    this.kLineMap = const {},
    this.kLineDataStatus = DataStatus.idle,
    required this.fTradeTickModel,
    this.fTradeDepthModel,
  });

  FTradeKLineState copyWith({
    FTradeInfoModel? fTradeInfoModel,
    FTradeStateModel? fTradeStateModel,
    bool? isTimeOrKline,
    String? period,
    Map<String, FTradeKLineModel>? kLineMap,
    DataStatus? kLineDataStatus,
    FTradeTickModel? fTradeTickModel,
    FTradeDepthModel? fTradeDepthModel,
  }) {
    return FTradeKLineState(
      fTradeInfoModel: fTradeInfoModel ?? this.fTradeInfoModel,
      fTradeStateModel: fTradeStateModel ?? this.fTradeStateModel,
      isTimeOrKline: isTimeOrKline ?? this.isTimeOrKline,
      period: period ?? this.period,
      kLineMap: kLineMap ?? this.kLineMap,
      kLineDataStatus: kLineDataStatus ?? this.kLineDataStatus,
      fTradeTickModel: fTradeTickModel ?? this.fTradeTickModel,
      fTradeDepthModel: fTradeDepthModel ?? this.fTradeDepthModel,
    );
  }

  @override
  List<Object?> get props => [
        fTradeInfoModel,
        fTradeStateModel,
        isTimeOrKline,
        period,
        kLineMap,
        kLineDataStatus,
        fTradeDepthModel,
        fTradeTickModel
      ];
}
