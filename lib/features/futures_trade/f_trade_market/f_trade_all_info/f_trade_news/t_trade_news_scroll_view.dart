import 'package:flutter/material.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';

class TradeNewsScrollView extends StatefulWidget {
  final FTradeMarketType type;
  final FTradeListItemModel data;
  const TradeNewsScrollView({super.key, required this.type, required this.data});

  @override
  State<TradeNewsScrollView> createState() => _TradeNewsScrollViewState();
}

class _TradeNewsScrollViewState extends State<TradeNewsScrollView> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        spacing: 14,
        children: [
          Center(
            child: Text('news'),
          )
        ],
      ),
    );
  }
}

// if (widget.isIndexTrading) ...[
                //   QuotesNewsList(instrument: widget.instrument)
                // ] else ...[
                //   QuotesDistSection(),
                //   QuotesNewsList(instrument: widget.instrument),
                //   QuotesCompanyInfo(),
                // ],