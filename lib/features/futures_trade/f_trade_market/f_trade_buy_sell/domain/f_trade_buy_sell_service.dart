import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/api/network/network_helper.dart';

class FTradeBuySellService {
  /// 创建期货订单
  static Future<(bool?, String?)> fetchCreateFTradeOrderResult({required Map<String, dynamic> queryParameters}) async {
    assert(queryParameters.isNotEmpty, 'queryParameters is empty');
    final Response response = await NetworkProvider().post(
      ApiEndpoints.postFuturesCreateOrder,
      isAuthRequired: true,
      data: queryParameters,
    );
    final responseModel = NetworkHelper.mappingResponseData<bool>(response);
    return (responseModel.data, responseModel.msg);
  }
}
