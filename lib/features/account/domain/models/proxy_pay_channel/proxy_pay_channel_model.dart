import 'package:freezed_annotation/freezed_annotation.dart';

part 'proxy_pay_channel_model.freezed.dart';
part 'proxy_pay_channel_model.g.dart';

@freezed
class ProxyPayChannelModel with _$ProxyPayChannelModel {
  const factory ProxyPayChannelModel({
    int? id,
    String? channelName,
  }) = _ProxyPayChannelModel;

  factory ProxyPayChannelModel.fromJson(Map<String, dynamic> json) => _$ProxyPayChannelModelFromJson(json);
}
