// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'proxy_pay_channel_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProxyPayChannelModel _$ProxyPayChannelModelFromJson(Map<String, dynamic> json) {
  return _ProxyPayChannelModel.fromJson(json);
}

/// @nodoc
mixin _$ProxyPayChannelModel {
  int? get id => throw _privateConstructorUsedError;
  String? get channelName => throw _privateConstructorUsedError;

  /// Serializes this ProxyPayChannelModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProxyPayChannelModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProxyPayChannelModelCopyWith<ProxyPayChannelModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProxyPayChannelModelCopyWith<$Res> {
  factory $ProxyPayChannelModelCopyWith(ProxyPayChannelModel value,
          $Res Function(ProxyPayChannelModel) then) =
      _$ProxyPayChannelModelCopyWithImpl<$Res, ProxyPayChannelModel>;
  @useResult
  $Res call({int? id, String? channelName});
}

/// @nodoc
class _$ProxyPayChannelModelCopyWithImpl<$Res,
        $Val extends ProxyPayChannelModel>
    implements $ProxyPayChannelModelCopyWith<$Res> {
  _$ProxyPayChannelModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProxyPayChannelModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? channelName = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      channelName: freezed == channelName
          ? _value.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProxyPayChannelModelImplCopyWith<$Res>
    implements $ProxyPayChannelModelCopyWith<$Res> {
  factory _$$ProxyPayChannelModelImplCopyWith(_$ProxyPayChannelModelImpl value,
          $Res Function(_$ProxyPayChannelModelImpl) then) =
      __$$ProxyPayChannelModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? id, String? channelName});
}

/// @nodoc
class __$$ProxyPayChannelModelImplCopyWithImpl<$Res>
    extends _$ProxyPayChannelModelCopyWithImpl<$Res, _$ProxyPayChannelModelImpl>
    implements _$$ProxyPayChannelModelImplCopyWith<$Res> {
  __$$ProxyPayChannelModelImplCopyWithImpl(_$ProxyPayChannelModelImpl _value,
      $Res Function(_$ProxyPayChannelModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProxyPayChannelModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? channelName = freezed,
  }) {
    return _then(_$ProxyPayChannelModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      channelName: freezed == channelName
          ? _value.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProxyPayChannelModelImpl implements _ProxyPayChannelModel {
  const _$ProxyPayChannelModelImpl({this.id, this.channelName});

  factory _$ProxyPayChannelModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProxyPayChannelModelImplFromJson(json);

  @override
  final int? id;
  @override
  final String? channelName;

  @override
  String toString() {
    return 'ProxyPayChannelModel(id: $id, channelName: $channelName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProxyPayChannelModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.channelName, channelName) ||
                other.channelName == channelName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, channelName);

  /// Create a copy of ProxyPayChannelModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProxyPayChannelModelImplCopyWith<_$ProxyPayChannelModelImpl>
      get copyWith =>
          __$$ProxyPayChannelModelImplCopyWithImpl<_$ProxyPayChannelModelImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProxyPayChannelModelImplToJson(
      this,
    );
  }
}

abstract class _ProxyPayChannelModel implements ProxyPayChannelModel {
  const factory _ProxyPayChannelModel(
      {final int? id, final String? channelName}) = _$ProxyPayChannelModelImpl;

  factory _ProxyPayChannelModel.fromJson(Map<String, dynamic> json) =
      _$ProxyPayChannelModelImpl.fromJson;

  @override
  int? get id;
  @override
  String? get channelName;

  /// Create a copy of ProxyPayChannelModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProxyPayChannelModelImplCopyWith<_$ProxyPayChannelModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
