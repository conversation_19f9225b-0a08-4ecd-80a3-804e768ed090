// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_wallet_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserWalletModelImpl _$$UserWalletModelImplFromJson(
        Map<String, dynamic> json) =>
    _$UserWalletModelImpl(
      bankCode: json['bankCode'] as String?,
      createTime: json['createTime'] as String?,
      creator: json['creator'] as String?,
      icon: json['icon'] as String?,
      id: (json['id'] as num?)?.toInt(),
      payAddress: json['payAddress'] as String?,
      payTypeCode: json['payTypeCode'] as String?,
      payWayCode: json['payWayCode'] as String?,
      siteId: (json['siteId'] as num?)?.toInt(),
      updateTime: json['updateTime'] as String?,
      updater: json['updater'] as String?,
      userId: (json['userId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$UserWalletModelImplToJson(
        _$UserWalletModelImpl instance) =>
    <String, dynamic>{
      'bankCode': instance.bankCode,
      'createTime': instance.createTime,
      'creator': instance.creator,
      'icon': instance.icon,
      'id': instance.id,
      'payAddress': instance.payAddress,
      'payTypeCode': instance.payTypeCode,
      'payWayCode': instance.payWayCode,
      'siteId': instance.siteId,
      'updateTime': instance.updateTime,
      'updater': instance.updater,
      'userId': instance.userId,
    };
