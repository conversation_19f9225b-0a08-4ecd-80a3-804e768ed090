// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_wallet_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserWalletModel _$UserWalletModelFromJson(Map<String, dynamic> json) {
  return _UserWalletModel.fromJson(json);
}

/// @nodoc
mixin _$UserWalletModel {
  String? get bankCode => throw _privateConstructorUsedError;
  String? get createTime => throw _privateConstructorUsedError;
  String? get creator => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  int? get id => throw _privateConstructorUsedError;
  String? get payAddress => throw _privateConstructorUsedError;
  String? get payTypeCode => throw _privateConstructorUsedError;
  String? get payWayCode => throw _privateConstructorUsedError;
  int? get siteId => throw _privateConstructorUsedError;
  String? get updateTime => throw _privateConstructorUsedError;
  String? get updater => throw _privateConstructorUsedError;
  int? get userId => throw _privateConstructorUsedError;

  /// Serializes this UserWalletModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserWalletModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserWalletModelCopyWith<UserWalletModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserWalletModelCopyWith<$Res> {
  factory $UserWalletModelCopyWith(
          UserWalletModel value, $Res Function(UserWalletModel) then) =
      _$UserWalletModelCopyWithImpl<$Res, UserWalletModel>;
  @useResult
  $Res call(
      {String? bankCode,
      String? createTime,
      String? creator,
      String? icon,
      int? id,
      String? payAddress,
      String? payTypeCode,
      String? payWayCode,
      int? siteId,
      String? updateTime,
      String? updater,
      int? userId});
}

/// @nodoc
class _$UserWalletModelCopyWithImpl<$Res, $Val extends UserWalletModel>
    implements $UserWalletModelCopyWith<$Res> {
  _$UserWalletModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserWalletModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankCode = freezed,
    Object? createTime = freezed,
    Object? creator = freezed,
    Object? icon = freezed,
    Object? id = freezed,
    Object? payAddress = freezed,
    Object? payTypeCode = freezed,
    Object? payWayCode = freezed,
    Object? siteId = freezed,
    Object? updateTime = freezed,
    Object? updater = freezed,
    Object? userId = freezed,
  }) {
    return _then(_value.copyWith(
      bankCode: freezed == bankCode
          ? _value.bankCode
          : bankCode // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      creator: freezed == creator
          ? _value.creator
          : creator // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      payAddress: freezed == payAddress
          ? _value.payAddress
          : payAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      payTypeCode: freezed == payTypeCode
          ? _value.payTypeCode
          : payTypeCode // ignore: cast_nullable_to_non_nullable
              as String?,
      payWayCode: freezed == payWayCode
          ? _value.payWayCode
          : payWayCode // ignore: cast_nullable_to_non_nullable
              as String?,
      siteId: freezed == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int?,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      updater: freezed == updater
          ? _value.updater
          : updater // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserWalletModelImplCopyWith<$Res>
    implements $UserWalletModelCopyWith<$Res> {
  factory _$$UserWalletModelImplCopyWith(_$UserWalletModelImpl value,
          $Res Function(_$UserWalletModelImpl) then) =
      __$$UserWalletModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? bankCode,
      String? createTime,
      String? creator,
      String? icon,
      int? id,
      String? payAddress,
      String? payTypeCode,
      String? payWayCode,
      int? siteId,
      String? updateTime,
      String? updater,
      int? userId});
}

/// @nodoc
class __$$UserWalletModelImplCopyWithImpl<$Res>
    extends _$UserWalletModelCopyWithImpl<$Res, _$UserWalletModelImpl>
    implements _$$UserWalletModelImplCopyWith<$Res> {
  __$$UserWalletModelImplCopyWithImpl(
      _$UserWalletModelImpl _value, $Res Function(_$UserWalletModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserWalletModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankCode = freezed,
    Object? createTime = freezed,
    Object? creator = freezed,
    Object? icon = freezed,
    Object? id = freezed,
    Object? payAddress = freezed,
    Object? payTypeCode = freezed,
    Object? payWayCode = freezed,
    Object? siteId = freezed,
    Object? updateTime = freezed,
    Object? updater = freezed,
    Object? userId = freezed,
  }) {
    return _then(_$UserWalletModelImpl(
      bankCode: freezed == bankCode
          ? _value.bankCode
          : bankCode // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      creator: freezed == creator
          ? _value.creator
          : creator // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      payAddress: freezed == payAddress
          ? _value.payAddress
          : payAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      payTypeCode: freezed == payTypeCode
          ? _value.payTypeCode
          : payTypeCode // ignore: cast_nullable_to_non_nullable
              as String?,
      payWayCode: freezed == payWayCode
          ? _value.payWayCode
          : payWayCode // ignore: cast_nullable_to_non_nullable
              as String?,
      siteId: freezed == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int?,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      updater: freezed == updater
          ? _value.updater
          : updater // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserWalletModelImpl implements _UserWalletModel {
  const _$UserWalletModelImpl(
      {this.bankCode,
      this.createTime,
      this.creator,
      this.icon,
      this.id,
      this.payAddress,
      this.payTypeCode,
      this.payWayCode,
      this.siteId,
      this.updateTime,
      this.updater,
      this.userId});

  factory _$UserWalletModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserWalletModelImplFromJson(json);

  @override
  final String? bankCode;
  @override
  final String? createTime;
  @override
  final String? creator;
  @override
  final String? icon;
  @override
  final int? id;
  @override
  final String? payAddress;
  @override
  final String? payTypeCode;
  @override
  final String? payWayCode;
  @override
  final int? siteId;
  @override
  final String? updateTime;
  @override
  final String? updater;
  @override
  final int? userId;

  @override
  String toString() {
    return 'UserWalletModel(bankCode: $bankCode, createTime: $createTime, creator: $creator, icon: $icon, id: $id, payAddress: $payAddress, payTypeCode: $payTypeCode, payWayCode: $payWayCode, siteId: $siteId, updateTime: $updateTime, updater: $updater, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserWalletModelImpl &&
            (identical(other.bankCode, bankCode) ||
                other.bankCode == bankCode) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.creator, creator) || other.creator == creator) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.payAddress, payAddress) ||
                other.payAddress == payAddress) &&
            (identical(other.payTypeCode, payTypeCode) ||
                other.payTypeCode == payTypeCode) &&
            (identical(other.payWayCode, payWayCode) ||
                other.payWayCode == payWayCode) &&
            (identical(other.siteId, siteId) || other.siteId == siteId) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            (identical(other.updater, updater) || other.updater == updater) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      bankCode,
      createTime,
      creator,
      icon,
      id,
      payAddress,
      payTypeCode,
      payWayCode,
      siteId,
      updateTime,
      updater,
      userId);

  /// Create a copy of UserWalletModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserWalletModelImplCopyWith<_$UserWalletModelImpl> get copyWith =>
      __$$UserWalletModelImplCopyWithImpl<_$UserWalletModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserWalletModelImplToJson(
      this,
    );
  }
}

abstract class _UserWalletModel implements UserWalletModel {
  const factory _UserWalletModel(
      {final String? bankCode,
      final String? createTime,
      final String? creator,
      final String? icon,
      final int? id,
      final String? payAddress,
      final String? payTypeCode,
      final String? payWayCode,
      final int? siteId,
      final String? updateTime,
      final String? updater,
      final int? userId}) = _$UserWalletModelImpl;

  factory _UserWalletModel.fromJson(Map<String, dynamic> json) =
      _$UserWalletModelImpl.fromJson;

  @override
  String? get bankCode;
  @override
  String? get createTime;
  @override
  String? get creator;
  @override
  String? get icon;
  @override
  int? get id;
  @override
  String? get payAddress;
  @override
  String? get payTypeCode;
  @override
  String? get payWayCode;
  @override
  int? get siteId;
  @override
  String? get updateTime;
  @override
  String? get updater;
  @override
  int? get userId;

  /// Create a copy of UserWalletModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserWalletModelImplCopyWith<_$UserWalletModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
