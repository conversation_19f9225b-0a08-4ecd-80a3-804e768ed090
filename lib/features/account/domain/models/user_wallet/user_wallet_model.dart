import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_wallet_model.freezed.dart';
part 'user_wallet_model.g.dart';

@freezed
class UserWalletModel with _$UserWalletModel {
  const factory UserWalletModel({
    String? bankCode,
    String? createTime,
    String? creator,
    String? icon,
    int? id,
    String? payAddress,
    String? payTypeCode,
    String? payWayCode,
    int? siteId,
    String? updateTime,
    String? updater,
    int? userId,
  }) = _UserWalletModel;

  factory UserWalletModel.fromJson(Map<String, dynamic> json) => _$UserWalletModelFromJson(json);
}
