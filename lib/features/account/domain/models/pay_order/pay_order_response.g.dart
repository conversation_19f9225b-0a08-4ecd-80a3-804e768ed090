// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pay_order_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PayOrderResponseImpl _$$PayOrderResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$PayOrderResponseImpl(
      current: (json['current'] as num?)?.toInt(),
      pages: (json['pages'] as num?)?.toInt(),
      records: (json['records'] as List<dynamic>?)
          ?.map((e) => PayOrderRecord.fromJson(e as Map<String, dynamic>))
          .toList(),
      size: (json['size'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$PayOrderResponseImplToJson(
        _$PayOrderResponseImpl instance) =>
    <String, dynamic>{
      'current': instance.current,
      'pages': instance.pages,
      'records': instance.records,
      'size': instance.size,
      'total': instance.total,
    };

_$PayOrderRecordImpl _$$PayOrderRecordImplFromJson(Map<String, dynamic> json) =>
    _$PayOrderRecordImpl(
      channelId: (json['channelId'] as num?)?.toInt(),
      channelName: json['channelName'] as String?,
      createTime: json['createTime'] as String?,
      id: (json['id'] as num?)?.toInt(),
      orderAmount: (json['orderAmount'] as num?)?.toInt(),
      status: (json['status'] as num?)?.toInt(),
      orderNo: json['orderNo'] as String?,
    );

Map<String, dynamic> _$$PayOrderRecordImplToJson(
        _$PayOrderRecordImpl instance) =>
    <String, dynamic>{
      'channelId': instance.channelId,
      'channelName': instance.channelName,
      'createTime': instance.createTime,
      'id': instance.id,
      'orderAmount': instance.orderAmount,
      'status': instance.status,
      'orderNo': instance.orderNo,
    };
