// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pay_order_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PayOrderResponse _$PayOrderResponseFromJson(Map<String, dynamic> json) {
  return _PayOrderResponse.fromJson(json);
}

/// @nodoc
mixin _$PayOrderResponse {
  int? get current => throw _privateConstructorUsedError;
  int? get pages => throw _privateConstructorUsedError;
  List<PayOrderRecord>? get records => throw _privateConstructorUsedError;
  int? get size => throw _privateConstructorUsedError;
  int? get total => throw _privateConstructorUsedError;

  /// Serializes this PayOrderResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PayOrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PayOrderResponseCopyWith<PayOrderResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PayOrderResponseCopyWith<$Res> {
  factory $PayOrderResponseCopyWith(
          PayOrderResponse value, $Res Function(PayOrderResponse) then) =
      _$PayOrderResponseCopyWithImpl<$Res, PayOrderResponse>;
  @useResult
  $Res call(
      {int? current,
      int? pages,
      List<PayOrderRecord>? records,
      int? size,
      int? total});
}

/// @nodoc
class _$PayOrderResponseCopyWithImpl<$Res, $Val extends PayOrderResponse>
    implements $PayOrderResponseCopyWith<$Res> {
  _$PayOrderResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PayOrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = freezed,
    Object? pages = freezed,
    Object? records = freezed,
    Object? size = freezed,
    Object? total = freezed,
  }) {
    return _then(_value.copyWith(
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      pages: freezed == pages
          ? _value.pages
          : pages // ignore: cast_nullable_to_non_nullable
              as int?,
      records: freezed == records
          ? _value.records
          : records // ignore: cast_nullable_to_non_nullable
              as List<PayOrderRecord>?,
      size: freezed == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PayOrderResponseImplCopyWith<$Res>
    implements $PayOrderResponseCopyWith<$Res> {
  factory _$$PayOrderResponseImplCopyWith(_$PayOrderResponseImpl value,
          $Res Function(_$PayOrderResponseImpl) then) =
      __$$PayOrderResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? current,
      int? pages,
      List<PayOrderRecord>? records,
      int? size,
      int? total});
}

/// @nodoc
class __$$PayOrderResponseImplCopyWithImpl<$Res>
    extends _$PayOrderResponseCopyWithImpl<$Res, _$PayOrderResponseImpl>
    implements _$$PayOrderResponseImplCopyWith<$Res> {
  __$$PayOrderResponseImplCopyWithImpl(_$PayOrderResponseImpl _value,
      $Res Function(_$PayOrderResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of PayOrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = freezed,
    Object? pages = freezed,
    Object? records = freezed,
    Object? size = freezed,
    Object? total = freezed,
  }) {
    return _then(_$PayOrderResponseImpl(
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      pages: freezed == pages
          ? _value.pages
          : pages // ignore: cast_nullable_to_non_nullable
              as int?,
      records: freezed == records
          ? _value._records
          : records // ignore: cast_nullable_to_non_nullable
              as List<PayOrderRecord>?,
      size: freezed == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PayOrderResponseImpl implements _PayOrderResponse {
  const _$PayOrderResponseImpl(
      {this.current,
      this.pages,
      final List<PayOrderRecord>? records,
      this.size,
      this.total})
      : _records = records;

  factory _$PayOrderResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$PayOrderResponseImplFromJson(json);

  @override
  final int? current;
  @override
  final int? pages;
  final List<PayOrderRecord>? _records;
  @override
  List<PayOrderRecord>? get records {
    final value = _records;
    if (value == null) return null;
    if (_records is EqualUnmodifiableListView) return _records;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? size;
  @override
  final int? total;

  @override
  String toString() {
    return 'PayOrderResponse(current: $current, pages: $pages, records: $records, size: $size, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PayOrderResponseImpl &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.pages, pages) || other.pages == pages) &&
            const DeepCollectionEquality().equals(other._records, _records) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, current, pages,
      const DeepCollectionEquality().hash(_records), size, total);

  /// Create a copy of PayOrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PayOrderResponseImplCopyWith<_$PayOrderResponseImpl> get copyWith =>
      __$$PayOrderResponseImplCopyWithImpl<_$PayOrderResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PayOrderResponseImplToJson(
      this,
    );
  }
}

abstract class _PayOrderResponse implements PayOrderResponse {
  const factory _PayOrderResponse(
      {final int? current,
      final int? pages,
      final List<PayOrderRecord>? records,
      final int? size,
      final int? total}) = _$PayOrderResponseImpl;

  factory _PayOrderResponse.fromJson(Map<String, dynamic> json) =
      _$PayOrderResponseImpl.fromJson;

  @override
  int? get current;
  @override
  int? get pages;
  @override
  List<PayOrderRecord>? get records;
  @override
  int? get size;
  @override
  int? get total;

  /// Create a copy of PayOrderResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PayOrderResponseImplCopyWith<_$PayOrderResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PayOrderRecord _$PayOrderRecordFromJson(Map<String, dynamic> json) {
  return _PayOrderRecord.fromJson(json);
}

/// @nodoc
mixin _$PayOrderRecord {
  int? get channelId => throw _privateConstructorUsedError;
  String? get channelName => throw _privateConstructorUsedError;
  String? get createTime => throw _privateConstructorUsedError;
  int? get id => throw _privateConstructorUsedError;
  int? get orderAmount => throw _privateConstructorUsedError;
  int? get status => throw _privateConstructorUsedError;
  String? get orderNo => throw _privateConstructorUsedError;

  /// Serializes this PayOrderRecord to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PayOrderRecord
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PayOrderRecordCopyWith<PayOrderRecord> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PayOrderRecordCopyWith<$Res> {
  factory $PayOrderRecordCopyWith(
          PayOrderRecord value, $Res Function(PayOrderRecord) then) =
      _$PayOrderRecordCopyWithImpl<$Res, PayOrderRecord>;
  @useResult
  $Res call(
      {int? channelId,
      String? channelName,
      String? createTime,
      int? id,
      int? orderAmount,
      int? status,
      String? orderNo});
}

/// @nodoc
class _$PayOrderRecordCopyWithImpl<$Res, $Val extends PayOrderRecord>
    implements $PayOrderRecordCopyWith<$Res> {
  _$PayOrderRecordCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PayOrderRecord
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? channelId = freezed,
    Object? channelName = freezed,
    Object? createTime = freezed,
    Object? id = freezed,
    Object? orderAmount = freezed,
    Object? status = freezed,
    Object? orderNo = freezed,
  }) {
    return _then(_value.copyWith(
      channelId: freezed == channelId
          ? _value.channelId
          : channelId // ignore: cast_nullable_to_non_nullable
              as int?,
      channelName: freezed == channelName
          ? _value.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      orderAmount: freezed == orderAmount
          ? _value.orderAmount
          : orderAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      orderNo: freezed == orderNo
          ? _value.orderNo
          : orderNo // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PayOrderRecordImplCopyWith<$Res>
    implements $PayOrderRecordCopyWith<$Res> {
  factory _$$PayOrderRecordImplCopyWith(_$PayOrderRecordImpl value,
          $Res Function(_$PayOrderRecordImpl) then) =
      __$$PayOrderRecordImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? channelId,
      String? channelName,
      String? createTime,
      int? id,
      int? orderAmount,
      int? status,
      String? orderNo});
}

/// @nodoc
class __$$PayOrderRecordImplCopyWithImpl<$Res>
    extends _$PayOrderRecordCopyWithImpl<$Res, _$PayOrderRecordImpl>
    implements _$$PayOrderRecordImplCopyWith<$Res> {
  __$$PayOrderRecordImplCopyWithImpl(
      _$PayOrderRecordImpl _value, $Res Function(_$PayOrderRecordImpl) _then)
      : super(_value, _then);

  /// Create a copy of PayOrderRecord
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? channelId = freezed,
    Object? channelName = freezed,
    Object? createTime = freezed,
    Object? id = freezed,
    Object? orderAmount = freezed,
    Object? status = freezed,
    Object? orderNo = freezed,
  }) {
    return _then(_$PayOrderRecordImpl(
      channelId: freezed == channelId
          ? _value.channelId
          : channelId // ignore: cast_nullable_to_non_nullable
              as int?,
      channelName: freezed == channelName
          ? _value.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      orderAmount: freezed == orderAmount
          ? _value.orderAmount
          : orderAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      orderNo: freezed == orderNo
          ? _value.orderNo
          : orderNo // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PayOrderRecordImpl implements _PayOrderRecord {
  const _$PayOrderRecordImpl(
      {this.channelId,
      this.channelName,
      this.createTime,
      this.id,
      this.orderAmount,
      this.status,
      this.orderNo});

  factory _$PayOrderRecordImpl.fromJson(Map<String, dynamic> json) =>
      _$$PayOrderRecordImplFromJson(json);

  @override
  final int? channelId;
  @override
  final String? channelName;
  @override
  final String? createTime;
  @override
  final int? id;
  @override
  final int? orderAmount;
  @override
  final int? status;
  @override
  final String? orderNo;

  @override
  String toString() {
    return 'PayOrderRecord(channelId: $channelId, channelName: $channelName, createTime: $createTime, id: $id, orderAmount: $orderAmount, status: $status, orderNo: $orderNo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PayOrderRecordImpl &&
            (identical(other.channelId, channelId) ||
                other.channelId == channelId) &&
            (identical(other.channelName, channelName) ||
                other.channelName == channelName) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.orderAmount, orderAmount) ||
                other.orderAmount == orderAmount) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.orderNo, orderNo) || other.orderNo == orderNo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, channelId, channelName,
      createTime, id, orderAmount, status, orderNo);

  /// Create a copy of PayOrderRecord
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PayOrderRecordImplCopyWith<_$PayOrderRecordImpl> get copyWith =>
      __$$PayOrderRecordImplCopyWithImpl<_$PayOrderRecordImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PayOrderRecordImplToJson(
      this,
    );
  }
}

abstract class _PayOrderRecord implements PayOrderRecord {
  const factory _PayOrderRecord(
      {final int? channelId,
      final String? channelName,
      final String? createTime,
      final int? id,
      final int? orderAmount,
      final int? status,
      final String? orderNo}) = _$PayOrderRecordImpl;

  factory _PayOrderRecord.fromJson(Map<String, dynamic> json) =
      _$PayOrderRecordImpl.fromJson;

  @override
  int? get channelId;
  @override
  String? get channelName;
  @override
  String? get createTime;
  @override
  int? get id;
  @override
  int? get orderAmount;
  @override
  int? get status;
  @override
  String? get orderNo;

  /// Create a copy of PayOrderRecord
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PayOrderRecordImplCopyWith<_$PayOrderRecordImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
