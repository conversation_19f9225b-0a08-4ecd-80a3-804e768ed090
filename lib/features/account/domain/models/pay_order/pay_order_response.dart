import 'package:freezed_annotation/freezed_annotation.dart';

part 'pay_order_response.freezed.dart';
part 'pay_order_response.g.dart';

@freezed
class PayOrderResponse with _$PayOrderResponse {
  const factory PayOrderResponse({
    int? current,
    int? pages,
    List<PayOrderRecord>? records,
    int? size,
    int? total,
  }) = _PayOrderResponse;

  factory PayOrderResponse.fromJson(Map<String, dynamic> json) => _$PayOrderResponseFromJson(json);
}

@freezed
class PayOrderRecord with _$PayOrderRecord {
  const factory PayOrderRecord({
    int? channelId,
    String? channelName,
    String? createTime,
    int? id,
    int? orderAmount,
    int? status,
    String? orderNo,
  }) = _PayOrderRecord;

  factory PayOrderRecord.fromJson(Map<String, dynamic> json) => _$PayOrderRecordFromJson(json);
}
