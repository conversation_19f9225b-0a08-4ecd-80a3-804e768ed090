// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'deposit_channel_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DepositChannelModel _$DepositChannelModelFromJson(Map<String, dynamic> json) {
  return _DepositChannelModel.fromJson(json);
}

/// @nodoc
mixin _$DepositChannelModel {
  int? get id => throw _privateConstructorUsedError;
  String? get channelName => throw _privateConstructorUsedError;
  double? get minAmount => throw _privateConstructorUsedError;
  double? get maxAmount => throw _privateConstructorUsedError;
  int? get status => throw _privateConstructorUsedError;
  String? get bankAccount => throw _privateConstructorUsedError;
  String? get bankAddress => throw _privateConstructorUsedError;
  String? get bankCode => throw _privateConstructorUsedError;
  String? get bankRegion => throw _privateConstructorUsedError;
  bool? get isOnlyOffline => throw _privateConstructorUsedError;
  String? get ownerName => throw _privateConstructorUsedError;
  double? get constantGiveRate => throw _privateConstructorUsedError;
  int? get giveGiftType => throw _privateConstructorUsedError;
  int? get giveRuleType => throw _privateConstructorUsedError;
  List<CustomizeGiveRate>? get customizeGiveRate =>
      throw _privateConstructorUsedError;

  /// Serializes this DepositChannelModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DepositChannelModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DepositChannelModelCopyWith<DepositChannelModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DepositChannelModelCopyWith<$Res> {
  factory $DepositChannelModelCopyWith(
          DepositChannelModel value, $Res Function(DepositChannelModel) then) =
      _$DepositChannelModelCopyWithImpl<$Res, DepositChannelModel>;
  @useResult
  $Res call(
      {int? id,
      String? channelName,
      double? minAmount,
      double? maxAmount,
      int? status,
      String? bankAccount,
      String? bankAddress,
      String? bankCode,
      String? bankRegion,
      bool? isOnlyOffline,
      String? ownerName,
      double? constantGiveRate,
      int? giveGiftType,
      int? giveRuleType,
      List<CustomizeGiveRate>? customizeGiveRate});
}

/// @nodoc
class _$DepositChannelModelCopyWithImpl<$Res, $Val extends DepositChannelModel>
    implements $DepositChannelModelCopyWith<$Res> {
  _$DepositChannelModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DepositChannelModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? channelName = freezed,
    Object? minAmount = freezed,
    Object? maxAmount = freezed,
    Object? status = freezed,
    Object? bankAccount = freezed,
    Object? bankAddress = freezed,
    Object? bankCode = freezed,
    Object? bankRegion = freezed,
    Object? isOnlyOffline = freezed,
    Object? ownerName = freezed,
    Object? constantGiveRate = freezed,
    Object? giveGiftType = freezed,
    Object? giveRuleType = freezed,
    Object? customizeGiveRate = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      channelName: freezed == channelName
          ? _value.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String?,
      minAmount: freezed == minAmount
          ? _value.minAmount
          : minAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      maxAmount: freezed == maxAmount
          ? _value.maxAmount
          : maxAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      bankAccount: freezed == bankAccount
          ? _value.bankAccount
          : bankAccount // ignore: cast_nullable_to_non_nullable
              as String?,
      bankAddress: freezed == bankAddress
          ? _value.bankAddress
          : bankAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      bankCode: freezed == bankCode
          ? _value.bankCode
          : bankCode // ignore: cast_nullable_to_non_nullable
              as String?,
      bankRegion: freezed == bankRegion
          ? _value.bankRegion
          : bankRegion // ignore: cast_nullable_to_non_nullable
              as String?,
      isOnlyOffline: freezed == isOnlyOffline
          ? _value.isOnlyOffline
          : isOnlyOffline // ignore: cast_nullable_to_non_nullable
              as bool?,
      ownerName: freezed == ownerName
          ? _value.ownerName
          : ownerName // ignore: cast_nullable_to_non_nullable
              as String?,
      constantGiveRate: freezed == constantGiveRate
          ? _value.constantGiveRate
          : constantGiveRate // ignore: cast_nullable_to_non_nullable
              as double?,
      giveGiftType: freezed == giveGiftType
          ? _value.giveGiftType
          : giveGiftType // ignore: cast_nullable_to_non_nullable
              as int?,
      giveRuleType: freezed == giveRuleType
          ? _value.giveRuleType
          : giveRuleType // ignore: cast_nullable_to_non_nullable
              as int?,
      customizeGiveRate: freezed == customizeGiveRate
          ? _value.customizeGiveRate
          : customizeGiveRate // ignore: cast_nullable_to_non_nullable
              as List<CustomizeGiveRate>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DepositChannelModelImplCopyWith<$Res>
    implements $DepositChannelModelCopyWith<$Res> {
  factory _$$DepositChannelModelImplCopyWith(_$DepositChannelModelImpl value,
          $Res Function(_$DepositChannelModelImpl) then) =
      __$$DepositChannelModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String? channelName,
      double? minAmount,
      double? maxAmount,
      int? status,
      String? bankAccount,
      String? bankAddress,
      String? bankCode,
      String? bankRegion,
      bool? isOnlyOffline,
      String? ownerName,
      double? constantGiveRate,
      int? giveGiftType,
      int? giveRuleType,
      List<CustomizeGiveRate>? customizeGiveRate});
}

/// @nodoc
class __$$DepositChannelModelImplCopyWithImpl<$Res>
    extends _$DepositChannelModelCopyWithImpl<$Res, _$DepositChannelModelImpl>
    implements _$$DepositChannelModelImplCopyWith<$Res> {
  __$$DepositChannelModelImplCopyWithImpl(_$DepositChannelModelImpl _value,
      $Res Function(_$DepositChannelModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of DepositChannelModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? channelName = freezed,
    Object? minAmount = freezed,
    Object? maxAmount = freezed,
    Object? status = freezed,
    Object? bankAccount = freezed,
    Object? bankAddress = freezed,
    Object? bankCode = freezed,
    Object? bankRegion = freezed,
    Object? isOnlyOffline = freezed,
    Object? ownerName = freezed,
    Object? constantGiveRate = freezed,
    Object? giveGiftType = freezed,
    Object? giveRuleType = freezed,
    Object? customizeGiveRate = freezed,
  }) {
    return _then(_$DepositChannelModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      channelName: freezed == channelName
          ? _value.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String?,
      minAmount: freezed == minAmount
          ? _value.minAmount
          : minAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      maxAmount: freezed == maxAmount
          ? _value.maxAmount
          : maxAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      bankAccount: freezed == bankAccount
          ? _value.bankAccount
          : bankAccount // ignore: cast_nullable_to_non_nullable
              as String?,
      bankAddress: freezed == bankAddress
          ? _value.bankAddress
          : bankAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      bankCode: freezed == bankCode
          ? _value.bankCode
          : bankCode // ignore: cast_nullable_to_non_nullable
              as String?,
      bankRegion: freezed == bankRegion
          ? _value.bankRegion
          : bankRegion // ignore: cast_nullable_to_non_nullable
              as String?,
      isOnlyOffline: freezed == isOnlyOffline
          ? _value.isOnlyOffline
          : isOnlyOffline // ignore: cast_nullable_to_non_nullable
              as bool?,
      ownerName: freezed == ownerName
          ? _value.ownerName
          : ownerName // ignore: cast_nullable_to_non_nullable
              as String?,
      constantGiveRate: freezed == constantGiveRate
          ? _value.constantGiveRate
          : constantGiveRate // ignore: cast_nullable_to_non_nullable
              as double?,
      giveGiftType: freezed == giveGiftType
          ? _value.giveGiftType
          : giveGiftType // ignore: cast_nullable_to_non_nullable
              as int?,
      giveRuleType: freezed == giveRuleType
          ? _value.giveRuleType
          : giveRuleType // ignore: cast_nullable_to_non_nullable
              as int?,
      customizeGiveRate: freezed == customizeGiveRate
          ? _value._customizeGiveRate
          : customizeGiveRate // ignore: cast_nullable_to_non_nullable
              as List<CustomizeGiveRate>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DepositChannelModelImpl implements _DepositChannelModel {
  const _$DepositChannelModelImpl(
      {this.id,
      this.channelName,
      this.minAmount,
      this.maxAmount,
      this.status,
      this.bankAccount,
      this.bankAddress,
      this.bankCode,
      this.bankRegion,
      this.isOnlyOffline,
      this.ownerName,
      this.constantGiveRate,
      this.giveGiftType,
      this.giveRuleType,
      final List<CustomizeGiveRate>? customizeGiveRate})
      : _customizeGiveRate = customizeGiveRate;

  factory _$DepositChannelModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$DepositChannelModelImplFromJson(json);

  @override
  final int? id;
  @override
  final String? channelName;
  @override
  final double? minAmount;
  @override
  final double? maxAmount;
  @override
  final int? status;
  @override
  final String? bankAccount;
  @override
  final String? bankAddress;
  @override
  final String? bankCode;
  @override
  final String? bankRegion;
  @override
  final bool? isOnlyOffline;
  @override
  final String? ownerName;
  @override
  final double? constantGiveRate;
  @override
  final int? giveGiftType;
  @override
  final int? giveRuleType;
  final List<CustomizeGiveRate>? _customizeGiveRate;
  @override
  List<CustomizeGiveRate>? get customizeGiveRate {
    final value = _customizeGiveRate;
    if (value == null) return null;
    if (_customizeGiveRate is EqualUnmodifiableListView)
      return _customizeGiveRate;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'DepositChannelModel(id: $id, channelName: $channelName, minAmount: $minAmount, maxAmount: $maxAmount, status: $status, bankAccount: $bankAccount, bankAddress: $bankAddress, bankCode: $bankCode, bankRegion: $bankRegion, isOnlyOffline: $isOnlyOffline, ownerName: $ownerName, constantGiveRate: $constantGiveRate, giveGiftType: $giveGiftType, giveRuleType: $giveRuleType, customizeGiveRate: $customizeGiveRate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DepositChannelModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.channelName, channelName) ||
                other.channelName == channelName) &&
            (identical(other.minAmount, minAmount) ||
                other.minAmount == minAmount) &&
            (identical(other.maxAmount, maxAmount) ||
                other.maxAmount == maxAmount) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.bankAccount, bankAccount) ||
                other.bankAccount == bankAccount) &&
            (identical(other.bankAddress, bankAddress) ||
                other.bankAddress == bankAddress) &&
            (identical(other.bankCode, bankCode) ||
                other.bankCode == bankCode) &&
            (identical(other.bankRegion, bankRegion) ||
                other.bankRegion == bankRegion) &&
            (identical(other.isOnlyOffline, isOnlyOffline) ||
                other.isOnlyOffline == isOnlyOffline) &&
            (identical(other.ownerName, ownerName) ||
                other.ownerName == ownerName) &&
            (identical(other.constantGiveRate, constantGiveRate) ||
                other.constantGiveRate == constantGiveRate) &&
            (identical(other.giveGiftType, giveGiftType) ||
                other.giveGiftType == giveGiftType) &&
            (identical(other.giveRuleType, giveRuleType) ||
                other.giveRuleType == giveRuleType) &&
            const DeepCollectionEquality()
                .equals(other._customizeGiveRate, _customizeGiveRate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      channelName,
      minAmount,
      maxAmount,
      status,
      bankAccount,
      bankAddress,
      bankCode,
      bankRegion,
      isOnlyOffline,
      ownerName,
      constantGiveRate,
      giveGiftType,
      giveRuleType,
      const DeepCollectionEquality().hash(_customizeGiveRate));

  /// Create a copy of DepositChannelModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DepositChannelModelImplCopyWith<_$DepositChannelModelImpl> get copyWith =>
      __$$DepositChannelModelImplCopyWithImpl<_$DepositChannelModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DepositChannelModelImplToJson(
      this,
    );
  }
}

abstract class _DepositChannelModel implements DepositChannelModel {
  const factory _DepositChannelModel(
          {final int? id,
          final String? channelName,
          final double? minAmount,
          final double? maxAmount,
          final int? status,
          final String? bankAccount,
          final String? bankAddress,
          final String? bankCode,
          final String? bankRegion,
          final bool? isOnlyOffline,
          final String? ownerName,
          final double? constantGiveRate,
          final int? giveGiftType,
          final int? giveRuleType,
          final List<CustomizeGiveRate>? customizeGiveRate}) =
      _$DepositChannelModelImpl;

  factory _DepositChannelModel.fromJson(Map<String, dynamic> json) =
      _$DepositChannelModelImpl.fromJson;

  @override
  int? get id;
  @override
  String? get channelName;
  @override
  double? get minAmount;
  @override
  double? get maxAmount;
  @override
  int? get status;
  @override
  String? get bankAccount;
  @override
  String? get bankAddress;
  @override
  String? get bankCode;
  @override
  String? get bankRegion;
  @override
  bool? get isOnlyOffline;
  @override
  String? get ownerName;
  @override
  double? get constantGiveRate;
  @override
  int? get giveGiftType;
  @override
  int? get giveRuleType;
  @override
  List<CustomizeGiveRate>? get customizeGiveRate;

  /// Create a copy of DepositChannelModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DepositChannelModelImplCopyWith<_$DepositChannelModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
