import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class TradeBottomSheetFutureSlTp {
  final BuildContext context;

  final String contractName;
  final String directionText;
  final Color directionColor;
  final double openPrice;
  final double currentPrice;
  final double restNum;
  final String currency;
  final Function(double takeProfitValue, double stopLossValue) onPressedOk;
  final TextEditingController takeProfitController = TextEditingController();
  final TextEditingController stopLossController = TextEditingController();

  TradeBottomSheetFutureSlTp({
    required this.context,
    required this.contractName,
    required this.directionText,
    required this.directionColor,
    required this.openPrice,
    required this.currentPrice,
    required this.restNum,
    required this.currency,
    required this.onPressedOk,
  });

  Future<void> show() async {
    await showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder: (context) {
          return _TradeBottomSheetFutureSlTpContent(
            contractName: contractName,
            directionText: directionText,
            directionColor: directionColor,
            openPrice: openPrice,
            currentPrice: currentPrice,
            currency: currency,
            onPressedSubmit: onPressedOk,
            restNum: restNum,
            takeProfitController: takeProfitController,
            stopLossController: stopLossController,
          );
        });
  }
}

class _TradeBottomSheetFutureSlTpContent extends StatefulWidget {
  final String contractName;
  final String directionText;
  final Color directionColor;
  final double openPrice;
  final double currentPrice;
  final double restNum;
  final String currency;
  final Function(double takeProfitValue, double stopLossValue) onPressedSubmit;
  final TextEditingController takeProfitController;
  final TextEditingController stopLossController;

  const _TradeBottomSheetFutureSlTpContent({
    required this.contractName,
    required this.directionText,
    required this.directionColor,
    required this.openPrice,
    required this.currentPrice,
    required this.restNum,
    required this.currency,
    required this.onPressedSubmit,
    required this.takeProfitController,
    required this.stopLossController,
  });

  @override
  State<_TradeBottomSheetFutureSlTpContent> createState() => _TradeBottomSheetFutureSlTpContentState();
}

class _TradeBottomSheetFutureSlTpContentState extends State<_TradeBottomSheetFutureSlTpContent> {
  bool isSubmitBtnLoading = false;

  @override
  Widget build(BuildContext context) {
    return Padding(
      // viewInsets.bottom 就是键盘高度
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Text('take_profit_stop_loss'.tr(),
                style: context.textTheme.regular.copyWith(color: context.colorTheme.textTitle)),
            SizedBox(height: 16.gw),
            // 合约信息行
            Row(
              children: [
                Expanded(
                    child: Text('marketTitle7'.tr(),
                        style: context.textTheme.regular.fs15.w500.copyWith(color: context.colorTheme.textSecondary))),
                Text(widget.contractName, style: context.textTheme.regular.copyWith(color: Colors.black)),
                const SizedBox(width: 2),
                Text(widget.directionText, style: context.textTheme.regular.copyWith(color: const Color(0xffF5222D))),
              ],
            ),
            SizedBox(height: 8.gw),
            // 开仓价、现价
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('${'open_price'.tr()}(${widget.currency})',
                    style: context.textTheme.regular.fs15.w500.copyWith(color: context.colorTheme.textSecondary)),
                FlipText(widget.openPrice,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w800,
                      color: Theme.of(context).primaryColor,
                    )),
              ],
            ),
            SizedBox(height: 8.gw),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('${'currentPrice'.tr()}(${widget.currency})',
                    style: context.textTheme.regular.fs15.w500.copyWith(color: context.colorTheme.textSecondary)),
                FlipText(widget.currentPrice,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w800,
                      color: Theme.of(context).primaryColor,
                    )),
              ],
            ),
            SizedBox(height: 17.gw),
            // 止盈
            Align(
              alignment: Alignment.centerLeft,
              child: Text('${'take_profit'.tr()}(${widget.currency})',
                  style: context.textTheme.regular.w500.copyWith(color: context.colorTheme.textTitle)),
            ),
            SizedBox(height: 7.gw),
            // 止盈价格
            TextFieldWidget(
              controller: widget.takeProfitController,
              hintText: 'take_profit_price'.tr(),
              textInputType: TextInputType.numberWithOptions(decimal: true),
              hintStyle: context.textTheme.regular.fs16.w500.copyWith(color: context.colorTheme.textTitle),
              suffixIcon: Text(widget.currency,
                  style: context.textTheme.regular.fs15.w500.copyWith(color: context.colorTheme.textSecondary)),
              suffixIconConstraints: BoxConstraints(minWidth: 44.gw, minHeight: 22.gh),
              fillColor: Color(0xffECF0FF),
              showBorderSide: false,
            ),
            const SizedBox(height: 4),
            ValueListenableBuilder(
              valueListenable: widget.takeProfitController,
              builder: (context, TextEditingValue value, _) {
                String profitText = "-";
                Color profitColor = context.colorTheme.textSecondary;
                if (value.text.isNotEmpty) {
                  final tp = double.tryParse(value.text);
                  if (tp != null) {
                    final profit = tp - widget.openPrice;
                    profitText = profit > 0 ? (profit * widget.restNum).toStringAsFixed(2) : "-";
                    profitColor = profit > 0
                        ? const Color(0xFF2AB930) // 绿色
                        : context.colorTheme.textSecondary;
                  }
                }
                return RichText(
                  text: TextSpan(
                    style: context.textTheme.regular.fs12.copyWith(color: context.colorTheme.textSecondary),
                    children: [
                      TextSpan(
                          // '当现价触达${value.text.isEmpty ? "-" : value.text}时，将会触发市价止盈委托卖出目前仓位。\n预期盈利为',
                          text: 'trigger_take_profit_message'
                              .tr(namedArgs: {'price': value.text.isEmpty ? "-" : value.text})),
                      TextSpan(
                        text: profitText,
                        style: context.textTheme.regular.fs12.copyWith(color: profitColor),
                      ),
                      const TextSpan(text: '。'),
                    ],
                  ),
                );
              },
            ),
            const SizedBox(height: 12),
            // 止损
            Text('${'stop_loss'.tr()}(${widget.currency})',
                style: context.textTheme.regular.w500.copyWith(color: context.colorTheme.textTitle)),

            SizedBox(height: 7.gw),

            TextFieldWidget(
              // textFieldHeight: 42.gw,
              controller: widget.stopLossController,
              hintText: 'stop_loss_price'.tr(),
              textInputType: TextInputType.numberWithOptions(decimal: true),
              hintStyle: context.textTheme.regular.fs16.w500.copyWith(color: context.colorTheme.textTitle),
              suffixIcon: Text(widget.currency,
                  style: context.textTheme.regular.fs15.w500.copyWith(color: context.colorTheme.textSecondary)),
              suffixIconConstraints: BoxConstraints(minWidth: 44.gw, minHeight: 22.gh),
              fillColor: Color(0xffECF0FF),
              showBorderSide: false,
            ),
            const SizedBox(height: 4),

            ValueListenableBuilder(
              valueListenable: widget.stopLossController,
              builder: (context, TextEditingValue value, _) {
                String lossText = "-";
                Color lossColor = context.colorTheme.textSecondary;
                if (value.text.isNotEmpty) {
                  final sl = double.tryParse(value.text);
                  if (sl != null) {
                    final loss = sl - widget.openPrice;
                    lossText = loss < 0 ? (loss * widget.restNum).toStringAsFixed(2) : "-";
                    lossColor = loss < 0
                        ? const Color(0xFFF5222D) // 红色
                        : context.colorTheme.textSecondary;
                  }
                }
                return RichText(
                  text: TextSpan(
                    style: context.textTheme.regular.fs12.copyWith(color: context.colorTheme.textSecondary),
                    children: [
                      TextSpan(
                          // '当现价触达${value.text.isEmpty ? "-" : value.text}时，将会触发市价止损委托卖出目前仓位。\n预期亏损为',
                          text: 'trigger_stop_loss_message'
                              .tr(namedArgs: {'price': value.text.isEmpty ? "-" : value.text})),
                      TextSpan(
                        text: lossText,
                        style: context.textTheme.regular.fs12.copyWith(color: lossColor),
                      ),
                      const TextSpan(text: '。'),
                    ],
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
            // 确定按钮

            CustomMaterialButton(
              buttonText: 'ok'.tr(),
              borderColor: context.theme.primaryColor,
              color: context.theme.primaryColor,
              padding: EdgeInsets.zero,
              fontSize: 14,
              borderRadius: 6,
              isLoading: isSubmitBtnLoading,
              onPressed: () async {
                FocusScope.of(context).unfocus();
                final tp = double.tryParse(widget.takeProfitController.text) ?? 0;
                final sl = double.tryParse(widget.stopLossController.text) ?? 0;

                if (tp != 0 || sl != 0) {
                  setState(() {
                    isSubmitBtnLoading = true;
                  });
                  try {
                    await widget.onPressedSubmit.call(tp, sl);
                  } finally {
                    if (mounted) {
                      setState(() {
                        isSubmitBtnLoading = false;
                      });
                    }
                  }
                } else {
                  GPEasyLoading.showToast('please_set_tp_sl_price'.tr()); // 请先设置'止盈/止损'价格
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
