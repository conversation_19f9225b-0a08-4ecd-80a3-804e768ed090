import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_stock_app/features/account/domain/models/pay_order/pay_order_response.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 'pay_order_state.freezed.dart';

@freezed
class PayOrderState with _$PayOrderState {
  const factory PayOrderState({
    @Default(DataStatus.idle) DataStatus payOrdersFetchStatus,
    String? error,
    List<PayOrderRecord>? payOrders,
    int? current,
    int? total,
    String? createDateStart,
    String? createDateEnd,
  }) = _PayOrderState;
}
