import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/account/domain/models/pay_order/pay_order_response.dart';
import 'package:gp_stock_app/features/account/domain/services/bank_service.dart';
import 'package:gp_stock_app/features/account/logic/pay_order/pay_order_state.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class PayOrderCubit extends Cubit<PayOrderState> {
  PayOrderCubit() : super(const PayOrderState());

  final _bankRepository = BankService();
  final int _pageSize = 20;

  Future<void> getPayOrders({bool isLoadMore = false}) async {
    if (state.payOrdersFetchStatus == DataStatus.loading) return;

    final int pageNumber = isLoadMore ? (state.current ?? 0) + 1 : 1;

    if (!isLoadMore) {
      emit(state.copyWith(payOrdersFetchStatus: DataStatus.loading));
    }

    try {
      final result = await _bankRepository.getPayOrders(
        pageNumber: pageNumber,
        pageSize: _pageSize,
        createDateStart: state.createDateStart,
        createDateEnd: state.createDateEnd,
      );

      if (result.isSuccess && result.data != null) {
        final List<PayOrderRecord> parsedRecords = result.data!.records ?? [];

        final List<PayOrderRecord> currentList =
            isLoadMore ? (List<PayOrderRecord>.from(state.payOrders ?? [])..addAll(parsedRecords)) : parsedRecords;

        emit(state.copyWith(
          payOrdersFetchStatus: DataStatus.success,
          payOrders: currentList,
          current: result.data?.current,
          total: result.data?.total,
        ));
      } else {
        emit(state.copyWith(
          payOrdersFetchStatus: DataStatus.failed,
          error: result.error ?? 'failedToLoad'.tr(), // Using translation key
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        payOrdersFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  void updateDateFilter({String? startDate, String? endDate}) {
    emit(state.copyWith(
      createDateStart: startDate,
      createDateEnd: endDate,
    ));
    getPayOrders();
  }

  void clearFilters() {
    emit(state.copyWith(
      createDateStart: null,
      createDateEnd: null,
    ));
    getPayOrders();
  }
}
