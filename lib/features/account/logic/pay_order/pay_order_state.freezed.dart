// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pay_order_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PayOrderState {
  DataStatus get payOrdersFetchStatus => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  List<PayOrderRecord>? get payOrders => throw _privateConstructorUsedError;
  int? get current => throw _privateConstructorUsedError;
  int? get total => throw _privateConstructorUsedError;
  String? get createDateStart => throw _privateConstructorUsedError;
  String? get createDateEnd => throw _privateConstructorUsedError;

  /// Create a copy of PayOrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PayOrderStateCopyWith<PayOrderState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PayOrderStateCopyWith<$Res> {
  factory $PayOrderStateCopyWith(
          PayOrderState value, $Res Function(PayOrderState) then) =
      _$PayOrderStateCopyWithImpl<$Res, PayOrderState>;
  @useResult
  $Res call(
      {DataStatus payOrdersFetchStatus,
      String? error,
      List<PayOrderRecord>? payOrders,
      int? current,
      int? total,
      String? createDateStart,
      String? createDateEnd});
}

/// @nodoc
class _$PayOrderStateCopyWithImpl<$Res, $Val extends PayOrderState>
    implements $PayOrderStateCopyWith<$Res> {
  _$PayOrderStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PayOrderState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? payOrdersFetchStatus = null,
    Object? error = freezed,
    Object? payOrders = freezed,
    Object? current = freezed,
    Object? total = freezed,
    Object? createDateStart = freezed,
    Object? createDateEnd = freezed,
  }) {
    return _then(_value.copyWith(
      payOrdersFetchStatus: null == payOrdersFetchStatus
          ? _value.payOrdersFetchStatus
          : payOrdersFetchStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      payOrders: freezed == payOrders
          ? _value.payOrders
          : payOrders // ignore: cast_nullable_to_non_nullable
              as List<PayOrderRecord>?,
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      createDateStart: freezed == createDateStart
          ? _value.createDateStart
          : createDateStart // ignore: cast_nullable_to_non_nullable
              as String?,
      createDateEnd: freezed == createDateEnd
          ? _value.createDateEnd
          : createDateEnd // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PayOrderStateImplCopyWith<$Res>
    implements $PayOrderStateCopyWith<$Res> {
  factory _$$PayOrderStateImplCopyWith(
          _$PayOrderStateImpl value, $Res Function(_$PayOrderStateImpl) then) =
      __$$PayOrderStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DataStatus payOrdersFetchStatus,
      String? error,
      List<PayOrderRecord>? payOrders,
      int? current,
      int? total,
      String? createDateStart,
      String? createDateEnd});
}

/// @nodoc
class __$$PayOrderStateImplCopyWithImpl<$Res>
    extends _$PayOrderStateCopyWithImpl<$Res, _$PayOrderStateImpl>
    implements _$$PayOrderStateImplCopyWith<$Res> {
  __$$PayOrderStateImplCopyWithImpl(
      _$PayOrderStateImpl _value, $Res Function(_$PayOrderStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of PayOrderState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? payOrdersFetchStatus = null,
    Object? error = freezed,
    Object? payOrders = freezed,
    Object? current = freezed,
    Object? total = freezed,
    Object? createDateStart = freezed,
    Object? createDateEnd = freezed,
  }) {
    return _then(_$PayOrderStateImpl(
      payOrdersFetchStatus: null == payOrdersFetchStatus
          ? _value.payOrdersFetchStatus
          : payOrdersFetchStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      payOrders: freezed == payOrders
          ? _value._payOrders
          : payOrders // ignore: cast_nullable_to_non_nullable
              as List<PayOrderRecord>?,
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      createDateStart: freezed == createDateStart
          ? _value.createDateStart
          : createDateStart // ignore: cast_nullable_to_non_nullable
              as String?,
      createDateEnd: freezed == createDateEnd
          ? _value.createDateEnd
          : createDateEnd // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$PayOrderStateImpl implements _PayOrderState {
  const _$PayOrderStateImpl(
      {this.payOrdersFetchStatus = DataStatus.idle,
      this.error,
      final List<PayOrderRecord>? payOrders,
      this.current,
      this.total,
      this.createDateStart,
      this.createDateEnd})
      : _payOrders = payOrders;

  @override
  @JsonKey()
  final DataStatus payOrdersFetchStatus;
  @override
  final String? error;
  final List<PayOrderRecord>? _payOrders;
  @override
  List<PayOrderRecord>? get payOrders {
    final value = _payOrders;
    if (value == null) return null;
    if (_payOrders is EqualUnmodifiableListView) return _payOrders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? current;
  @override
  final int? total;
  @override
  final String? createDateStart;
  @override
  final String? createDateEnd;

  @override
  String toString() {
    return 'PayOrderState(payOrdersFetchStatus: $payOrdersFetchStatus, error: $error, payOrders: $payOrders, current: $current, total: $total, createDateStart: $createDateStart, createDateEnd: $createDateEnd)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PayOrderStateImpl &&
            (identical(other.payOrdersFetchStatus, payOrdersFetchStatus) ||
                other.payOrdersFetchStatus == payOrdersFetchStatus) &&
            (identical(other.error, error) || other.error == error) &&
            const DeepCollectionEquality()
                .equals(other._payOrders, _payOrders) &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.createDateStart, createDateStart) ||
                other.createDateStart == createDateStart) &&
            (identical(other.createDateEnd, createDateEnd) ||
                other.createDateEnd == createDateEnd));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      payOrdersFetchStatus,
      error,
      const DeepCollectionEquality().hash(_payOrders),
      current,
      total,
      createDateStart,
      createDateEnd);

  /// Create a copy of PayOrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PayOrderStateImplCopyWith<_$PayOrderStateImpl> get copyWith =>
      __$$PayOrderStateImplCopyWithImpl<_$PayOrderStateImpl>(this, _$identity);
}

abstract class _PayOrderState implements PayOrderState {
  const factory _PayOrderState(
      {final DataStatus payOrdersFetchStatus,
      final String? error,
      final List<PayOrderRecord>? payOrders,
      final int? current,
      final int? total,
      final String? createDateStart,
      final String? createDateEnd}) = _$PayOrderStateImpl;

  @override
  DataStatus get payOrdersFetchStatus;
  @override
  String? get error;
  @override
  List<PayOrderRecord>? get payOrders;
  @override
  int? get current;
  @override
  int? get total;
  @override
  String? get createDateStart;
  @override
  String? get createDateEnd;

  /// Create a copy of PayOrderState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PayOrderStateImplCopyWith<_$PayOrderStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
