import 'package:bloc/bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/features/account/domain/repository/bank_repository.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:injectable/injectable.dart';

import '../../domain/models/proxy_pay_channel/proxy_pay_channel_model.dart';
import 'proxy_pay_channel_state.dart';

@injectable
class ProxyPayChannelCubit extends Cubit<ProxyPayChannelState> {
  ProxyPayChannelCubit() : super(const ProxyPayChannelState());

  final _bankService = getIt<BankRepository>();

  Future<void> getProxyPayChannels() async {
    if (state.channelsStatus == DataStatus.loading) return;

    emit(state.copyWith(channelsStatus: DataStatus.loading));

    try {
      final result = await _bankService.getProxyPayChannelList();
      if (result.isSuccess) {
        emit(state.copyWith(
          channelsStatus: DataStatus.success,
          channels: result.data,
          error: null,
        ));
      } else {
        emit(state.copyWith(
          channelsStatus: DataStatus.failed,
          error: result.error,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        channelsStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  void updateSelectedChannel(ProxyPayChannelModel? channel) => emit(state.copyWith(selectedChannel: channel));
}
