import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import '../../domain/models/proxy_pay_channel/proxy_pay_channel_model.dart';

part 'proxy_pay_channel_state.freezed.dart';

@freezed
class ProxyPayChannelState with _$ProxyPayChannelState {
  const factory ProxyPayChannelState({
    @Default(DataStatus.idle) DataStatus channelsStatus,
    List<ProxyPayChannelModel>? channels,
    ProxyPayChannelModel? selectedChannel,
    String? error,
  }) = _ProxyPayChannelState;
}
