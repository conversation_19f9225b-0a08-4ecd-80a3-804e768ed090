// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'proxy_pay_channel_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ProxyPayChannelState {
  DataStatus get channelsStatus => throw _privateConstructorUsedError;
  List<ProxyPayChannelModel>? get channels =>
      throw _privateConstructorUsedError;
  ProxyPayChannelModel? get selectedChannel =>
      throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  /// Create a copy of ProxyPayChannelState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProxyPayChannelStateCopyWith<ProxyPayChannelState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProxyPayChannelStateCopyWith<$Res> {
  factory $ProxyPayChannelStateCopyWith(ProxyPayChannelState value,
          $Res Function(ProxyPayChannelState) then) =
      _$ProxyPayChannelStateCopyWithImpl<$Res, ProxyPayChannelState>;
  @useResult
  $Res call(
      {DataStatus channelsStatus,
      List<ProxyPayChannelModel>? channels,
      ProxyPayChannelModel? selectedChannel,
      String? error});

  $ProxyPayChannelModelCopyWith<$Res>? get selectedChannel;
}

/// @nodoc
class _$ProxyPayChannelStateCopyWithImpl<$Res,
        $Val extends ProxyPayChannelState>
    implements $ProxyPayChannelStateCopyWith<$Res> {
  _$ProxyPayChannelStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProxyPayChannelState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? channelsStatus = null,
    Object? channels = freezed,
    Object? selectedChannel = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      channelsStatus: null == channelsStatus
          ? _value.channelsStatus
          : channelsStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      channels: freezed == channels
          ? _value.channels
          : channels // ignore: cast_nullable_to_non_nullable
              as List<ProxyPayChannelModel>?,
      selectedChannel: freezed == selectedChannel
          ? _value.selectedChannel
          : selectedChannel // ignore: cast_nullable_to_non_nullable
              as ProxyPayChannelModel?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of ProxyPayChannelState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProxyPayChannelModelCopyWith<$Res>? get selectedChannel {
    if (_value.selectedChannel == null) {
      return null;
    }

    return $ProxyPayChannelModelCopyWith<$Res>(_value.selectedChannel!,
        (value) {
      return _then(_value.copyWith(selectedChannel: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProxyPayChannelStateImplCopyWith<$Res>
    implements $ProxyPayChannelStateCopyWith<$Res> {
  factory _$$ProxyPayChannelStateImplCopyWith(_$ProxyPayChannelStateImpl value,
          $Res Function(_$ProxyPayChannelStateImpl) then) =
      __$$ProxyPayChannelStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DataStatus channelsStatus,
      List<ProxyPayChannelModel>? channels,
      ProxyPayChannelModel? selectedChannel,
      String? error});

  @override
  $ProxyPayChannelModelCopyWith<$Res>? get selectedChannel;
}

/// @nodoc
class __$$ProxyPayChannelStateImplCopyWithImpl<$Res>
    extends _$ProxyPayChannelStateCopyWithImpl<$Res, _$ProxyPayChannelStateImpl>
    implements _$$ProxyPayChannelStateImplCopyWith<$Res> {
  __$$ProxyPayChannelStateImplCopyWithImpl(_$ProxyPayChannelStateImpl _value,
      $Res Function(_$ProxyPayChannelStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProxyPayChannelState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? channelsStatus = null,
    Object? channels = freezed,
    Object? selectedChannel = freezed,
    Object? error = freezed,
  }) {
    return _then(_$ProxyPayChannelStateImpl(
      channelsStatus: null == channelsStatus
          ? _value.channelsStatus
          : channelsStatus // ignore: cast_nullable_to_non_nullable
              as DataStatus,
      channels: freezed == channels
          ? _value._channels
          : channels // ignore: cast_nullable_to_non_nullable
              as List<ProxyPayChannelModel>?,
      selectedChannel: freezed == selectedChannel
          ? _value.selectedChannel
          : selectedChannel // ignore: cast_nullable_to_non_nullable
              as ProxyPayChannelModel?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ProxyPayChannelStateImpl implements _ProxyPayChannelState {
  const _$ProxyPayChannelStateImpl(
      {this.channelsStatus = DataStatus.idle,
      final List<ProxyPayChannelModel>? channels,
      this.selectedChannel,
      this.error})
      : _channels = channels;

  @override
  @JsonKey()
  final DataStatus channelsStatus;
  final List<ProxyPayChannelModel>? _channels;
  @override
  List<ProxyPayChannelModel>? get channels {
    final value = _channels;
    if (value == null) return null;
    if (_channels is EqualUnmodifiableListView) return _channels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final ProxyPayChannelModel? selectedChannel;
  @override
  final String? error;

  @override
  String toString() {
    return 'ProxyPayChannelState(channelsStatus: $channelsStatus, channels: $channels, selectedChannel: $selectedChannel, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProxyPayChannelStateImpl &&
            (identical(other.channelsStatus, channelsStatus) ||
                other.channelsStatus == channelsStatus) &&
            const DeepCollectionEquality().equals(other._channels, _channels) &&
            (identical(other.selectedChannel, selectedChannel) ||
                other.selectedChannel == selectedChannel) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, channelsStatus,
      const DeepCollectionEquality().hash(_channels), selectedChannel, error);

  /// Create a copy of ProxyPayChannelState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProxyPayChannelStateImplCopyWith<_$ProxyPayChannelStateImpl>
      get copyWith =>
          __$$ProxyPayChannelStateImplCopyWithImpl<_$ProxyPayChannelStateImpl>(
              this, _$identity);
}

abstract class _ProxyPayChannelState implements ProxyPayChannelState {
  const factory _ProxyPayChannelState(
      {final DataStatus channelsStatus,
      final List<ProxyPayChannelModel>? channels,
      final ProxyPayChannelModel? selectedChannel,
      final String? error}) = _$ProxyPayChannelStateImpl;

  @override
  DataStatus get channelsStatus;
  @override
  List<ProxyPayChannelModel>? get channels;
  @override
  ProxyPayChannelModel? get selectedChannel;
  @override
  String? get error;

  /// Create a copy of ProxyPayChannelState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProxyPayChannelStateImplCopyWith<_$ProxyPayChannelStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
