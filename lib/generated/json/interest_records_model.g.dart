import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/contract/domain/models/interest_records/interest_records_model.dart';

InterestRecordsModel $InterestRecordsModelFromJson(Map<String, dynamic> json) {
  final InterestRecordsModel interestRecordsModel = InterestRecordsModel();
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    interestRecordsModel.current = current;
  }
  final bool? hasNext = jsonConvert.convert<bool>(json['hasNext']);
  if (hasNext != null) {
    interestRecordsModel.hasNext = hasNext;
  }
  final List<InterestRecordModel>? records = (json['records'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<InterestRecordModel>(e) as InterestRecordModel).toList();
  if (records != null) {
    interestRecordsModel.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    interestRecordsModel.total = total;
  }
  return interestRecordsModel;
}

Map<String, dynamic> $InterestRecordsModelToJson(InterestRecordsModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['current'] = entity.current;
  data['hasNext'] = entity.hasNext;
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  return data;
}

extension InterestRecordsModelExtension on InterestRecordsModel {
  InterestRecordsModel copyWith({
    int? current,
    bool? hasNext,
    List<InterestRecordModel>? records,
    int? total,
  }) {
    return InterestRecordsModel()
      ..current = current ?? this.current
      ..hasNext = hasNext ?? this.hasNext
      ..records = records ?? this.records
      ..total = total ?? this.total;
  }
}

InterestRecordModel $InterestRecordModelFromJson(Map<String, dynamic> json) {
  final InterestRecordModel interestRecordModel = InterestRecordModel();
  final double? afterNum = jsonConvert.convert<double>(json['afterNum']);
  if (afterNum != null) {
    interestRecordModel.afterNum = afterNum;
  }
  final double? beforeNum = jsonConvert.convert<double>(json['beforeNum']);
  if (beforeNum != null) {
    interestRecordModel.beforeNum = beforeNum;
  }
  final int? commonAccountId = jsonConvert.convert<int>(json['commonAccountId']);
  if (commonAccountId != null) {
    interestRecordModel.commonAccountId = commonAccountId;
  }
  final int? contractAccountId = jsonConvert.convert<int>(json['contractAccountId']);
  if (contractAccountId != null) {
    interestRecordModel.contractAccountId = contractAccountId;
  }
  final int? contractApplyId = jsonConvert.convert<int>(json['contractApplyId']);
  if (contractApplyId != null) {
    interestRecordModel.contractApplyId = contractApplyId;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    interestRecordModel.createTime = createTime;
  }
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    interestRecordModel.currency = currency;
  }
  final String? extra = jsonConvert.convert<String>(json['extra']);
  if (extra != null) {
    interestRecordModel.extra = extra;
  }
  final int? fromType = jsonConvert.convert<int>(json['fromType']);
  if (fromType != null) {
    interestRecordModel.fromType = fromType;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    interestRecordModel.id = id;
  }
  final double? updateNum = jsonConvert.convert<double>(json['updateNum']);
  if (updateNum != null) {
    interestRecordModel.updateNum = updateNum;
  }
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    interestRecordModel.userId = userId;
  }
  return interestRecordModel;
}

Map<String, dynamic> $InterestRecordModelToJson(InterestRecordModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['afterNum'] = entity.afterNum;
  data['beforeNum'] = entity.beforeNum;
  data['commonAccountId'] = entity.commonAccountId;
  data['contractAccountId'] = entity.contractAccountId;
  data['contractApplyId'] = entity.contractApplyId;
  data['createTime'] = entity.createTime;
  data['currency'] = entity.currency;
  data['extra'] = entity.extra;
  data['fromType'] = entity.fromType;
  data['id'] = entity.id;
  data['updateNum'] = entity.updateNum;
  data['userId'] = entity.userId;
  return data;
}

extension InterestRecordModelExtension on InterestRecordModel {
  InterestRecordModel copyWith({
    double? afterNum,
    double? beforeNum,
    int? commonAccountId,
    int? contractAccountId,
    int? contractApplyId,
    String? createTime,
    String? currency,
    String? extra,
    int? fromType,
    int? id,
    double? updateNum,
    int? userId,
  }) {
    return InterestRecordModel()
      ..afterNum = afterNum ?? this.afterNum
      ..beforeNum = beforeNum ?? this.beforeNum
      ..commonAccountId = commonAccountId ?? this.commonAccountId
      ..contractAccountId = contractAccountId ?? this.contractAccountId
      ..contractApplyId = contractApplyId ?? this.contractApplyId
      ..createTime = createTime ?? this.createTime
      ..currency = currency ?? this.currency
      ..extra = extra ?? this.extra
      ..fromType = fromType ?? this.fromType
      ..id = id ?? this.id
      ..updateNum = updateNum ?? this.updateNum
      ..userId = userId ?? this.userId;
  }
}