import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/profile/domain/models/third_party_channel_entity_list_entity.dart';

ThirdPartyChannelEntityListEntity $ThirdPartyChannelEntityListEntityFromJson(Map<String, dynamic> json) {
  final ThirdPartyChannelEntityListEntity thirdPartyChannelEntityListEntity = ThirdPartyChannelEntityListEntity();
  final List<ThirdPartyChannelEntityListList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ThirdPartyChannelEntityListList>(e) as ThirdPartyChannelEntityListList).toList();
  if (list != null) {
    thirdPartyChannelEntityListEntity.list = list;
  }
  return thirdPartyChannelEntityListEntity;
}

Map<String, dynamic> $ThirdPartyChannelEntityListEntityToJson(ThirdPartyChannelEntityListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension ThirdPartyChannelEntityListEntityExtension on ThirdPartyChannelEntityListEntity {
  ThirdPartyChannelEntityListEntity copyWith({
    List<ThirdPartyChannelEntityListList>? list,
  }) {
    return ThirdPartyChannelEntityListEntity()
      ..list = list ?? this.list;
  }
}

ThirdPartyChannelEntityListList $ThirdPartyChannelEntityListListFromJson(Map<String, dynamic> json) {
  final ThirdPartyChannelEntityListList thirdPartyChannelEntityListList = ThirdPartyChannelEntityListList();
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    thirdPartyChannelEntityListList.currency = currency;
  }
  final String? icon = jsonConvert.convert<String>(json['icon']);
  if (icon != null) {
    thirdPartyChannelEntityListList.icon = icon;
  }
  final List<ThirdPartyChannelEntityListListPayTypeList>? payTypeList = (json['payTypeList'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ThirdPartyChannelEntityListListPayTypeList>(e) as ThirdPartyChannelEntityListListPayTypeList)
      .toList();
  if (payTypeList != null) {
    thirdPartyChannelEntityListList.payTypeList = payTypeList;
  }
  final String? payWayCode = jsonConvert.convert<String>(json['payWayCode']);
  if (payWayCode != null) {
    thirdPartyChannelEntityListList.payWayCode = payWayCode;
  }
  final String? payWayName = jsonConvert.convert<String>(json['payWayName']);
  if (payWayName != null) {
    thirdPartyChannelEntityListList.payWayName = payWayName;
  }
  final bool? recommended = jsonConvert.convert<bool>(json['recommended']);
  if (recommended != null) {
    thirdPartyChannelEntityListList.recommended = recommended;
  }
  return thirdPartyChannelEntityListList;
}

Map<String, dynamic> $ThirdPartyChannelEntityListListToJson(ThirdPartyChannelEntityListList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['currency'] = entity.currency;
  data['icon'] = entity.icon;
  data['payTypeList'] = entity.payTypeList.map((v) => v.toJson()).toList();
  data['payWayCode'] = entity.payWayCode;
  data['payWayName'] = entity.payWayName;
  data['recommended'] = entity.recommended;
  return data;
}

extension ThirdPartyChannelEntityListListExtension on ThirdPartyChannelEntityListList {
  ThirdPartyChannelEntityListList copyWith({
    String? currency,
    String? icon,
    List<ThirdPartyChannelEntityListListPayTypeList>? payTypeList,
    String? payWayCode,
    String? payWayName,
    bool? recommended,
  }) {
    return ThirdPartyChannelEntityListList()
      ..currency = currency ?? this.currency
      ..icon = icon ?? this.icon
      ..payTypeList = payTypeList ?? this.payTypeList
      ..payWayCode = payWayCode ?? this.payWayCode
      ..payWayName = payWayName ?? this.payWayName
      ..recommended = recommended ?? this.recommended;
  }
}

ThirdPartyChannelEntityListListPayTypeList $ThirdPartyChannelEntityListListPayTypeListFromJson(
    Map<String, dynamic> json) {
  final ThirdPartyChannelEntityListListPayTypeList thirdPartyChannelEntityListListPayTypeList = ThirdPartyChannelEntityListListPayTypeList();
  final String? amountList = jsonConvert.convert<String>(json['amountList']);
  if (amountList != null) {
    thirdPartyChannelEntityListListPayTypeList.amountList = amountList;
  }
  final int? amountMaxLimit = jsonConvert.convert<int>(json['amountMaxLimit']);
  if (amountMaxLimit != null) {
    thirdPartyChannelEntityListListPayTypeList.amountMaxLimit = amountMaxLimit;
  }
  final int? amountMinLimit = jsonConvert.convert<int>(json['amountMinLimit']);
  if (amountMinLimit != null) {
    thirdPartyChannelEntityListListPayTypeList.amountMinLimit = amountMinLimit;
  }
  final String? controllerTips = jsonConvert.convert<String>(json['controllerTips']);
  if (controllerTips != null) {
    thirdPartyChannelEntityListListPayTypeList.controllerTips = controllerTips;
  }
  final int? exchangeRate = jsonConvert.convert<int>(json['exchangeRate']);
  if (exchangeRate != null) {
    thirdPartyChannelEntityListListPayTypeList.exchangeRate = exchangeRate;
  }
  final bool? fixedAmount = jsonConvert.convert<bool>(json['fixedAmount']);
  if (fixedAmount != null) {
    thirdPartyChannelEntityListListPayTypeList.fixedAmount = fixedAmount;
  }
  final int? payTypeId = jsonConvert.convert<int>(json['payTypeId']);
  if (payTypeId != null) {
    thirdPartyChannelEntityListListPayTypeList.payTypeId = payTypeId;
  }
  final String? payTypeName = jsonConvert.convert<String>(json['payTypeName']);
  if (payTypeName != null) {
    thirdPartyChannelEntityListListPayTypeList.payTypeName = payTypeName;
  }
  final int? sort = jsonConvert.convert<int>(json['sort']);
  if (sort != null) {
    thirdPartyChannelEntityListListPayTypeList.sort = sort;
  }
  return thirdPartyChannelEntityListListPayTypeList;
}

Map<String, dynamic> $ThirdPartyChannelEntityListListPayTypeListToJson(
    ThirdPartyChannelEntityListListPayTypeList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['amountList'] = entity.amountList;
  data['amountMaxLimit'] = entity.amountMaxLimit;
  data['amountMinLimit'] = entity.amountMinLimit;
  data['controllerTips'] = entity.controllerTips;
  data['exchangeRate'] = entity.exchangeRate;
  data['fixedAmount'] = entity.fixedAmount;
  data['payTypeId'] = entity.payTypeId;
  data['payTypeName'] = entity.payTypeName;
  data['sort'] = entity.sort;
  return data;
}

extension ThirdPartyChannelEntityListListPayTypeListExtension on ThirdPartyChannelEntityListListPayTypeList {
  ThirdPartyChannelEntityListListPayTypeList copyWith({
    String? amountList,
    int? amountMaxLimit,
    int? amountMinLimit,
    String? controllerTips,
    int? exchangeRate,
    bool? fixedAmount,
    int? payTypeId,
    String? payTypeName,
    int? sort,
  }) {
    return ThirdPartyChannelEntityListListPayTypeList()
      ..amountList = amountList ?? this.amountList
      ..amountMaxLimit = amountMaxLimit ?? this.amountMaxLimit
      ..amountMinLimit = amountMinLimit ?? this.amountMinLimit
      ..controllerTips = controllerTips ?? this.controllerTips
      ..exchangeRate = exchangeRate ?? this.exchangeRate
      ..fixedAmount = fixedAmount ?? this.fixedAmount
      ..payTypeId = payTypeId ?? this.payTypeId
      ..payTypeName = payTypeName ?? this.payTypeName
      ..sort = sort ?? this.sort;
  }
}