import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/user.dart';
import 'package:gp_stock_app/features/sign_in/domain/models/login/login_response.dart';


UserModel $UserModelFromJson(Map<String, dynamic> json) {
  final UserModel userModel = UserModel();
  final bool? auth = jsonConvert.convert<bool>(json['auth']);
  if (auth != null) {
    userModel.auth = auth;
  }
  final int? authStatus = jsonConvert.convert<int>(json['authStatus']);
  if (authStatus != null) {
    userModel.authStatus = authStatus;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    userModel.avatar = avatar;
  }
  final String? countryCode = jsonConvert.convert<String>(json['countryCode']);
  if (countryCode != null) {
    userModel.countryCode = countryCode;
  }
  final String? email = jsonConvert.convert<String>(json['email']);
  if (email != null) {
    userModel.email = email;
  }
  final int? fromType = jsonConvert.convert<int>(json['fromType']);
  if (fromType != null) {
    userModel.fromType = fromType;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    userModel.id = id;
  }
  final String? idCard = jsonConvert.convert<String>(json['idCard']);
  if (idCard != null) {
    userModel.idCard = idCard;
  }
  final String? imAccount = jsonConvert.convert<String>(json['imAccount']);
  if (imAccount != null) {
    userModel.imAccount = imAccount;
  }
  final String? inviteCode = jsonConvert.convert<String>(json['inviteCode']);
  if (inviteCode != null) {
    userModel.inviteCode = inviteCode;
  }
  final bool? isPayment = jsonConvert.convert<bool>(json['isPayment']);
  if (isPayment != null) {
    userModel.isPayment = isPayment;
  }
  final int? level = jsonConvert.convert<int>(json['level']);
  if (level != null) {
    userModel.level = level;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    userModel.mobile = mobile;
  }
  final String? nickname = jsonConvert.convert<String>(json['nickname']);
  if (nickname != null) {
    userModel.nickname = nickname;
  }
  final int? pid = jsonConvert.convert<int>(json['pid']);
  if (pid != null) {
    userModel.pid = pid;
  }
  final String? profiles = jsonConvert.convert<String>(json['profiles']);
  if (profiles != null) {
    userModel.profiles = profiles;
  }
  final String? realName = jsonConvert.convert<String>(json['realName']);
  if (realName != null) {
    userModel.realName = realName;
  }
  final int? score = jsonConvert.convert<int>(json['score']);
  if (score != null) {
    userModel.score = score;
  }
  final int? sex = jsonConvert.convert<int>(json['sex']);
  if (sex != null) {
    userModel.sex = sex;
  }
  final bool? status = jsonConvert.convert<bool>(json['status']);
  if (status != null) {
    userModel.status = status;
  }
  final int? tradeStatus = jsonConvert.convert<int>(json['tradeStatus']);
  if (tradeStatus != null) {
    userModel.tradeStatus = tradeStatus;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    userModel.type = type;
  }
  return userModel;
}

Map<String, dynamic> $UserModelToJson(UserModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['auth'] = entity.auth;
  data['authStatus'] = entity.authStatus;
  data['avatar'] = entity.avatar;
  data['countryCode'] = entity.countryCode;
  data['email'] = entity.email;
  data['fromType'] = entity.fromType;
  data['id'] = entity.id;
  data['idCard'] = entity.idCard;
  data['imAccount'] = entity.imAccount;
  data['inviteCode'] = entity.inviteCode;
  data['isPayment'] = entity.isPayment;
  data['level'] = entity.level;
  data['mobile'] = entity.mobile;
  data['nickname'] = entity.nickname;
  data['pid'] = entity.pid;
  data['profiles'] = entity.profiles;
  data['realName'] = entity.realName;
  data['score'] = entity.score;
  data['sex'] = entity.sex;
  data['status'] = entity.status;
  data['tradeStatus'] = entity.tradeStatus;
  data['type'] = entity.type;
  return data;
}

extension UserModelExtension on UserModel {
  UserModel copyWith({
    bool? auth,
    int? authStatus,
    String? avatar,
    String? countryCode,
    String? email,
    int? fromType,
    int? id,
    String? idCard,
    String? imAccount,
    String? inviteCode,
    bool? isPayment,
    int? level,
    String? mobile,
    String? nickname,
    int? pid,
    String? profiles,
    String? realName,
    int? score,
    int? sex,
    bool? status,
    int? tradeStatus,
    int? type,
  }) {
    return UserModel()
      ..auth = auth ?? this.auth
      ..authStatus = authStatus ?? this.authStatus
      ..avatar = avatar ?? this.avatar
      ..countryCode = countryCode ?? this.countryCode
      ..email = email ?? this.email
      ..fromType = fromType ?? this.fromType
      ..id = id ?? this.id
      ..idCard = idCard ?? this.idCard
      ..imAccount = imAccount ?? this.imAccount
      ..inviteCode = inviteCode ?? this.inviteCode
      ..isPayment = isPayment ?? this.isPayment
      ..level = level ?? this.level
      ..mobile = mobile ?? this.mobile
      ..nickname = nickname ?? this.nickname
      ..pid = pid ?? this.pid
      ..profiles = profiles ?? this.profiles
      ..realName = realName ?? this.realName
      ..score = score ?? this.score
      ..sex = sex ?? this.sex
      ..status = status ?? this.status
      ..tradeStatus = tradeStatus ?? this.tradeStatus
      ..type = type ?? this.type;
  }
}