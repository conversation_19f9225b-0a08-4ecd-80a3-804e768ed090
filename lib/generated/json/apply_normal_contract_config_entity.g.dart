import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/contract/apply_normal_contract_config_entity.dart';

ApplyNormalContractConfigEntity $ApplyNormalContractConfigEntityFromJson(Map<String, dynamic> json) {
  final ApplyNormalContractConfigEntity applyNormalContractConfigEntity = ApplyNormalContractConfigEntity();
  final List<ApplyNormalContractConfigAmountList>? amountList = (json['amountList'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<ApplyNormalContractConfigAmountList>(e) as ApplyNormalContractConfigAmountList)
      .toList();
  if (amountList != null) {
    applyNormalContractConfigEntity.amountList = amountList;
  }
  final List<ApplyNormalContractConfigContractConfigMap>? contractConfigMap = (json['contractConfigMap'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ApplyNormalContractConfigContractConfigMap>(e) as ApplyNormalContractConfigContractConfigMap)
      .toList();
  if (contractConfigMap != null) {
    applyNormalContractConfigEntity.contractConfigMap = contractConfigMap;
  }
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    applyNormalContractConfigEntity.currency = currency;
  }
  final int? interestCash = jsonConvert.convert<int>(json['interestCash']);
  if (interestCash != null) {
    applyNormalContractConfigEntity.interestCash = interestCash;
  }
  final List<ApplyNormalContractConfigRuleMap>? ruleMap = (json['ruleMap'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ApplyNormalContractConfigRuleMap>(e) as ApplyNormalContractConfigRuleMap).toList();
  if (ruleMap != null) {
    applyNormalContractConfigEntity.ruleMap = ruleMap;
  }
  final double? useAmount = jsonConvert.convert<double>(json['useAmount']);
  if (useAmount != null) {
    applyNormalContractConfigEntity.useAmount = useAmount;
  }
  return applyNormalContractConfigEntity;
}

Map<String, dynamic> $ApplyNormalContractConfigEntityToJson(ApplyNormalContractConfigEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['amountList'] = entity.amountList.map((v) => v.toJson()).toList();
  data['contractConfigMap'] = entity.contractConfigMap.map((v) => v.toJson()).toList();
  data['currency'] = entity.currency;
  data['interestCash'] = entity.interestCash;
  data['ruleMap'] = entity.ruleMap.map((v) => v.toJson()).toList();
  data['useAmount'] = entity.useAmount;
  return data;
}

extension ApplyNormalContractConfigEntityExtension on ApplyNormalContractConfigEntity {
  ApplyNormalContractConfigEntity copyWith({
    List<ApplyNormalContractConfigAmountList>? amountList,
    List<ApplyNormalContractConfigContractConfigMap>? contractConfigMap,
    String? currency,
    int? interestCash,
    List<ApplyNormalContractConfigRuleMap>? ruleMap,
    double? useAmount,
  }) {
    return ApplyNormalContractConfigEntity()
      ..amountList = amountList ?? this.amountList
      ..contractConfigMap = contractConfigMap ?? this.contractConfigMap
      ..currency = currency ?? this.currency
      ..interestCash = interestCash ?? this.interestCash
      ..ruleMap = ruleMap ?? this.ruleMap
      ..useAmount = useAmount ?? this.useAmount;
  }
}

ApplyNormalContractConfigAmountList $ApplyNormalContractConfigAmountListFromJson(Map<String, dynamic> json) {
  final ApplyNormalContractConfigAmountList applyNormalContractConfigAmountList = ApplyNormalContractConfigAmountList();
  final int? applyAmount = jsonConvert.convert<int>(json['applyAmount']);
  if (applyAmount != null) {
    applyNormalContractConfigAmountList.applyAmount = applyAmount;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    applyNormalContractConfigAmountList.id = id;
  }
  return applyNormalContractConfigAmountList;
}

Map<String, dynamic> $ApplyNormalContractConfigAmountListToJson(ApplyNormalContractConfigAmountList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['applyAmount'] = entity.applyAmount;
  data['id'] = entity.id;
  return data;
}

extension ApplyNormalContractConfigAmountListExtension on ApplyNormalContractConfigAmountList {
  ApplyNormalContractConfigAmountList copyWith({
    int? applyAmount,
    int? id,
  }) {
    return ApplyNormalContractConfigAmountList()
      ..applyAmount = applyAmount ?? this.applyAmount
      ..id = id ?? this.id;
  }
}

ApplyNormalContractConfigContractConfigMap $ApplyNormalContractConfigContractConfigMapFromJson(
    Map<String, dynamic> json) {
  final ApplyNormalContractConfigContractConfigMap applyNormalContractConfigContractConfigMap = ApplyNormalContractConfigContractConfigMap();
  final List<ApplyNormalContractConfigContractConfigMapConfigList>? configList = (json['configList'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ApplyNormalContractConfigContractConfigMapConfigList>(
          e) as ApplyNormalContractConfigContractConfigMapConfigList)
      .toList();
  if (configList != null) {
    applyNormalContractConfigContractConfigMap.configList = configList;
  }
  final int? periodType = jsonConvert.convert<int>(json['periodType']);
  if (periodType != null) {
    applyNormalContractConfigContractConfigMap.periodType = periodType;
  }
  return applyNormalContractConfigContractConfigMap;
}

Map<String, dynamic> $ApplyNormalContractConfigContractConfigMapToJson(
    ApplyNormalContractConfigContractConfigMap entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['configList'] = entity.configList.map((v) => v.toJson()).toList();
  data['periodType'] = entity.periodType;
  return data;
}

extension ApplyNormalContractConfigContractConfigMapExtension on ApplyNormalContractConfigContractConfigMap {
  ApplyNormalContractConfigContractConfigMap copyWith({
    List<ApplyNormalContractConfigContractConfigMapConfigList>? configList,
    int? periodType,
  }) {
    return ApplyNormalContractConfigContractConfigMap()
      ..configList = configList ?? this.configList
      ..periodType = periodType ?? this.periodType;
  }
}

ApplyNormalContractConfigContractConfigMapConfigList $ApplyNormalContractConfigContractConfigMapConfigListFromJson(
    Map<String, dynamic> json) {
  final ApplyNormalContractConfigContractConfigMapConfigList applyNormalContractConfigContractConfigMapConfigList = ApplyNormalContractConfigContractConfigMapConfigList();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    applyNormalContractConfigContractConfigMapConfigList.id = id;
  }
  final double? interestRate = jsonConvert.convert<double>(json['interestRate']);
  if (interestRate != null) {
    applyNormalContractConfigContractConfigMapConfigList.interestRate = interestRate;
  }
  final int? multiple = jsonConvert.convert<int>(json['multiple']);
  if (multiple != null) {
    applyNormalContractConfigContractConfigMapConfigList.multiple = multiple;
  }
  return applyNormalContractConfigContractConfigMapConfigList;
}

Map<String, dynamic> $ApplyNormalContractConfigContractConfigMapConfigListToJson(
    ApplyNormalContractConfigContractConfigMapConfigList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['interestRate'] = entity.interestRate;
  data['multiple'] = entity.multiple;
  return data;
}

extension ApplyNormalContractConfigContractConfigMapConfigListExtension on ApplyNormalContractConfigContractConfigMapConfigList {
  ApplyNormalContractConfigContractConfigMapConfigList copyWith({
    int? id,
    double? interestRate,
    int? multiple,
  }) {
    return ApplyNormalContractConfigContractConfigMapConfigList()
      ..id = id ?? this.id
      ..interestRate = interestRate ?? this.interestRate
      ..multiple = multiple ?? this.multiple;
  }
}

ApplyNormalContractConfigRuleMap $ApplyNormalContractConfigRuleMapFromJson(Map<String, dynamic> json) {
  final ApplyNormalContractConfigRuleMap applyNormalContractConfigRuleMap = ApplyNormalContractConfigRuleMap();
  final int? closeLossRadio = jsonConvert.convert<int>(json['closeLossRadio']);
  if (closeLossRadio != null) {
    applyNormalContractConfigRuleMap.closeLossRadio = closeLossRadio;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    applyNormalContractConfigRuleMap.id = id;
  }
  final String? market = jsonConvert.convert<String>(json['market']);
  if (market != null) {
    applyNormalContractConfigRuleMap.market = market;
  }
  final int? warnLossRadio = jsonConvert.convert<int>(json['warnLossRadio']);
  if (warnLossRadio != null) {
    applyNormalContractConfigRuleMap.warnLossRadio = warnLossRadio;
  }
  return applyNormalContractConfigRuleMap;
}

Map<String, dynamic> $ApplyNormalContractConfigRuleMapToJson(ApplyNormalContractConfigRuleMap entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['closeLossRadio'] = entity.closeLossRadio;
  data['id'] = entity.id;
  data['market'] = entity.market;
  data['warnLossRadio'] = entity.warnLossRadio;
  return data;
}

extension ApplyNormalContractConfigRuleMapExtension on ApplyNormalContractConfigRuleMap {
  ApplyNormalContractConfigRuleMap copyWith({
    int? closeLossRadio,
    int? id,
    String? market,
    int? warnLossRadio,
  }) {
    return ApplyNormalContractConfigRuleMap()
      ..closeLossRadio = closeLossRadio ?? this.closeLossRadio
      ..id = id ?? this.id
      ..market = market ?? this.market
      ..warnLossRadio = warnLossRadio ?? this.warnLossRadio;
  }
}