{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982068c482308970cf72273253e57418cc", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984fe138f4901b98eab7d94fdb8bb40a20", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982068c482308970cf72273253e57418cc", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad7fef4b218469ae655494ea12aba0ee", "name": "Debug-gp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982068c482308970cf72273253e57418cc", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bf710894246f893002845967e5fba04b", "name": "Debug-pre"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982068c482308970cf72273253e57418cc", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9865ee379eecf2ee412b56573bffa2ad05", "name": "Debug-rsyp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982068c482308970cf72273253e57418cc", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983ced715b5ec06fbff55b4447f95653c6", "name": "Debug-yhxt"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa513fd0d79aadb8632a42552fa713a9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982d98f893f2d61eb5ef5abbdafa173de7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa513fd0d79aadb8632a42552fa713a9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984f2e7b39c7e29f8dc93a660212732a48", "name": "Profile-gp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa513fd0d79aadb8632a42552fa713a9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982c380f30ee4b2cbba587c5608c56fd3b", "name": "Profile-pre"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa513fd0d79aadb8632a42552fa713a9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e8faca476a5a3538dc836e9a97ea3682", "name": "Profile-rsyp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa513fd0d79aadb8632a42552fa713a9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a9ffe82f16561d0ed9fa521a7403e991", "name": "Profile-yhxt"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa513fd0d79aadb8632a42552fa713a9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a24b8f5d2c32170bfc8cfa907cdce2da", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa513fd0d79aadb8632a42552fa713a9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cc084f706d7ef4a4a19ced135a751f43", "name": "Release-gp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa513fd0d79aadb8632a42552fa713a9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989c6a724488c044aeb08df8153cece59c", "name": "Release-pre"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa513fd0d79aadb8632a42552fa713a9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e15bb9fa18b528a037123d2b59107c07", "name": "Release-rsyp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa513fd0d79aadb8632a42552fa713a9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9823e03d125a702fb967134422df95c374", "name": "Release-yhxt"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e261d1ec751f194469517c4b45ee01e5", "guid": "bfdfe7dc352907fc980b868725387e985d96629efd0d26c16b2c6ef4eb6dd781"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dedb23c8b61dc7a0770aa2ab03b6d5d8", "guid": "bfdfe7dc352907fc980b868725387e98b6a0d67c36ce125321be5c1f34ab185c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3570d6a390831edd1fe5e27b786efda", "guid": "bfdfe7dc352907fc980b868725387e98b4f63e8141d553cb1c465a41f45b6529"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b70ba557fbcc839a1f4e6dcce25b1ac", "guid": "bfdfe7dc352907fc980b868725387e98dcb05fac706677d1fce36e49eb42252d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982392caa889db280988f6a785e6c90e6f", "guid": "bfdfe7dc352907fc980b868725387e984434481345a22bdefad0c9ef21645360"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802e88465efcddbdd6c91b3c27b6f607a", "guid": "bfdfe7dc352907fc980b868725387e986d1369e631145f904d7fbbf2afda3d84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c05bc7c09159a149c0f7d7123defef3f", "guid": "bfdfe7dc352907fc980b868725387e98c5e6178b83d717d200c84a479076ff0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfefd8ef906821d0185e851617e34ae4", "guid": "bfdfe7dc352907fc980b868725387e988ae3ab1fa479cb48ed9171cc725c87b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fdd1fc310f5a90f5cd09dc8e2c7139d", "guid": "bfdfe7dc352907fc980b868725387e988aeaf66f88c529465b6bc79b0c2dbdc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98968822a4b2c21fa55186cfe44e68a3e0", "guid": "bfdfe7dc352907fc980b868725387e988ca3a243c768b5928a62475bde467e70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d166a8107e59192801cb0cab4a4870a", "guid": "bfdfe7dc352907fc980b868725387e98dd7f5ca9cb0c4f961874027d6d408665"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98096d5fcc3f966593b74d096a91111fd4", "guid": "bfdfe7dc352907fc980b868725387e981c59be2370d9ecac4069db81b413c8ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a86d3d1b9be101ab27b0bef38af6f4e7", "guid": "bfdfe7dc352907fc980b868725387e98c21dd4f8956dcc862dddca2912fb8bb6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893c82f7f0cbd7efe4991580b840d5a99", "guid": "bfdfe7dc352907fc980b868725387e980397802f281901dcfa0aaf5a1501a936", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b90f6e8e3a6498f7e1c755ddc46ffcc", "guid": "bfdfe7dc352907fc980b868725387e98755a9fa94b0221ccf3e7dd5cb73127bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9821a5218bafb5edf4218243b69da82", "guid": "bfdfe7dc352907fc980b868725387e983adc22ca20ad66ffef906eb63c219547", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7538ce95ebbb7912a4b81634a763fc8", "guid": "bfdfe7dc352907fc980b868725387e98fa78d3edf39e02c78842d2e89932c912"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840364628620a87e907668287a6bb0054", "guid": "bfdfe7dc352907fc980b868725387e9863b48c6069f3e7d9525491fb2abb18bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f103c2a7cb3d3b0f2d5888c8ccb2b8d6", "guid": "bfdfe7dc352907fc980b868725387e982f6feae8993210f73905a242a6ca0c39", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983616e84dbe3cc326d3c51d94af9f6820", "guid": "bfdfe7dc352907fc980b868725387e98be6b97a52f2fce26f93f648347405386"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efee50187d2180bd9620a2eacbb7908c", "guid": "bfdfe7dc352907fc980b868725387e988b5265152cb326a9885daf8f49ded057"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d2057cb25743fff9ff521ec656d3daa", "guid": "bfdfe7dc352907fc980b868725387e98a0bff1744331d56fb8db7c44ddd58931"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8a1c0ee47bd1d4dde60db9c2d79d96f", "guid": "bfdfe7dc352907fc980b868725387e98bc19902e1592b0748721d7f7973d6336", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834f56ac29353acb1106a8ffb29765fc8", "guid": "bfdfe7dc352907fc980b868725387e982ccc7a4e98fe9b1595f4cda8517b9e8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ac61d26bbc2725df5d8f0e25f369912", "guid": "bfdfe7dc352907fc980b868725387e987014d5ff2ba530591805e84182269aee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885a1c4a3307f31e55a646735d2490809", "guid": "bfdfe7dc352907fc980b868725387e98b4e59f3f9061247d6b694028f10d9905"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e6b58f30ebac5ca64ec06b9ab1ffc76", "guid": "bfdfe7dc352907fc980b868725387e98c802f43dca53a87555f6d498817000fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98613d8e9aa911b7c4d5f6f0e9f8938aee", "guid": "bfdfe7dc352907fc980b868725387e98d3c1d1377b9a30a955057006ea68e47b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac48285c517ed5c262ffd0abd7cd24e3", "guid": "bfdfe7dc352907fc980b868725387e981f5d5d7b18b9e165152c456b4024ff42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ae9e783d419195d05d295e01c490481", "guid": "bfdfe7dc352907fc980b868725387e98efac4c42bceb3329eb839e8d9aa05109"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4d536a267d2760aa77aaf4664b53e27", "guid": "bfdfe7dc352907fc980b868725387e9877b269a44a698f3f238964a47ed0b9e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f0baceb937adcbbe454eb47963ba7b7", "guid": "bfdfe7dc352907fc980b868725387e98ab28a805d16d28772bd2df5514875130"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98398a1bbb4280bbd192236daad329bc29", "guid": "bfdfe7dc352907fc980b868725387e98c17e285a13dc9be4f9c5bd610f7cf4cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc371f47bc2264d5f27973f1ea5b10ed", "guid": "bfdfe7dc352907fc980b868725387e98b87bb0869abf8fe70d69236975037ac0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820343835177136529782e4155966c294", "guid": "bfdfe7dc352907fc980b868725387e98d738f08b4e740edc49f587c3b78dd1e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e219a449d733424559680bab98c891c", "guid": "bfdfe7dc352907fc980b868725387e98cdbef8595b6005433d3093409f0119d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982402628e15ab08b56392aabce0ef8a24", "guid": "bfdfe7dc352907fc980b868725387e985efd7297c99bbcea39a1d856157f0527"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8c1ff05c9595dd290e120b7fadd0cc0", "guid": "bfdfe7dc352907fc980b868725387e98696c2360da71d06acb375074b80fb9fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ac68dca69e688fe38add2fac648fc25", "guid": "bfdfe7dc352907fc980b868725387e9872e9aaa15cc2e5f0b01e150de560272b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b788ec0cb8075dc01f385518f8a8d32", "guid": "bfdfe7dc352907fc980b868725387e982bf78e0061153b71431031b83a1b020e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881bc35200d70a67eaa0c688b0aa530ce", "guid": "bfdfe7dc352907fc980b868725387e986719d619e38b609411bd819a7a5c46c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858b45e6635935eef2be10454e883752a", "guid": "bfdfe7dc352907fc980b868725387e983dbf081eb4ae83aa3c6ae44acbd34748"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f5d3daf6b1d7247aa05c8bcaff27f46", "guid": "bfdfe7dc352907fc980b868725387e981b28823d1cc80a22acd9da63c5213cc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98564e9b399f86c25d0828f5ca0de39b49", "guid": "bfdfe7dc352907fc980b868725387e98fa2f874192432f1bdce8957bedc87e0a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5591683914243ee6039f436ea01d8af", "guid": "bfdfe7dc352907fc980b868725387e988429a87bc82e836d4cf5cb544468cf77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c3ae97c72639843248165836a52a292", "guid": "bfdfe7dc352907fc980b868725387e9867eac275e4350b3ff3b04fe7ed23b299"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c50038f7771eb2acda759e80d4283a01", "guid": "bfdfe7dc352907fc980b868725387e984aa9132238c020e2827199c52e610254"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852af81e8d47693a7f8b04606a042383c", "guid": "bfdfe7dc352907fc980b868725387e986d30d27e57dae47bc46f1bb9bd85e5f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f78948a4ba2ffc8500780a890a660f0", "guid": "bfdfe7dc352907fc980b868725387e98043c3811c3603bcda755fad237e3ecf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a6d27679ea70d9cae3af00b86e49c44", "guid": "bfdfe7dc352907fc980b868725387e9827cd3e364457505bbb9676945f8381f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985499d81663111ec150b6215d64ab4a9c", "guid": "bfdfe7dc352907fc980b868725387e986692d582e438e59df170a02d375e7efe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d0c98973198cda9e46d8a2c4d39e30b", "guid": "bfdfe7dc352907fc980b868725387e989ca361c5d1711840545ce6e4af6f8bed"}], "guid": "bfdfe7dc352907fc980b868725387e9837df177bb042753edb16fef6fedce551", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9812766419d99e3297f9ea128fd28c25c5", "guid": "bfdfe7dc352907fc980b868725387e98e683f345dab2f140fca77478fa59377c"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e980d13a9ea025f34627933ee687e56d58b", "guid": "bfdfe7dc352907fc980b868725387e98873fc22860c5a6bd8c64f3670bba049a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98fca87294d03da911ea35f78f9c9c4f94", "guid": "bfdfe7dc352907fc980b868725387e980ff8ccf9ef91d0a5d0bd88069971eb45"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d2264a6264d85d2dad14223f20e6d51b", "guid": "bfdfe7dc352907fc980b868725387e98cd9ea8c142227abb5b91a6c2dbda71d6"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98f9925d535409a9905d5b75a716c58b9f", "guid": "bfdfe7dc352907fc980b868725387e9867101ee6d6882a48cabad4d2b69ec9ed"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983f4c0d5acb2f6805b64eb78dcd341532", "guid": "bfdfe7dc352907fc980b868725387e98dea165f940b88d5400d5cd4add932cde"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98f753aa9a6aed8cdd4cb495d404981861", "guid": "bfdfe7dc352907fc980b868725387e987f87d6395ffeb2a19e3b2e3fe7811c6c"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98db77644d96ac498318c1c204bff403ef", "guid": "bfdfe7dc352907fc980b868725387e987738f02bc3b6edcdd19ffc0134fbcbc9"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98dd6326763c887ea88302186c5c95a5a1", "guid": "bfdfe7dc352907fc980b868725387e98ed42d1e66306363c5b31bb95a61544c2"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98402689e610bdde415592a4ae811ec4d3", "guid": "bfdfe7dc352907fc980b868725387e984fe721827c9b8249059eb90b02973e0b"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98403c7921b6cdad460da6a269dc7570b7", "guid": "bfdfe7dc352907fc980b868725387e98f0f75ebbbb21ab124021c8d90776fb10"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98c200d1aa07ae20c7ff060add350f16e4", "guid": "bfdfe7dc352907fc980b868725387e98b7d87db902c2a8e1f0612d36782e9c1e"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ba6899b8618d41051494e22e7baec09b", "guid": "bfdfe7dc352907fc980b868725387e98b03da0c03fabfafa42aedae750b457e7"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b027f71ba43b39a646bad2ac15a3aa05", "guid": "bfdfe7dc352907fc980b868725387e98bab804ca135693c3b778be1eb9eace13"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9800aef5dcca299544965a37b9cbaaf92f", "guid": "bfdfe7dc352907fc980b868725387e980fce15c7e958ff0e857eb0397e643946"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d79f03489f9684b54cda5825358aae00", "guid": "bfdfe7dc352907fc980b868725387e986769acf5f77515d5dfbe8f7e945dbdff"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ae322ba26a5ffb1cde2b2e78a9475bbc", "guid": "bfdfe7dc352907fc980b868725387e98e6ca26666d0f8a62bbd16e120189d2fe"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9832cd2b3e0c900edb021dc16d73d53ec1", "guid": "bfdfe7dc352907fc980b868725387e984d748a2f38ad41209596b0a823ca7a3f"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9839305f5ea085069aae6f47fbc986b477", "guid": "bfdfe7dc352907fc980b868725387e98be8a3a037882d5b119f03dbae9c23155"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9805cc2d8ec7d0427ab7e3a3a0d82000bc", "guid": "bfdfe7dc352907fc980b868725387e982d42248af903e6b6c8c69c37f3045b8b"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98225db1f8ad5bff3dfd197d317d7d8126", "guid": "bfdfe7dc352907fc980b868725387e985d5a9ff65f887f9bc52f0bb29c769858"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98cd522fde02069c532036b6d28e68b63f", "guid": "bfdfe7dc352907fc980b868725387e984a7aee0930081fa70173499514eae52f"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9814fb156d0fc5ff2f04cdc741012182ab", "guid": "bfdfe7dc352907fc980b868725387e98e02d635cfefe2ef9316d965cf1e6d009"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98fc1bff42bae94a1ced5994a847a225e6", "guid": "bfdfe7dc352907fc980b868725387e9854ff31936e3e39150a23202d58c477f8"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9861e2aac6e953444afb5333714b8ec58f", "guid": "bfdfe7dc352907fc980b868725387e98132e9fad8f670066c0277aef71b2c265"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9823488858356bbb5bbb27d18e50306f4b", "guid": "bfdfe7dc352907fc980b868725387e98a67db4834203bbb1ab58d08bb5ee23df"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98c2c36d60c41c7ac0898204c49be20c90", "guid": "bfdfe7dc352907fc980b868725387e98f6f83685a50286805eeaddacfdbaeff0"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9843ab2077d3d434ebb5bb6c8e5ba59650", "guid": "bfdfe7dc352907fc980b868725387e9840d60e6d165ae09d4089ec7748446f08"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e981270d56a221e6e288be2b45a8d0c7085", "guid": "bfdfe7dc352907fc980b868725387e98c83d3b816d5fe1684735982881834846"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b14aecae7c45fd9e0b690e110837af58", "guid": "bfdfe7dc352907fc980b868725387e98e7e15306d51eef86cf1852b5d3fbab61"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98c08db1303e7a249f847a6111a1db753f", "guid": "bfdfe7dc352907fc980b868725387e98068699b9387317b60a7d99cf316583f4"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989067aaf25bc870ed80e9200083f7a63f", "guid": "bfdfe7dc352907fc980b868725387e98c39cd198031083b983d508a0cc16cc26"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98197d0ccfcdb3711fd7b1e5e5c2cb5b29", "guid": "bfdfe7dc352907fc980b868725387e98ff790f674ed42da1d805b25e35612486"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98f607e5a9979318bb669458d857433f93", "guid": "bfdfe7dc352907fc980b868725387e984815a1040066ccffcb11ebc78097aa99"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98204806959a1134fad2dab3d157434e31", "guid": "bfdfe7dc352907fc980b868725387e989da3dd09a90a4724979864ceb47b0a2d"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a4d4ac440af8e1c1f4478076e81e2f27", "guid": "bfdfe7dc352907fc980b868725387e9842fbd52a002892c685dadd85e813b9cd"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e986af3fb98d83ddf04a77f6581d14c9c2d", "guid": "bfdfe7dc352907fc980b868725387e98513687142681d2ac3263b70c9d697a95"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e987c5ef6c9bd663fba129e1dca2b1b5bba", "guid": "bfdfe7dc352907fc980b868725387e98f79bf583a8b407f3a98c17b5365da938"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b8b21001cf4f7564083b61136372a71f", "guid": "bfdfe7dc352907fc980b868725387e98d6163d7215761229e2520102eb5bcd26"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b1068af0b18a60ecaa1943789112b359", "guid": "bfdfe7dc352907fc980b868725387e989fb843adfad2c069ef755f4ca1b621c5"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983dcf11a43cd72abe5017ecb0c62c8a05", "guid": "bfdfe7dc352907fc980b868725387e986cc616a0b13ce8f0cc6e51064c1d0d0b"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983dabc0815b0739114829d015af129f02", "guid": "bfdfe7dc352907fc980b868725387e98bb34925615d47c235a540a0dd29e7f50"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ec1bb7413fc5a239399a3cbbf0629e0f", "guid": "bfdfe7dc352907fc980b868725387e98afd1254ec6c5dc53e421fcf51fe26050"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ba5ad63b591360330c605ac90d5d0f08", "guid": "bfdfe7dc352907fc980b868725387e988700d0c0bff17718391e952fcbcf5208"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9867048b51fda71eb29b6316f44e3149d1", "guid": "bfdfe7dc352907fc980b868725387e98f0491c40b14518463d6f5db648ee4a8e"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ec5757f9519598408e9d866ac17f006a", "guid": "bfdfe7dc352907fc980b868725387e981ffcadc73c2e0e5a0ac13013c55946d1"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e981c94e7e0d333beb6c6260bd90f5ec04c", "guid": "bfdfe7dc352907fc980b868725387e9800aef53376a42f7ca86ec68ace983f5c"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98c290663032538897c97e5944d76493db", "guid": "bfdfe7dc352907fc980b868725387e98675ac716a780dc1c43d3c30e76a5bdc9"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e986d60900e3a0601266d5aedbe9947832e", "guid": "bfdfe7dc352907fc980b868725387e986ff9adb722949fbef1aa40e2242c926e"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e981ae9000efd6933905c93dcae44dca9f1", "guid": "bfdfe7dc352907fc980b868725387e98530ae2bc3db8a27f9d45ebc4380737f0"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98adc1c4332fbf79104d08892294901dc9", "guid": "bfdfe7dc352907fc980b868725387e9884e6c367d9a2b7007c8e99b72ff11b01"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9868c16ad19165524e3fd5c896c90e185d", "guid": "bfdfe7dc352907fc980b868725387e9865a253f64aa34e429ed2c52b6d583d37"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98eeb9bc0fb4e104ccf7fd2d3474094b72", "guid": "bfdfe7dc352907fc980b868725387e98ecac4c586609c7f88bda3018a876cacb"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9821c93e9cefa3935c38e66638d78b7966", "guid": "bfdfe7dc352907fc980b868725387e98009f61db34623a38f84a6f48e1ec35ea"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9851a7d8a0a70ddcba24148e8c9c842f02", "guid": "bfdfe7dc352907fc980b868725387e98baf9842b49550f1c23cc5ba7e4df9972"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c42763e1ac6c085be466c1f4d16d7771", "guid": "bfdfe7dc352907fc980b868725387e98c8fd5e8db3120133f6ddd1bb779f5a45"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98814092dc263067892e8a524828d82526", "guid": "bfdfe7dc352907fc980b868725387e98b84e8a49ce94777445a950fbf4f7d312"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e980ca58fceec81df0e780feab0a15e5905", "guid": "bfdfe7dc352907fc980b868725387e98f292bf993232c0f5995bd9942c33dd18"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98881f1d267660c3d8a2ab1641d79a410a", "guid": "bfdfe7dc352907fc980b868725387e9846c05440998e766112b8ea822229d016"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e988b31a263dcc8c78c25428427dd157228", "guid": "bfdfe7dc352907fc980b868725387e98364428af17993d1ab95ca5e3fcda96a5"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98923858f057b75b284721b366a4ce9fc8", "guid": "bfdfe7dc352907fc980b868725387e9880d6588126ddcef52f757ec6541aab71"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98abb156b8252ec43640ea4c0f922f3e0e", "guid": "bfdfe7dc352907fc980b868725387e98e077e3887af9a9887093ae744889b2e9"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d9be44ae1069325d5d2f480ccdb14c61", "guid": "bfdfe7dc352907fc980b868725387e988aaf4a831e4a7c1da9f308af3686dbf8"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e981308120587bbd24ab6f22ebbd5cddaf4", "guid": "bfdfe7dc352907fc980b868725387e986c7b662e6e75d33288bfe1523bbcd065"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989224d4814e12a2b230c365a7841b4928", "guid": "bfdfe7dc352907fc980b868725387e982b030f2e13575fe95ac0f500cc4ba6a6"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e986412ca0e42226787b58ed929dfc99340", "guid": "bfdfe7dc352907fc980b868725387e988992cffb1d72f55abc45595b9b85598a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9809bb8626082a0423e69f313a098ed81f", "guid": "bfdfe7dc352907fc980b868725387e98dd1b4bd5fee6991282625bf987144a08"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98f1a717e9d24a1bb7e974b9e656b528b9", "guid": "bfdfe7dc352907fc980b868725387e98673367686b1c5beb22f8bb8da772fa29"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98e261fa3ce620d798000d60fdf646a943", "guid": "bfdfe7dc352907fc980b868725387e98b10d8dbe52f1f1197cb084e4e561a487"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9837d1ce79eb981e8421ae1d28efa1d370", "guid": "bfdfe7dc352907fc980b868725387e980d5eb354f73692e029b29aff6a2144f7"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98782a966e0327a7591460fef4dfb55caa", "guid": "bfdfe7dc352907fc980b868725387e982c5ecb20b5b8b1bb6a5830b11ac8e9e6"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e988bea6605b01d1e827a69f0d9d4759718", "guid": "bfdfe7dc352907fc980b868725387e98a8c9c05c54ceb984755a402bf57bf623"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a8d5472ccaa532b1e7887628ac77d42e", "guid": "bfdfe7dc352907fc980b868725387e984673c2f82cf46fe2f7a073d1eabc0785"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98681ebceee81bd1317f406ee9a887b9e9", "guid": "bfdfe7dc352907fc980b868725387e983e78d5c2b633ee47b6754caf14b1d5b8"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98870482973185a800ceeef9e872d6524b", "guid": "bfdfe7dc352907fc980b868725387e98a0e0e52c61700552f2483f789a9e67ea"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989d70a86d8a6048a1d6ef3c18616cc140", "guid": "bfdfe7dc352907fc980b868725387e98b053eb444def602e361b6b37f4c5b218"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b1cf0358be712bf25330e681cf1dd8ba", "guid": "bfdfe7dc352907fc980b868725387e9888f818610513ea7825d1e9ea7ae00f75"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9868cb61c255d94d63146f73c0594732c5", "guid": "bfdfe7dc352907fc980b868725387e98617c263320850db1a93bfafc692a535b"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98cff8b60540ee2be8775c4d19ca91d1dd", "guid": "bfdfe7dc352907fc980b868725387e9872e15909e8ee43b7a1b8012eae83560e"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98466ba27dc79722d60a97be9b8a8b50a5", "guid": "bfdfe7dc352907fc980b868725387e983d73c2e5744890441041b90e50502615"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98be03cff24c6b868dab85d084f4ee0d63", "guid": "bfdfe7dc352907fc980b868725387e98d243d823cf8420b8c360a32236546a7a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9879325a0cbc47c60617e666abe9beb280", "guid": "bfdfe7dc352907fc980b868725387e98fea27fd96358d8b6f9dfcc06860a897e"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9806ba73fbcc8bbd3fdfcf3f77e742b854", "guid": "bfdfe7dc352907fc980b868725387e9847f4fbc8fb2781c76baa6b5240541f7a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e988f78a7f62f1900bf2a01e2dfcc1fce30", "guid": "bfdfe7dc352907fc980b868725387e98189f81cf42d732cedff7cd6566655896"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e988b7d403abc41bb749f2481e98426d44b", "guid": "bfdfe7dc352907fc980b868725387e9821f13d4abc31c2ac920088e31a39f466"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ca8b93ea31bf499474211c881bb9e37c", "guid": "bfdfe7dc352907fc980b868725387e987b6d8a0f1f10a5d5d62298ca597efe56"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d31b994a63cb9b7bb8e4fa99fffd6b68", "guid": "bfdfe7dc352907fc980b868725387e98e6f07dece189b3b00b59427f3b21b19c"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98fc3d407340bef967f7f1ab10ca4284eb", "guid": "bfdfe7dc352907fc980b868725387e98b7599dd7687db36a2c002f03cc6fc971"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e981ae60b52f95ad6fbc24d4cf20aa1a86d", "guid": "bfdfe7dc352907fc980b868725387e9867bb586073d7c821434e2df5074d9d76"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ed9d3fd14bc377c73c38d4efb4383a12", "guid": "bfdfe7dc352907fc980b868725387e988608466a89814a1371ee7890e17a9724"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e981b48a7e7ed433b8b8d527a4fddd8e6ae", "guid": "bfdfe7dc352907fc980b868725387e98169f0227add77ba58e5705ec362b59bc"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983bc2e4e341747e049548d284af7888dd", "guid": "bfdfe7dc352907fc980b868725387e9886374ac3bb08836c6f3867ee46e1dce1"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e986cd38ace050c488372a76fa1bfbae6d0", "guid": "bfdfe7dc352907fc980b868725387e9870436346bdbdcd9a42ee3206b1ebe476"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e987dc8a3ab6c87162e151454ae8f2737ee", "guid": "bfdfe7dc352907fc980b868725387e985bc03bad847393a96b527c0bcf7cc062"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9896f6d49040fed346e662ae2fe92423d8", "guid": "bfdfe7dc352907fc980b868725387e98fb7c530e4cdd96aa8b86a6daa4bb74c6"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98363b381619453023ba09db4a21a71a94", "guid": "bfdfe7dc352907fc980b868725387e9898f17a96f94f57cb02dd7c0121cad518"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9808550d8b695b20148f27846ee7a9f8fa", "guid": "bfdfe7dc352907fc980b868725387e9832a6857e2ea48e015b2f23b466c9d441"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9858a2ef2af253f9468713f9365239ce07", "guid": "bfdfe7dc352907fc980b868725387e983a75fe4f4769c3b47a8265c05c4fdbb4"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98fa9c94706f9ca486253bdf140ae9db38", "guid": "bfdfe7dc352907fc980b868725387e98cf77831121af5baafa7804a9898fc705"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b89bc7b863359739086898d042de6a06", "guid": "bfdfe7dc352907fc980b868725387e981d9f5809d9a8e8785231fd16feb92ce9"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9844032872443d87c9dbf92c4f027cbe20", "guid": "bfdfe7dc352907fc980b868725387e98a2571e4d3c5778f8cd6439fe98ab1587"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e985924ee3dab504e259f1ea342a30b044e", "guid": "bfdfe7dc352907fc980b868725387e986effa30d705c9dbe7a75ca7c553c490b"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9820e1a480bb82ca807f211bca83fc1066", "guid": "bfdfe7dc352907fc980b868725387e989c51c861d25170f0c186a5b9da196eb7"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983c43e45aa8af8d25b0780400e232aa02", "guid": "bfdfe7dc352907fc980b868725387e984c4e618c7f5836c5d6246a9a46ebce63"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98159f0fd519764bf999aef958c034d145", "guid": "bfdfe7dc352907fc980b868725387e983a502ffaee47e12da6a7d7081d3ebf41"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98558d1175ef6f5bf34441f8a048f095bc", "guid": "bfdfe7dc352907fc980b868725387e988ccf233c5e2c766172f0e47eaeea7384"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98abec93322fec7947b3b380a80d4a3a89", "guid": "bfdfe7dc352907fc980b868725387e9828c0fede8c58909e60f2c82b74f6de69"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98647066ef2352e2c6505553f25fe14327", "guid": "bfdfe7dc352907fc980b868725387e98aa448f332510bb2c6de46a8e5169d6b1"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9851af790414933cf95fc63c47c066bb93", "guid": "bfdfe7dc352907fc980b868725387e9804f2fbd2d0f73ecab683c918d6b9f61d"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9879c58687399570380a8ee8ba555b6889", "guid": "bfdfe7dc352907fc980b868725387e98b090f30577d570432ed43138eb2b0f40"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98dcd7b727c85535cffc29c2304e12b4b1", "guid": "bfdfe7dc352907fc980b868725387e98af82e5cb474b6e4a12b9b6067bfc494a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b8964e8811428ec4b3a552197cc542f6", "guid": "bfdfe7dc352907fc980b868725387e98e5aed47f113c24c8998e98b767efa694"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9801d9a5c331774b802e060015cde64eb4", "guid": "bfdfe7dc352907fc980b868725387e98434323b355c556c47e3628a8f8d7048a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a25d47f38719ad6b5c26c7a5f6268a43", "guid": "bfdfe7dc352907fc980b868725387e9886e95701d58e9e33d86e4d6a34704a12"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9881ee577f89b9539e8ebe5d7f270051e0", "guid": "bfdfe7dc352907fc980b868725387e980deed748d052beeadd113801a8461642"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98add427b74b9f69389f1ec4ef85bdd972", "guid": "bfdfe7dc352907fc980b868725387e98b6419a7c831a6cd1df173dde8095815e"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98020a2532d592cf18dc64b3186ab69066", "guid": "bfdfe7dc352907fc980b868725387e9853b20b4031a11f2fa8663b30821401cc"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98c7fc2e7b6911b934eb3af5a02c66ce64", "guid": "bfdfe7dc352907fc980b868725387e98fcc6a787d1c4264ab1252fafd6204589"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e985ad67c39ab82d833af16f98b01e6b486", "guid": "bfdfe7dc352907fc980b868725387e98bcaf33c08fc142382b087fb184517b16"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a4a668426419b633802ad0d2dc9d18b4", "guid": "bfdfe7dc352907fc980b868725387e988c7c23111cdfc045a9a9a4250587455c"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98fa9ee33fcdf9b52a74beeb1f2305477d", "guid": "bfdfe7dc352907fc980b868725387e98ce1a086b05f158dc84f6371e87fdea6c"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e984a4643abb9d470d7ccaab8dc7b45b768", "guid": "bfdfe7dc352907fc980b868725387e9886aebb8053461e7480601a5078613ca5"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a2f11c5f04cda8f46c2e230f20fac55a", "guid": "bfdfe7dc352907fc980b868725387e983f237352ac4234445cd9ca6c0abedcac"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d8f3a4c205f6fa9199ffc51dd24a4553", "guid": "bfdfe7dc352907fc980b868725387e98f11354395bf1ff50ad70285ad1918795"}], "guid": "bfdfe7dc352907fc980b868725387e9818430fcc81d140015a49fb0ef50b785b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cb32ce58c90a2c90af0fad8644e516ec", "guid": "bfdfe7dc352907fc980b868725387e982830ef8d692a8144ab169424e2a5f8f3"}], "guid": "bfdfe7dc352907fc980b868725387e98b56f16f1a8a52a040d8ed5e04ea2ef0f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98d959394de94b004da2395f9f120c76a4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98459ecbbef4dbbe8a07a0c87bad0e0d1b", "name": "libwebp", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982988ca927fde17a51bcb980424f8e9e7", "name": "libwebp.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-gp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-pre", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-rsyp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-yhxt", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-gp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-pre", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-rsyp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-yhxt", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-gp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-pre", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-rsyp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-yhxt", "provisioningStyle": 1}], "type": "standard"}