{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b2c4daeec004560f553a27f11f7eb619", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98169537ca90e06cf6a2d58e14b3d06a76", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b2c4daeec004560f553a27f11f7eb619", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fdc3d67193b4d9a09906b20a21afb80b", "name": "Debug-gp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b2c4daeec004560f553a27f11f7eb619", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98836f949e8c81270b74ab8e95e34baab1", "name": "Debug-pre"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b2c4daeec004560f553a27f11f7eb619", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98776d2d955a1676916314aa3693bf17bb", "name": "Debug-rsyp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b2c4daeec004560f553a27f11f7eb619", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ca8e4a126c2085a8a5df35ce93fcc05a", "name": "Debug-yhxt"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ba52e9a0986a5f4460fde6bd6a6be3c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982f8179d775607cfacc964af3a9146f94", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ba52e9a0986a5f4460fde6bd6a6be3c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98db1a3cbb0c8db720106e7f4b8b77b9d4", "name": "Profile-gp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ba52e9a0986a5f4460fde6bd6a6be3c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986d48ae2c3bb23d993202b99c5ca65e1e", "name": "Profile-pre"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ba52e9a0986a5f4460fde6bd6a6be3c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9820282cf224b9b5ad341040c0f38ceefb", "name": "Profile-rsyp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ba52e9a0986a5f4460fde6bd6a6be3c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dff19b3400559992017b73dab1637fe0", "name": "Profile-yhxt"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ba52e9a0986a5f4460fde6bd6a6be3c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cb759083635bd84c52d199de7550d9bd", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ba52e9a0986a5f4460fde6bd6a6be3c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e66174b93d4999cc66e3bc1844852c05", "name": "Release-gp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ba52e9a0986a5f4460fde6bd6a6be3c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d78035261eeb117280d23242aa459be9", "name": "Release-pre"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ba52e9a0986a5f4460fde6bd6a6be3c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983f8de85c117c5cc008ceb06516dad47d", "name": "Release-rsyp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ba52e9a0986a5f4460fde6bd6a6be3c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984db7f48c4961c26f99cbe8e820dd4682", "name": "Release-yhxt"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a8bb742337debe80396a1ec39480fbc6", "guid": "bfdfe7dc352907fc980b868725387e98b3460a9a2766b5b65fccdad0ad92b5d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baeb60563b882961b4ad64783fafc1e5", "guid": "bfdfe7dc352907fc980b868725387e989cbdd18829f475b8a384b0a22d0b9a54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b0f4269e4c9159f7afa185dc3884233", "guid": "bfdfe7dc352907fc980b868725387e98854293902be253ffd170419dcb3f689a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856a208e1f547ca4d7fbfda2c74b428ee", "guid": "bfdfe7dc352907fc980b868725387e98799d39babaf68b7fdfb1e7e53a658feb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cdaec702b1af622091d2bbce4eebe8d", "guid": "bfdfe7dc352907fc980b868725387e980edfe6105ceed15590b0d6bbcc83933e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b2342c8cfbe805dc0c62ccbf1c17895", "guid": "bfdfe7dc352907fc980b868725387e98f16dd1a0e6c0ef26e5c3cac8cacbe24d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9fb4eb251007a2016d9e9ec2bd3a999", "guid": "bfdfe7dc352907fc980b868725387e98db015a836bb8a6be5707c9b6b3deb612", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984121d0beacba03e53d4082a004e992c0", "guid": "bfdfe7dc352907fc980b868725387e98afd7441e77100fa5bb07f72fee1992ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885226c5b28ebf9615d083aa35be09896", "guid": "bfdfe7dc352907fc980b868725387e98a34333afe5c515c459230386106ff4b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983428c18bfc598c390e20027b367c3746", "guid": "bfdfe7dc352907fc980b868725387e988afc1523d3181a93199a2cb9011173c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841d0dfe474efea8579d80659e0aee957", "guid": "bfdfe7dc352907fc980b868725387e98c8591eec6a8ab3625e3a48dcfcaf97b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d414d51ba292a67fb78da2cd2f177afb", "guid": "bfdfe7dc352907fc980b868725387e9820d0f42d651face4268cf711075c305a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890494a37968f9096e9008ee16e4d6324", "guid": "bfdfe7dc352907fc980b868725387e983d60f347937013a203dfd40d6385016c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b619de2d2e42a93be079239f5ece2e99", "guid": "bfdfe7dc352907fc980b868725387e98b3b631a23cbbeca4aca8d97eed748ef5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4540cb12f654451d65a41d84a1387b3", "guid": "bfdfe7dc352907fc980b868725387e98407e4252864119be492fc195f8feef97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb01088792c3d6e69d5e6d381a3dfa95", "guid": "bfdfe7dc352907fc980b868725387e98a493477bc84ab7ee44839b65ad4ab0f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b248350c1e4aec272a598d183017cfb", "guid": "bfdfe7dc352907fc980b868725387e98c469d15f3f116ac6f33b90c48eefa73d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c2ccd1e60332f9e54f1b17709bc975", "guid": "bfdfe7dc352907fc980b868725387e98cbf4dce8bd1bf1e7502a80aea8c5401c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcc25ee753bab369112853721aa3519a", "guid": "bfdfe7dc352907fc980b868725387e98ae0e2188e739c3c5f0819bdac2ec6bbd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851c5af45b851668b62ba2fa61a781724", "guid": "bfdfe7dc352907fc980b868725387e98c3cb368d761afbbbe81f1944e2f590ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daf30a102bbfb1abbf2b5ce05320c29e", "guid": "bfdfe7dc352907fc980b868725387e981306844b65c34d71c74b28062c4670c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a26e26c1c4de797f3c7b941272b71b1e", "guid": "bfdfe7dc352907fc980b868725387e989b2c2ffce8643ed5f0fdec2bc7168d63", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983004b681b709f00f81a797265a2454b5", "guid": "bfdfe7dc352907fc980b868725387e9892465905ad65c9d7f4b446eddd0c5bb0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896d86896c77a2d5becec12869a1bdda0", "guid": "bfdfe7dc352907fc980b868725387e98e8084349d3f8ef48e7d3137f966b4ee2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd5d3ed44e3288c951479a07418de986", "guid": "bfdfe7dc352907fc980b868725387e980d0d9be2719bb9fab3b9c115f33b7622", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab2c6d0c04b3e6119133cf540ec11c27", "guid": "bfdfe7dc352907fc980b868725387e9842e8e2f2776186f37676efec614ed1cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b619fcd37217d55fc64ddcbf8630376", "guid": "bfdfe7dc352907fc980b868725387e9802f2da20817bb4569b5b6d23e1a20661", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e1012476c46444ef05a7a55e35b38d8", "guid": "bfdfe7dc352907fc980b868725387e98d32353df3607c4b7d93fdddb4569919f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f40dec7996e6733698346f486dfb9123", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989b46a8b0eb44218ffa6eaa009481d29d", "guid": "bfdfe7dc352907fc980b868725387e9864e1716493fca8d889f241b2b480605b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98990dec9a092ca6987a374e260a4de645", "guid": "bfdfe7dc352907fc980b868725387e98022e960565f3defe5565626a0afde060"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ea64730d79ae7201d86ed77fa2a2a44", "guid": "bfdfe7dc352907fc980b868725387e9874b6243f46ea46eaa33227778ec6a74d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814cad680ad02ec7c9774048a1e5c7a81", "guid": "bfdfe7dc352907fc980b868725387e98db9e510ad3c4d171e9cfeac87aa4b41e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883de4dfcd77865632f61f3e748aeb293", "guid": "bfdfe7dc352907fc980b868725387e98c733505355e944c1f04c92614707303c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876211bb1f5feba001e8abeacd0f57245", "guid": "bfdfe7dc352907fc980b868725387e989547f3a5aec3dc9db755e01a422a0412"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab3c1b5457a6401ab384dc169696bfae", "guid": "bfdfe7dc352907fc980b868725387e98f3c24cac2fe6da7d7d616e9896ee53c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5ba76af00fc4dc51720820d983ca596", "guid": "bfdfe7dc352907fc980b868725387e98fb55dbc8693949b5042c6bbbd8887520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98010b7db624a3722648ff87da9e0e334c", "guid": "bfdfe7dc352907fc980b868725387e985ae7f8abfd6c509f9d13a9e9daa76872"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de4878a72e4eeafb9a8efc2309faceca", "guid": "bfdfe7dc352907fc980b868725387e98320c8ad0b2a333b958e1cceb26312b95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835fb1475811e13610fe558ec19d2d55a", "guid": "bfdfe7dc352907fc980b868725387e980b3675c96784bbf88f1c5ea80fa6d56d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c81dd14358c301a7f44298e4cae2a473", "guid": "bfdfe7dc352907fc980b868725387e98843411dd4ddad67226504a66bdfd724b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5768940e74c6bc9e9dfb864779ba425", "guid": "bfdfe7dc352907fc980b868725387e982794e4f2069b77e470cb7a5d2212295e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800c989626f5396bf556c39f405f15070", "guid": "bfdfe7dc352907fc980b868725387e984aa7a3e81d832b0261266b895958ec74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812846ecedd76e3c358168bbda0395cce", "guid": "bfdfe7dc352907fc980b868725387e98667c97fa52eef07f44d47a6d232164a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dc92d6d66ca1877d8b1e16172cc9cfc", "guid": "bfdfe7dc352907fc980b868725387e981c6b9d6aff3c187c6f5d447d0d5d1ad6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825bab6b520d6e62cc3698b2b9bcb557b", "guid": "bfdfe7dc352907fc980b868725387e98b797686779d5ed1377a6bf171325e071"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2380338d074f588ffd990b9776a69e4", "guid": "bfdfe7dc352907fc980b868725387e9839333f0a0436f76314c336f15e1ad181"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5c8e30fbb5340d0d4af31e210ec6417", "guid": "bfdfe7dc352907fc980b868725387e986858743a441a88d0e20cfecb891e62c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983289642859dbfdb1b7895caf5380c266", "guid": "bfdfe7dc352907fc980b868725387e98f7d14788a0fbbf771ec3bf80a2fa7e19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cebb2ec2408caf9615bffefc6812ddf0", "guid": "bfdfe7dc352907fc980b868725387e98f243e703e89265a0e17dc19e8b3e6f2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98062a311df141a65690f996e4a171d46a", "guid": "bfdfe7dc352907fc980b868725387e982cbea3b9ea45b68bdea8cd20cbbf3e4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833ab02881bf78b8e7107e10336be8bce", "guid": "bfdfe7dc352907fc980b868725387e9828b3de6e42a169d617330deacfbbad7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a666d1fafaccdbbb2273e2566cfec15", "guid": "bfdfe7dc352907fc980b868725387e9887d8ca6cf60348ab738adf2d32be650f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3960b2996eff3b1fadb4659178ce8b4", "guid": "bfdfe7dc352907fc980b868725387e98d21c893c94445b678463025f8dbcc9fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880edf2944b61c492b8786a069141ddf3", "guid": "bfdfe7dc352907fc980b868725387e98e0bd88c05ff6d53db8ab304d617b5194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828e018e707d03581fc37a7995cc1ba95", "guid": "bfdfe7dc352907fc980b868725387e984e296870bf2129340a4069dd0020a43d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca3135804124f586264a5000a7dd49ff", "guid": "bfdfe7dc352907fc980b868725387e983d2971dd6181a7144aedac8cfd0201c2"}], "guid": "bfdfe7dc352907fc980b868725387e984737a3e74e67e0a936b108abf015ccae", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cb32ce58c90a2c90af0fad8644e516ec", "guid": "bfdfe7dc352907fc980b868725387e98957a5fb7c5bbc6f3bd4778a6b10c47d2"}], "guid": "bfdfe7dc352907fc980b868725387e98501092994f8641a1331368b0e78fcffd", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ce6f4193809bcb8004e72ce7e36362e9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder"}], "guid": "bfdfe7dc352907fc980b868725387e982ec175b6b4d6149d1cce89f5f0b3694a", "name": "flutter_image_compress_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b928db338a2b0bf59a36f34e391aa676", "name": "flutter_image_compress_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-gp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-pre", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-rsyp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-yhxt", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-gp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-pre", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-rsyp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-yhxt", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-gp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-pre", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-rsyp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-yhxt", "provisioningStyle": 1}], "type": "standard"}