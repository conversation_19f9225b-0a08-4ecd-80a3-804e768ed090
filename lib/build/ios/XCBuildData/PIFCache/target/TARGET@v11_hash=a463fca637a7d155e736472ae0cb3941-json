{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ba9d32b340304f1733a3e0c9399e8b45", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98bf18ac9686df6f036b14c7b6b7849b77", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ba9d32b340304f1733a3e0c9399e8b45", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98e1b3db7858a198ecb135de39f64634db", "name": "Debug-gp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ba9d32b340304f1733a3e0c9399e8b45", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98cb8c07796650909fea957a2c3aac99f2", "name": "Debug-pre"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ba9d32b340304f1733a3e0c9399e8b45", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e988a0688e3c637238d75db4e888b6888fc", "name": "Debug-rsyp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ba9d32b340304f1733a3e0c9399e8b45", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98b259a03308123ab52c3a9177d3cff4ec", "name": "Debug-yhxt"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9851262f605d7642bf3276481c0e533425", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9832615e50e0e8a848f3c6a6c509de0355", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9851262f605d7642bf3276481c0e533425", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98de77f85329cb5a8686aca566ab6d284b", "name": "Profile-gp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9851262f605d7642bf3276481c0e533425", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e985939bf9c8d56daf2b293c8de0a3df2b3", "name": "Profile-pre"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9851262f605d7642bf3276481c0e533425", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98b919843106993f7d861bc307c60f1593", "name": "Profile-rsyp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9851262f605d7642bf3276481c0e533425", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98aedef1898b8327c177333b5785aaef12", "name": "Profile-yhxt"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9851262f605d7642bf3276481c0e533425", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e988cdf0a7764dfff3b24969143fec91cd9", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9851262f605d7642bf3276481c0e533425", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98229264a90f297679c7d373dc9dc61d13", "name": "Release-gp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9851262f605d7642bf3276481c0e533425", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98d33011373d437693e49b4bd3e2376bb4", "name": "Release-pre"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9851262f605d7642bf3276481c0e533425", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9859494f342179a430ce732e5ea4cf1e7c", "name": "Release-rsyp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9851262f605d7642bf3276481c0e533425", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98eb8430cd7f0b4aff2a857c8e22d2c452", "name": "Release-yhxt"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98a066d651f86cb40f001d680e611e9996", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98cef237120d6b06d67bd26a818d0ac454", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9883ac4f32daf5ca23396f4cef068c5f8c", "guid": "bfdfe7dc352907fc980b868725387e9863fc372ce09434cce12d8f720d926d2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98257c98499a1f8174edfa50d836f00f65", "guid": "bfdfe7dc352907fc980b868725387e9852557fc61632b36b3bf67af140e86b75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fd4a082428a00dce3f63a48dd76f54a", "guid": "bfdfe7dc352907fc980b868725387e98f4e31713e8bea181342c3d9f154f810f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe302b102b3e13b49949aab6755f3c74", "guid": "bfdfe7dc352907fc980b868725387e98ca595e214db4288ebac71ad2fbd97a37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98794f68730ab8dee6f8f0815380a2798c", "guid": "bfdfe7dc352907fc980b868725387e983e7c590a551ef0373141b377b0b997b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838a712e92a7ac7ef1b6cdf4b0e2b3645", "guid": "bfdfe7dc352907fc980b868725387e982e3043c65add628ba44e68e7a1aedfb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848cf2bba7016b6feb68e11219c56faf1", "guid": "bfdfe7dc352907fc980b868725387e98293f6c386525ddb9dc3a6bc8f37c4390"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d9f79b759b1b1dd69c9d5fa4c4c35e3", "guid": "bfdfe7dc352907fc980b868725387e986dbe6f0586efaf4802a5ba52bdcc461c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982698d2946fcc592dd0b803fee681a3d1", "guid": "bfdfe7dc352907fc980b868725387e98ff4be887d38a4d70dfaa7ffab286de9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4bddbb4c85a872f353de34689f8a8e9", "guid": "bfdfe7dc352907fc980b868725387e98f4c6bb1c030cff449b486a53f62306e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fafd5e182a5fc230c3450e4e32cadcbd", "guid": "bfdfe7dc352907fc980b868725387e9841933800edfb0abc6742d13a68e956a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca9bf51b3eeeeca0191d3df6d14f077a", "guid": "bfdfe7dc352907fc980b868725387e98fbbada9caab5a13b08ee52a5e02c6b66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f8cd5d16cac8f0807ad9f0cf44dcf4a", "guid": "bfdfe7dc352907fc980b868725387e988e5769e914fe1deec45eb5a5f998e48d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c68631cb20b53c862aa88eed9455d758", "guid": "bfdfe7dc352907fc980b868725387e98a22d16ccd4b80b9dea9d7253cf8663c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e67a8a1f9a0172138180d6d3f569cd4a", "guid": "bfdfe7dc352907fc980b868725387e9874476a0d152265ec83b6227c33b7bdc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b282a13b5027812aaedaa7838d92b8e0", "guid": "bfdfe7dc352907fc980b868725387e98ac40377185827b17b6f26bf1b0264a26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812ca427083e740cbc05790ea79a2d179", "guid": "bfdfe7dc352907fc980b868725387e981fff60774858327b7310f1ea051ca9ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d4bda1a88a6909938d5f5f7a7d6660e", "guid": "bfdfe7dc352907fc980b868725387e9831d41f8d10b6e48cbc453a38e181e538"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98919dfa50355d05f0c5d40af004a62007", "guid": "bfdfe7dc352907fc980b868725387e9806279f8970050732ab553aa5e86a1f7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a7d569f872f4ecf8acb16ed5de2342c", "guid": "bfdfe7dc352907fc980b868725387e989e9f04ac7d9c8f14daac0fabee4e0999"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98908a0e48a0e6e4ab6277b09bfb76d4fd", "guid": "bfdfe7dc352907fc980b868725387e984f65b06e6572e3996488d918214acec4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ff2cd54ae5603bbbf617f40a8e51b48", "guid": "bfdfe7dc352907fc980b868725387e98ab3f9bd64c2c0bca8b80516bff63727c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa81cd25a5a074722ac9364fdb79a250", "guid": "bfdfe7dc352907fc980b868725387e98b6a4305730f73fd18ade0853194680c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984daa9a4e21d341aff2a032fbdd608029", "guid": "bfdfe7dc352907fc980b868725387e983a4da466e83316d4c99f33c2182751c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986647a74d4d357086fc8eadc6445e7fb6", "guid": "bfdfe7dc352907fc980b868725387e9894618b998b3c5d4946d278c168b0a3e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980980f1ac9afdebbae50f1d4368e32aa1", "guid": "bfdfe7dc352907fc980b868725387e984704d245c70861c3dbf96dd1c3ccdf3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d189c93ec4c19565cc087a0e039d9130", "guid": "bfdfe7dc352907fc980b868725387e981fb3459268ed33a7e182d5f9def6affd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c53ed722fc1d99f282c2c017162a09cf", "guid": "bfdfe7dc352907fc980b868725387e989edb12d47a4d0f63fe369a085875c46c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98976e52f003bb4bbab635eee9edd70a0c", "guid": "bfdfe7dc352907fc980b868725387e980296cc55a8bb144ac597ebddde05e983"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879d444e421586abf0906a92b7dd89b60", "guid": "bfdfe7dc352907fc980b868725387e98d526eaafb824e7d625bf1bacd1785d13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d826b998159affdaa941af187baf171", "guid": "bfdfe7dc352907fc980b868725387e986288b2a0c65550eb9a6d250fe7ce09ca"}], "guid": "bfdfe7dc352907fc980b868725387e983df2743137f57f18b88b649726bffb59", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e986dbfa2df59ddcae0f992dedaee8f3553", "name": "TOCropViewController-TOCropViewControllerBundle", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980e69b2358d36eb6c2616a1dbbe45f585", "name": "TOCropViewControllerBundle.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-gp", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-pre", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-rsyp", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-yhxt", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-gp", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-pre", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-rsyp", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-yhxt", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-gp", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-pre", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-rsyp", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-yhxt", "provisioningStyle": 0}], "type": "standard"}