{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980dc63beeb84976bb5bb8d93cba840bc5", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dbfbfd81fd8ff71100e7c0b37aa99ad5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980dc63beeb84976bb5bb8d93cba840bc5", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983190c4680238990bc0fd4bc578441db8", "name": "Debug-gp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980dc63beeb84976bb5bb8d93cba840bc5", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f468d9bcd4a02d0754f0ef4444ef2f9e", "name": "Debug-pre"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980dc63beeb84976bb5bb8d93cba840bc5", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989cfb8cd047399e7ab52b830966e32729", "name": "Debug-rsyp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980dc63beeb84976bb5bb8d93cba840bc5", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9814bbd070d60a4815e6c5413bca173f3e", "name": "Debug-yhxt"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f29d7d502c95bfd835fd8771b8e0bf62", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980da8db16e62ac261edde39f876963f8b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f29d7d502c95bfd835fd8771b8e0bf62", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ec12d9527e14bb6d485adce55b5ff6c", "name": "Profile-gp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f29d7d502c95bfd835fd8771b8e0bf62", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ab300834eb61b4014955f5d6e5d185ca", "name": "Profile-pre"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f29d7d502c95bfd835fd8771b8e0bf62", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987170c9d25e8448355e42275dae6b3839", "name": "Profile-rsyp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f29d7d502c95bfd835fd8771b8e0bf62", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985331b25088bdfb3daf8bd6a0bcf47737", "name": "Profile-yhxt"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f29d7d502c95bfd835fd8771b8e0bf62", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98624c7849e45138c6675d55ac398787c5", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f29d7d502c95bfd835fd8771b8e0bf62", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985cf2c8df9bf203d8fd5f378cb075f0a9", "name": "Release-gp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f29d7d502c95bfd835fd8771b8e0bf62", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9845bd13d9bb03972e9d7f56999d1eb125", "name": "Release-pre"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f29d7d502c95bfd835fd8771b8e0bf62", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c15bcdd6e2c0b123923871c27569f619", "name": "Release-rsyp"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f29d7d502c95bfd835fd8771b8e0bf62", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ae75d95437f8f8ca8216a8489a583d7d", "name": "Release-yhxt"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98446124cfdb43c1298205733d4ba21a31", "guid": "bfdfe7dc352907fc980b868725387e983afb56a26cb7753fe45fbe98760e7d68", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dbd5acb4fac086b81cc3c28337d07f4", "guid": "bfdfe7dc352907fc980b868725387e98dd90d7b0de71c3563396842f7db79ef2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bb461203a144ccc6c0faac57648b680", "guid": "bfdfe7dc352907fc980b868725387e98d7119ba8f9b0fcd2eb98e9461a9c4d98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfe4c66c381716e5ebb807ba270da8b8", "guid": "bfdfe7dc352907fc980b868725387e98967453898b3ad745181e6a3f0c840eeb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983769731ffc2a482f733c402eff3e01c8", "guid": "bfdfe7dc352907fc980b868725387e98d277c54d35dac50f7577b8250691909a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981730d45f3ba84c958ba0b81701320216", "guid": "bfdfe7dc352907fc980b868725387e98b190b05f516ad6360f6c9eeadc28016a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a56519d07768a3539629b6653991ca7c", "guid": "bfdfe7dc352907fc980b868725387e98ab92a305639243592795b1ec9dea342d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a53f2a7f67d39ff4dc7b07a4e809939d", "guid": "bfdfe7dc352907fc980b868725387e98935953e2649df663fe157f36f20a4a76", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9a7a1acc8cd7e3a8cb0d1bded0a0a76", "guid": "bfdfe7dc352907fc980b868725387e98222836fc57333fa2f6bb56bee88d2461", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894ae91c265c5f31814784c61014e8c33", "guid": "bfdfe7dc352907fc980b868725387e98fe19ef381435a01a4931cc796edba70e", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bc25c7d3e3d8426fb39e2dc10d4da89", "guid": "bfdfe7dc352907fc980b868725387e98f92b389b499f88a96e4462534dd2f42d", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4ff2ce3e6aba288800f712fb3812a6f", "guid": "bfdfe7dc352907fc980b868725387e98828bc3b0ef6d0b4a3e3d97dd93e55bf6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0343b0ebc4e8ad0f2c8712cb65b8b32", "guid": "bfdfe7dc352907fc980b868725387e98ed8499c3e7b7680181494b2c471fec29", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890c989b25c8cf32c2026476c521095e8", "guid": "bfdfe7dc352907fc980b868725387e98df2ade9fe35cc2f7653b6bc0127fad4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8807dd58070d6956ca79ea10ab8f8b1", "guid": "bfdfe7dc352907fc980b868725387e98ab02b9ac9739500fdec0b0de0396219a", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e21c22b01abea784cd76fe58e2387c9", "guid": "bfdfe7dc352907fc980b868725387e98cbb561bd1b1cc94245145b4581390111", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd3ed7460e3dd72d61b3296074efe8f", "guid": "bfdfe7dc352907fc980b868725387e9899e4b7fb7c2d4f213c9109b01239fb47", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98477c7b43ccbd3f5155cfa55dfe9a578f", "guid": "bfdfe7dc352907fc980b868725387e98ce733ddf49ff395c55662145891d5818", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851a87969f02a3572174855cbf68815cb", "guid": "bfdfe7dc352907fc980b868725387e98392d6f2e573991d02cfca413966d8b1d", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4d42b4772f319bfb1d2f93f97f54c6f", "guid": "bfdfe7dc352907fc980b868725387e98bb6fe29e295fd69230232f52e47f8315", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a61b1bb7800d26fa6cba59870aa8283", "guid": "bfdfe7dc352907fc980b868725387e98906b099a62c395f5941f89d108ee98d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986430fa0b008459a77d9205ad6c926f23", "guid": "bfdfe7dc352907fc980b868725387e98462309468eff835e998d21698950d5ed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869667c614b758f38c9feff0bb72fc0cd", "guid": "bfdfe7dc352907fc980b868725387e98fc866a015bc207b5f4a2e26fe2fd0c2f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d56a5a659f86b1576e34eabc4f892795", "guid": "bfdfe7dc352907fc980b868725387e98c5a9aacfa849cbf34fccf8bd43e983eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0682b1d89107144e4e6f8376cf31472", "guid": "bfdfe7dc352907fc980b868725387e98b3b1484e5e59c69b85fcff73a1e1a312", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e2e2129c4ae7f6d82300c21465b06a1", "guid": "bfdfe7dc352907fc980b868725387e98a58b2129466beeeac49939ea74dd10f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e8089af5cea09b121fc7df6a2cd4715", "guid": "bfdfe7dc352907fc980b868725387e9880664dd9b13dce0aed6d8e83fe806822", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c7fbceafbf1b8399fd5747fc6fe1ef2", "guid": "bfdfe7dc352907fc980b868725387e98c2ca77e1e3a5976804fcedaf52a680ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852b51dfddeb54f7d37a9b02c070a03ff", "guid": "bfdfe7dc352907fc980b868725387e98899e3a7761850381c739840a31e406e9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b53d3f01323b82bac989fd794ab3a6a9", "guid": "bfdfe7dc352907fc980b868725387e98daa0e6ffb57d9a261b453a10681cf08f", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdf2d10ab98c8292cd44b6b6327c5483", "guid": "bfdfe7dc352907fc980b868725387e981395535772ff5550ea4882647ce5f86f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef6e11332b79290ff229a480bde46221", "guid": "bfdfe7dc352907fc980b868725387e9806c7b1acc044c711d66b9fdca526c81e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a4d3b856f98f5de2d776ad88feea816", "guid": "bfdfe7dc352907fc980b868725387e987430b595e51f9d403bb8533e96aa655c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f95af36c66c9ae7e4c26749f50db70e4", "guid": "bfdfe7dc352907fc980b868725387e983b2a0c77bade167a09c7e7704388ce7d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987612f8e308f21640c8ecfa8e087e6b22", "guid": "bfdfe7dc352907fc980b868725387e985252b2519c6a974971a33713683ad694", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c7a54fff28106aef5fe21ebccdcfeff", "guid": "bfdfe7dc352907fc980b868725387e98d1faf697fceee38a2f8e84a8213afb58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98086564d56507c7479610fd2923379877", "guid": "bfdfe7dc352907fc980b868725387e983ca15b6018a531789ba3ce321d2e9863", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860f1388d6dce426a30edd88f8d829a76", "guid": "bfdfe7dc352907fc980b868725387e98648e18cc1995069764bb4b8cb696938d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a39784d2ca8fd914eeade3e579bdd96e", "guid": "bfdfe7dc352907fc980b868725387e989ac55fd2156bdeb69bdcd6bcd389d976", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822635a7c476b303d33d14627af697364", "guid": "bfdfe7dc352907fc980b868725387e985437b5115b8443a83f4e34de7338dc2b", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835dd011585024a1c5ff81de4f156c63c", "guid": "bfdfe7dc352907fc980b868725387e9822295ada8aa28467fd147a2cdfe2683f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b08622b00127b6e50b338c5a34801674", "guid": "bfdfe7dc352907fc980b868725387e98cd4278a41119295c3cdaa014047424c6", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c05af13b885e0297d893874decce0e04", "guid": "bfdfe7dc352907fc980b868725387e98e0acf4855070988e75ee7f25a30a8c26", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f15e02352125ca3795bb372bd0ed986", "guid": "bfdfe7dc352907fc980b868725387e981c9f8cf77e59e7fd3d861ee6424ff3be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3f9fec6f77f472c29e92e86e7957d59", "guid": "bfdfe7dc352907fc980b868725387e98ddb65873b613a1dbc37d298f62ce883d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ce627144d5837f97f0b042472aab554", "guid": "bfdfe7dc352907fc980b868725387e982748e4f39d2327e2efc64db61cb3fd46", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d8dc764e82ea6ef5d43260027dd41ea", "guid": "bfdfe7dc352907fc980b868725387e98fc56a483eb2b55d9c73471264968020e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b599200221b4e64e86f12eff6617284", "guid": "bfdfe7dc352907fc980b868725387e984226cdbf8d389ae1b4be9b632fe566dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aebff40c3e20db02089c24d1032f1109", "guid": "bfdfe7dc352907fc980b868725387e98357bc0e98d21b3b3078e45b527a68cc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cda6d360f616c575be60ce7c4a6c88b", "guid": "bfdfe7dc352907fc980b868725387e9880a7dc0efde334f6d2c936ae5fb28307", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2cedc59c75e28eab5bc4710c4dfe5e1", "guid": "bfdfe7dc352907fc980b868725387e982b41e62cde8753b8689c13505317fbea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852d709d430e82a71bb3a707d4f2009f5", "guid": "bfdfe7dc352907fc980b868725387e98f667791b0e88451739c6999ac6f3ee66", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da81725f81cfb11929069a2cba47b9a7", "guid": "bfdfe7dc352907fc980b868725387e98e5959cdac83886b65ea36bc68eb347af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d165ae70e0399921645bcbcba24a7bce", "guid": "bfdfe7dc352907fc980b868725387e98a30d7bf5e9cb1f27fa426ba1171dbd70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d9e6166064617aa68d9a371a1c99d0c", "guid": "bfdfe7dc352907fc980b868725387e9825fc572293b89a5b072773f45ab3026e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863edb79542ee30d8287802faf5bdfdf7", "guid": "bfdfe7dc352907fc980b868725387e98d35a99c2c8dc971c127da2403cdd0a9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d252d5a22a5eb5c189c653d5829346aa", "guid": "bfdfe7dc352907fc980b868725387e98d91a755acd964576bc00440ea0d411fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa785542e22dc9bb4cc7cc49e5d7ac9e", "guid": "bfdfe7dc352907fc980b868725387e984da6cf9595ce23ea97ee7bf98eb6c78e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e189bba5123e93b90328442813fc1e75", "guid": "bfdfe7dc352907fc980b868725387e98f4ceef8d1620dfad59bd53a0247b0dd1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c037240efb68e430120cfa90cb245534", "guid": "bfdfe7dc352907fc980b868725387e981d0496a5e8d3da9664a03a5e8f45b4b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa8c15c77ffcb960a06993b18c97d087", "guid": "bfdfe7dc352907fc980b868725387e988f4d702593fd076f5a8ad2c6c6dba806", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfd2a10873128650b91c7acb85417253", "guid": "bfdfe7dc352907fc980b868725387e98980ec9d45bd4699d5719ecf374c7e3a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1d471db7730b004cf089a4c870b4dfb", "guid": "bfdfe7dc352907fc980b868725387e989532d34c1546bde052bd86c5d6e4b46f", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8686bc9a04c126b3cc8e661fcbfea45", "guid": "bfdfe7dc352907fc980b868725387e98f52de12303e080b0057b56ccd5f098c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987af0bf90abc430372b991ec6f82db073", "guid": "bfdfe7dc352907fc980b868725387e989f9596c325c998c9ca1563186a038dfd", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fadadf25aed76a858d768ea69b512638", "guid": "bfdfe7dc352907fc980b868725387e9841107da1cb9f36482134eaf42c92bc6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888f24e9def30ce3ed7937ea29c3b6c09", "guid": "bfdfe7dc352907fc980b868725387e9825dc82e225c66f02d1929e74d74dd04f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9d728d78b58166149c89e8d9b8368f8", "guid": "bfdfe7dc352907fc980b868725387e982bf0e603e729893c3c8ab5f0df4d6b5e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b82c0848f79698d5414bf52d2b86d962", "guid": "bfdfe7dc352907fc980b868725387e98a1bdecc727d2c008a92c6c6a75f558b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a567a635ae55d00b2559dffd8dcc20f", "guid": "bfdfe7dc352907fc980b868725387e98a457702cc572d7c30fff3e6506297f78", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b079bdc362201be2d29872559552462c", "guid": "bfdfe7dc352907fc980b868725387e983d811fd98e89d4ccb64379b465370efe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d9c073be7f3a87d27e35c352793be48", "guid": "bfdfe7dc352907fc980b868725387e98815c081a8766c5614f4533c12152ea8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4890041bcf472e9a93be398f1ff722f", "guid": "bfdfe7dc352907fc980b868725387e98e3a889fd56ac5f6ced7530a79f13e9a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4d3c159fde900d5d17de0d0ca739ffb", "guid": "bfdfe7dc352907fc980b868725387e9878c8087fcee3ab5c6f8c41d636e17202", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a835b8f6971d5cadb57f7be51162898", "guid": "bfdfe7dc352907fc980b868725387e9863eda6c8abd0d1dcfe307157e98b15a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989877796b3cdd51c6248e23b5cf366aa3", "guid": "bfdfe7dc352907fc980b868725387e989efc94ef8d30a1b8981a24971a21e2e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866c099ce802608b872a6203d09de5dd3", "guid": "bfdfe7dc352907fc980b868725387e98a817e70ffc590549dd515bd7b025175c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9834a8039ad93a867999d7b44f5c7cd868", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982b4f6a24510490f2203f8804a4f23682", "guid": "bfdfe7dc352907fc980b868725387e98393d84e8ea9e1b1fbeeb00073c43abad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987364133ad1c7dfb43905f955eb81acc3", "guid": "bfdfe7dc352907fc980b868725387e98f538c62325d46dd4e9e035bf77f25368"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecafb87bb821957badf045cbc45b5f57", "guid": "bfdfe7dc352907fc980b868725387e98034cb1640cc1c9accb4aefce4740dbb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856c60fe1f03c6ffeb7d6f4d71c8ce04d", "guid": "bfdfe7dc352907fc980b868725387e98ba43999161e48da2a532faf584bc1672"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887b6350a790269450aa9d12de9dbe100", "guid": "bfdfe7dc352907fc980b868725387e98ab7fb15d4137b35debcf0b63e647bceb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0736fe10152c8bc007ea6d1cb66db9e", "guid": "bfdfe7dc352907fc980b868725387e98f9e9aa7fd202c31d42f9a33ee9d62e7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98538cfc61cafc1ddd351d7935b824e7be", "guid": "bfdfe7dc352907fc980b868725387e98695acec5d6b72d76a467a4020afb7712"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdceae1c7163b93a2f12ea1a31973b6d", "guid": "bfdfe7dc352907fc980b868725387e98aad17da683b2fa6ad9cf4189fc2bf643"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985880972d8beb6039d266e536a6b8ccdc", "guid": "bfdfe7dc352907fc980b868725387e988d66568064a26753993da5802bbc5013"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804bb8860834989a5133244e450f8394e", "guid": "bfdfe7dc352907fc980b868725387e984db370293c1c1aff0b1f7c54d4296e72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea795cbb0bb26ac513e036d16ec76e73", "guid": "bfdfe7dc352907fc980b868725387e985d6061216830551cb071fab0d5562fb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a75ae5327f5a02ab83b08df6c703a2e2", "guid": "bfdfe7dc352907fc980b868725387e985e62410bbfe319315dbebda44f45294c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cc957c240e99b92ad1822a7371f4ff0", "guid": "bfdfe7dc352907fc980b868725387e98efc73bea0a4eec14ee99758c4fcdd2ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfff58ba8420970f68f7a82840da8347", "guid": "bfdfe7dc352907fc980b868725387e9816144279403f8923268cbf6f930a9445"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9562750711bce4a2d134c7592b6718c", "guid": "bfdfe7dc352907fc980b868725387e989af527a4a87ff508cf513cf2bc22c89b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3458df87578ce1c8e235c7bf86dabd6", "guid": "bfdfe7dc352907fc980b868725387e9870681fd1631f22dfd6f3732368ae22c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4d415042a79010bb7dd63ba433251d3", "guid": "bfdfe7dc352907fc980b868725387e98ed31e0207bb92847a8ee5b11e026129c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f70f4e158ab43c1346dbe487410d975", "guid": "bfdfe7dc352907fc980b868725387e9867ce84ae3291777d361ad6f40f64a915"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eebe3b0080171613dff3a4edbba7b881", "guid": "bfdfe7dc352907fc980b868725387e98d4ec6e2245db1207a40760e7ad557d1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983766588229624a98ee70985b5d18de6c", "guid": "bfdfe7dc352907fc980b868725387e98bb555d78dc20e2fdadd581f123b136a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3a4470d69aedbd7b8e84d4a4515933f", "guid": "bfdfe7dc352907fc980b868725387e98624d8fc0e23b4e5c07fe736915397592"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98461a3000d2dae9098b8d584d01af774f", "guid": "bfdfe7dc352907fc980b868725387e98123ed8fd9c3d1555a7c667ce264ac214"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fa886eb74db5ac8601547d892d441ef", "guid": "bfdfe7dc352907fc980b868725387e98def87bed68aca26db9db16cc230ca9db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800e900896b3844c9c06c2376e9619640", "guid": "bfdfe7dc352907fc980b868725387e9896d487526c9fc2da437e503c017ad10c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcaeac5c1d460b3afa6862518a09beb5", "guid": "bfdfe7dc352907fc980b868725387e988a6d4b4fd423e26d16d9eec99a8f301c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d26d54deebbdbafe84702df41f0adeb2", "guid": "bfdfe7dc352907fc980b868725387e98f0a82ad749ada62195722ac5c7d63c42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b00612c7d28664c48c9607189798d6b6", "guid": "bfdfe7dc352907fc980b868725387e9850c8b501cbdcbd072cb1ca5e5116bbdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980383e308f7339b1385683068d90ec078", "guid": "bfdfe7dc352907fc980b868725387e98ca06cae4b538d944329392ffb2865b3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98617cc538362717f57c9c02877c770911", "guid": "bfdfe7dc352907fc980b868725387e98064b501cad85f517b13f6053c544100a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f548749f342c60acaca16db36aebe0e3", "guid": "bfdfe7dc352907fc980b868725387e9860d9619234c1f858924fbba9b273793e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af7984a4e396b657f788afa282181756", "guid": "bfdfe7dc352907fc980b868725387e986370330d89493241bd08fc0b421e6124"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dceeccb734ac5b5ebe850c2a1f992f42", "guid": "bfdfe7dc352907fc980b868725387e980ad321d50bdb13115761e881e12ce9a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cfaae2672a64d5b15ba8381f4150fe2", "guid": "bfdfe7dc352907fc980b868725387e983df3982959f10409c86e4e7fb59789c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984524befb23abae95b0f13f4b1b037803", "guid": "bfdfe7dc352907fc980b868725387e98c2e688c7da082a18b60e6a3d3abec477"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dd3e820f72b5d54155f2fd36fe941a6", "guid": "bfdfe7dc352907fc980b868725387e9800af38b6f622121ecacf0126700db928"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e60c38f3c6c0e6979ce90c541ef09962", "guid": "bfdfe7dc352907fc980b868725387e98bfbe8c590bd80b758a1df321d903c409"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884afc33d463adb2961bfb642d4973435", "guid": "bfdfe7dc352907fc980b868725387e98b764b425c6d2ffd18b4f54b3b0f829f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985515a7b8e19ecd239179ae9c76e901af", "guid": "bfdfe7dc352907fc980b868725387e987f276ebe2341d9c281a1aee57db5d5a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daa28f53820f9ec21294278a6eb24b2c", "guid": "bfdfe7dc352907fc980b868725387e98a7f0257a78679683e68e5a4fbcdae759"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f844f37541c3c2f042981d1421a778f7", "guid": "bfdfe7dc352907fc980b868725387e9867d4ece36c172ea061782b83022db188"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0fca5379ee4fe1f4c7fc3e0003bc9c9", "guid": "bfdfe7dc352907fc980b868725387e984251d40b7bb622eb9cb2637bbffdd7ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98465600670b22aa8f5cbf341869086ada", "guid": "bfdfe7dc352907fc980b868725387e9807e6ed7e2e2a6f2ea864b62a3033c2a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f314013427434abea9937e8b7af9b42", "guid": "bfdfe7dc352907fc980b868725387e984176e82cc301aebe661f2188c5fa6afe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0ab7cbb6d677d004796416d793f96fb", "guid": "bfdfe7dc352907fc980b868725387e98ec4a20ff153d9cd79f023d84bf9a963b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835e9450e00be76ede61900476d24f43c", "guid": "bfdfe7dc352907fc980b868725387e987c54b11d3bf284dafbf39300f86104f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b87535bba7d060bb8045299ddc7f4291", "guid": "bfdfe7dc352907fc980b868725387e98f19f7e35202f7d81857bfc21c48340e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b708974d68f2b7e0f130a1300e1874c3", "guid": "bfdfe7dc352907fc980b868725387e988595231933d146ca2923a7d1116ebafa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d46beaa45dc0afdd6b52028b67edca7", "guid": "bfdfe7dc352907fc980b868725387e988f9996f58ac2dc2cf9172c044c7c6a7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98386d96f013127efe82c2525cf854f738", "guid": "bfdfe7dc352907fc980b868725387e9871d597f4305a52d2f30063435b8fe5d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abf57293a9611519dc5e3f48a2c9ab11", "guid": "bfdfe7dc352907fc980b868725387e981b4408f30e494354f26b25d84ddc43aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c06d00527c66f74de55ee7db6d1d88ee", "guid": "bfdfe7dc352907fc980b868725387e9803d061dbf9b086d9cf34096cb142782a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed051b779a207a2fe3ea032c0da4a22f", "guid": "bfdfe7dc352907fc980b868725387e98bd45821ff584bdfea67ddbef2b4d756a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a6bfbdd0a7e95a4b97f63fea2eb4a81", "guid": "bfdfe7dc352907fc980b868725387e98aec5cf9b7869d963a2e161a055096fc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98026f0dd7616df082c2c9cd29f52b9ce1", "guid": "bfdfe7dc352907fc980b868725387e98fa1432bde7e7a30b4293b38dbc1abe3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828802629f5bcce197b79e3f2a41168c6", "guid": "bfdfe7dc352907fc980b868725387e98520faec5ab0e1a333837fbe5119c584b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aba97b54069286dd3c4414f419b3a68", "guid": "bfdfe7dc352907fc980b868725387e98fa067f2266b11eec738c84e13b30ae43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b90a9345af31a36b50f9d345dfc2544", "guid": "bfdfe7dc352907fc980b868725387e9811d05e88d352fdc5aa4034dd681aea1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839891c0e1a418c891e87644cfbe2a5d4", "guid": "bfdfe7dc352907fc980b868725387e98af720e9d1efada4608278a1636dbe984"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c46a03878f54203ee29c5a749d40155", "guid": "bfdfe7dc352907fc980b868725387e98121c4ab9099fb36ed498ed1e433f6536"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878f7353b17e8d404caa5b5fb29cf115e", "guid": "bfdfe7dc352907fc980b868725387e9897a63450025ad6f3ea121f0edb9c1d35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ce70a48dffa135e45d5ab54b5dc0b88", "guid": "bfdfe7dc352907fc980b868725387e98d8438145a1224389a354ebe6b8c9931a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b38ab85c496cfe5829b4e92a46919418", "guid": "bfdfe7dc352907fc980b868725387e9818eb52423b3c1db1e49537ced32fa006"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984acbd9d3e8d1e03c2e57889be27bd4ac", "guid": "bfdfe7dc352907fc980b868725387e98e0a27c2e28cc9063ee09a297313c2b36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1f08291d8c009170ccfb46a6e91c2b1", "guid": "bfdfe7dc352907fc980b868725387e986b713db2b23734b3d6fb2dd89262c12e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3c6f3afcd9650c37780078d415786f5", "guid": "bfdfe7dc352907fc980b868725387e988cbd7ddc2a9dd6483f87ed9ffedcc1c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3574a56e392d1a23d9e99c235a157a5", "guid": "bfdfe7dc352907fc980b868725387e98a0aa39b933d7768885972ddfa6ac1f24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f49f86c86070003461158c5d2bf45aff", "guid": "bfdfe7dc352907fc980b868725387e98763ab033454d90e11a4b446b8877586c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd8f6da360d044845ea15d77381efd2f", "guid": "bfdfe7dc352907fc980b868725387e98fd66c583990cb76e83720aec1b7f27ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985be1cbbbbfd57ed7aa354b5eafee4156", "guid": "bfdfe7dc352907fc980b868725387e98d20d08883781e34a1190a001a02bb691"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf6c391ce47d000550c4dbd0318dcdcd", "guid": "bfdfe7dc352907fc980b868725387e98538dd0bfcf604e3bdbdd1a5538d00f33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d47db8596a4d7ff3cc733f1fe9f62879", "guid": "bfdfe7dc352907fc980b868725387e984248cca90ff9801ecc018b68d5b69c80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f6b125de453b81197fc516a9a8ee079", "guid": "bfdfe7dc352907fc980b868725387e98ee1b3c574c176444c87f46896be5f037"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860216f23a8b25d7361f39add371a8da1", "guid": "bfdfe7dc352907fc980b868725387e98b02aa0fb8b48df74c331bd64dcd3b92c"}], "guid": "bfdfe7dc352907fc980b868725387e98e75995b5e34d5d8ce0b814597b4e84d3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cb32ce58c90a2c90af0fad8644e516ec", "guid": "bfdfe7dc352907fc980b868725387e98bc8ae8e2b9c52a832f92b29f2112b2ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cabcbf9495a7d4da31097b36496143f", "guid": "bfdfe7dc352907fc980b868725387e98515a08da922b47f1713ab3a6513a0e18"}], "guid": "bfdfe7dc352907fc980b868725387e98c8b89365b9ec0190ce0caede15f0af8e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e989f87e5943179ce9fd65ed4a1fc87dc47", "targetReference": "bfdfe7dc352907fc980b868725387e9826e2628dc041aabe2d77e75ccb1dc95b"}], "guid": "bfdfe7dc352907fc980b868725387e980ac598c1c0e0b638a0ac1b6881e02142", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9826e2628dc041aabe2d77e75ccb1dc95b", "name": "SDWebImage-SDWebImage"}], "guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98880bd43228ac3a23ccb630571006ecd9", "name": "SDWebImage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-gp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-pre", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-rsyp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-yhxt", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-gp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-pre", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-rsyp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-yhxt", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-gp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-pre", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-rsyp", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-yhxt", "provisioningStyle": 1}], "type": "standard"}