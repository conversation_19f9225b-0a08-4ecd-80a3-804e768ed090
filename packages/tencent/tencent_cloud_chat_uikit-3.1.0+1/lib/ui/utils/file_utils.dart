import 'dart:io' as io;

import 'package:extended_image_library/src/_platform_web.dart' as web;
import 'package:flutter/foundation.dart' show kIsWeb;

class FileUtils {
  static dynamic getFile(String path) {
    return kIsWeb ? web.File(path) : io.File(path);
  }

  static bool existsSync(String path) {
    if (kIsWeb) {
      // Web platform doesn't support synchronous file operations
      // You might want to return true if you know the file exists
      // or implement an alternative check for web
      return true; // or implement your web-specific check
    } else {
      return io.File(path).existsSync();
    }
  }
}
