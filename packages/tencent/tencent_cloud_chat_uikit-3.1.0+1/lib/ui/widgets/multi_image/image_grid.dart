import 'package:flutter/material.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_chat_separate_view_model.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/message.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitChat/TIMUIKitMessageItem/tim_uikit_chat_image_elem.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitChat/TIMUIKitMessageItem/tim_uikit_chat_video_elem.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/multi_image/image_grid_layout_delegate.dart';

class TelegramMediaGrid extends StatelessWidget {
  final String? text;
  final List<V2TimMessage> messages;
  final TUIChatSeparateViewModel chatModel;
  final bool isShowMessageReaction;
  final VoidCallback clearJump;
  final bool isShowJump;
  final TextStyle nickNameStyle;
  final TUITheme theme;

  const TelegramMediaGrid({
    super.key,
    this.text,
    required this.messages,
    required this.chatModel,
    required this.isShowMessageReaction,
    required this.clearJump,
    required this.isShowJump,
    required this.nickNameStyle,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    final height = MediaQuery.sizeOf(context).height;
    final isSelf = messages.first.isSelf ?? true;
    final needShowText = text != null && text!.isNotEmpty;
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(1.5),
          decoration: BoxDecoration(
            color: theme.white?.withOpacity(0.8),
            borderRadius: BorderRadius.circular(14),
          ),
          child: Column(
            crossAxisAlignment: isSelf ? CrossAxisAlignment.end : CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: height * 0.4, // Adjust height as needed
                width: double.infinity,
                child: CustomMultiChildLayout(
                  delegate: ImageGridLayoutDelegate(imageCount: messages.length),
                  children: List.generate(
                    messages.length,
                    (index) => messages[index].elemType == MessageElemType.V2TIM_ELEM_TYPE_VIDEO
                        ? LayoutId(
                            id: 'image$index',
                            child: TIMUIKitVideoElem(
                              message: messages[index],
                              isShowJump: isShowJump,
                              chatModel: chatModel,
                              isShowMessageReaction: isShowMessageReaction,
                              indexAndLength: (index, messages.length, !needShowText),
                              groupImages: messages,
                            ),
                          )
                        : LayoutId(
                            id: 'image$index',
                            child: TIMUIKitImageElem(
                              clearJump: clearJump,
                              isShowJump: isShowJump,
                              chatModel: chatModel,
                              message: messages[index],
                              isShowMessageReaction: isShowMessageReaction,
                              indexAndLength: (index, messages.length, !needShowText),
                              groupImages: messages,
                            ),
                          ),
                  ),
                ),
              ),
              if (needShowText) ...[

                Container(
                  padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 10),
                  child: Text(text!, style: const TextStyle(fontSize: 14, color:  Color(0xff353D4B)),),
                ),
                const SizedBox(height: 20,),
              ],
            ],
          ),
        ),
        Container(
          margin: const EdgeInsets.only(bottom: 16, right: 8),
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
          decoration: BoxDecoration(
            color: theme.black?.withOpacity(0.5),
            borderRadius: BorderRadius.circular(14),
          ),
          child: Text(
            messages.last.timestamp.messageTime(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
            ),
          ),
        ),
      ],
    );
  }
}
