import 'dart:async';
import 'dart:convert';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';
import 'package:tencent_cloud_chat_uikit/data_services/core/models/custom_element.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/constants/history_message_constant.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/file_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/logger.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/message.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/platform.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/thumbnail_builder/thumbnail_builder_widget.dart';

class MySwiperPlugin extends StatefulWidget {
  const MySwiperPlugin(
    this.pics,
    this.index,
    this.reBuild, {
    required this.onThumbnailTap,
    super.key,
    required this.theme,
  });

  final List<V2TimMessage> pics;
  final int? index;
  final StreamController<int> reBuild;
  final Function(int) onThumbnailTap;
  final TUITheme theme;

  @override
  State<MySwiperPlugin> createState() => _MySwiperPluginState();
}

class _MySwiperPluginState extends State<MySwiperPlugin> {
  late PageController _thumbnailPageController;
  late int _currentPage;
  final double _thumbnailViewportFraction = 0.1;
  final double _thumbnailHeight = 50.0;

  @override
  void initState() {
    super.initState();
    _currentPage = widget.index ?? 0;
    _thumbnailPageController = PageController(
      initialPage: _currentPage,
      viewportFraction: _thumbnailViewportFraction,
    );

    // Listen to rebuild events from the main swiper
    widget.reBuild.stream.listen((index) {
      if (_currentPage != index) {
        setState(() {
          _currentPage = index;
        });
        // Animate thumbnail to the correct position
        _thumbnailPageController.animateTo(
          index * MediaQuery.of(context).size.width * _thumbnailViewportFraction,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _thumbnailPageController.dispose();
    super.dispose();
  }

  V2TimImage? _getImageFromList(V2TimMessage message, V2TimImageTypesEnum imgType, index) {
    if (message.imageElem == null) {
      final customData = CustomElement.fromJsonString(message.customElem!.data!);
      return V2TimImage(
        type: 1,
        localUrl: customData.mediaList[index!].thumbnailPath,
        url: customData.mediaList[index!].thumbnailUrl,
      );
    }
    return MessageUtils.getImageFromImgList(message.imageElem!.imageList,
        HistoryMessageDartConstant.imgPriorMap[imgType] ?? HistoryMessageDartConstant.oriImgPrior);
  }

  Widget _renderImage(V2TimMessage message, bool isSelected, int index) {
    final originalImg = _getImageFromList(message, V2TimImageTypesEnum.original, index);
    final smallImg = _getImageFromList(message, V2TimImageTypesEnum.small, index);
    Widget getImageWidget() {
      final isWeb = PlatformUtils().isWeb;
      if (PlatformUtils().isWeb && message.imageElem?.path != null) {
        return ExtendedImage.network(
          message.imageElem!.path ?? smallImg?.url ?? originalImg!.url!,
          fit: BoxFit.cover,
        );
      }

      // Check for local file path
      if (message.isSelf! && message.imageElem?.path != null && message.imageElem!.path!.isNotEmpty) {
        try {
          final file = FileUtils.getFile(message.imageElem!.path!);
          if (file.existsSync()) {
            return ExtendedImage.file(
              file,
              fit: BoxFit.cover,
            );
          }
        } catch (e) {
          outputLogger.i(e.toString());
        }
      }

      // Check for local URLs
      // if (smallImg?.localUrl != null || originalImg?.localUrl != null) {
      //   try {
      //     final localPath = smallImg?.localUrl ?? originalImg?.localUrl;
      //     if (localPath != null && (!isWeb && FileUtils.getFile(localPath).existsSync())) {
      //       return ExtendedImage.file(
      //         FileUtils.getFile(localPath),
      //         fit: BoxFit.cover,
      //       );
      //     }
      //   } catch (e) {
      //     outputLogger.i(e.toString());
      //   }
      // }

      // Fallback to network image
      if ((smallImg?.url ?? originalImg?.url) != null) {
        String url = smallImg?.url ?? originalImg!.url!;
        if (url.isBase64Image) {
          return Image.memory(
            base64Decode(url.cleanBase64),
            fit: BoxFit.cover, // Adjust as needed
          );
        }
        return ExtendedImage.network(
          smallImg?.url ?? originalImg!.url!,
          fit: BoxFit.cover,
        );
      }

      if (message.videoElem?.videoPath != null) {
        return VideoThumbnailBuilder(videoUrl: message.videoElem!.videoPath!);
      }

      return const Center(
        child: Icon(Icons.image, size: 40, color: Colors.grey),
      );
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 250),
      curve: Curves.easeInOut,
      padding: EdgeInsets.all(isSelected ? 0 : 4),
      width: isSelected ? 50 : 40,
      height: isSelected ? 50 : 40,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(2),
        child: getImageWidget(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: _thumbnailHeight,
      color: Colors.grey.withOpacity(0.2),
      child: Center(
        child: PageView.builder(
          controller: _thumbnailPageController,
          itemCount: widget.pics.length,
          onPageChanged: (index) {
            if (_currentPage != index) {
              setState(() {
                _currentPage = index;
              });
              widget.onThumbnailTap(index);
              widget.reBuild.add(index);
            }
          },
          itemBuilder: (BuildContext context, int index) {
            final isVideo = widget.pics[index].imageElem == null;
            return PointerInterceptor(
              child: GestureDetector(
                onTap: () {
                  widget.reBuild.add(index);
                  widget.onThumbnailTap(index);
                  _thumbnailPageController.animateTo(
                    index * MediaQuery.of(context).size.width * _thumbnailViewportFraction,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Positioned.fill(child: _renderImage(widget.pics[index], index == _currentPage, index)),
                    if (isVideo)
                      const Positioned.fill(
                        child: Icon(
                          Icons.play_arrow,
                          size: 20,
                          color: Colors.white,
                        ),
                      ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
