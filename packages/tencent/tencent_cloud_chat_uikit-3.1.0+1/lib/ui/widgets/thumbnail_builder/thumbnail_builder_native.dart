import 'dart:typed_data';
import 'dart:io';

import 'package:tencent_cloud_chat_uikit/ui/widgets/thumbnail_builder/thumbnail_builder.dart';
import 'package:fc_native_video_thumbnail/fc_native_video_thumbnail.dart';
import 'package:path_provider/path_provider.dart';

ThumbnailBuilder getThumbnailBuilder() => ThumbnailBuilderNative();

class ThumbnailBuilderNative extends ThumbnailBuilder {
  final _fcThumbnail = FcNativeVideoThumbnail();
  
  @override
  Future<Uint8List?> getThumbnail(String videoUrl) async {
    try {
      // Create temporary file paths for the thumbnail
      final tempDir = await getTemporaryDirectory();
      final thumbnailPath = '${tempDir.path}/thumbnail_${DateTime.now().millisecondsSinceEpoch}.jpg';
      
      // Generate the thumbnail
      final success = await _fcThumbnail.getVideoThumbnail(
        srcFile: videoUrl,
        destFile: thumbnailPath,
        width: 400,
        height: 400,
        quality: 75,
  
      );
      
      if (success) {
        // Read the generated thumbnail file
        final File thumbnailFile = File(thumbnailPath);
        if (await thumbnailFile.exists()) {
          final Uint8List thumbnailBytes = await thumbnailFile.readAsBytes();
          
          // Clean up the temporary file
          await thumbnailFile.delete();
          
          return thumbnailBytes;
        }
      }
      
      return null;
    } catch (e) {
      print('Error generating video thumbnail: $e');
      return null;
    }
  }
}