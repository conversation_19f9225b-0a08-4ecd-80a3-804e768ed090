import 'package:flutter/foundation.dart';

import 'thumbnail_builder_stub.dart'
    if (dart.library.io) 'thumbnail_builder_native.dart'
    if (dart.library.js) 'thumbnail_builder_web.dart';

abstract class ThumbnailBuilder {
  static ThumbnailBuilder? _instance;

  static ThumbnailBuilder get instance {
    if (_instance == null) {
      if (kIsWeb) {
        _instance = getThumbnailBuilder();
      } else {
        _instance = getThumbnailBuilder();
      }
    }
    return _instance!;
  }

  Future<Uint8List?> getThumbnail(String videoUrl);
}
