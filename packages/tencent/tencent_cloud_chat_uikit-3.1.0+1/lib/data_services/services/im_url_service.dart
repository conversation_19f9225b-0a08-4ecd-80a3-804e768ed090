// packages/tencent/tencent_cloud_chat_uikit-3.1.0+1/lib/data_services/services/im_url_service.dart

// 定义URL服务接口
abstract class IMUrlService {
  String getUploadUrl();
}

// 全局URL服务管理器
class IMUrlServiceManager {
  static IMUrlService? _instance;

  static void initialize(IMUrlService service) {
    _instance = service;
  }

  static String getUploadUrl() {
    if (_instance == null) {
      throw Exception('IMUrlService not initialized');
    }
    return _instance!.getUploadUrl();
  }
}