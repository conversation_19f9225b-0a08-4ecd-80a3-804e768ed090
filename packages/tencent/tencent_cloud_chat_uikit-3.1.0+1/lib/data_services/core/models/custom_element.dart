import 'dart:convert';

class CustomElement {
  final String groupId;
  final String? text;
  final List<MediaItem> mediaList;

  const CustomElement({
    required this.groupId,
    this.text,
    required this.mediaList,
  });

  factory CustomElement.fromJson(Map<String, dynamic> json) {
    return CustomElement(
      groupId: json['groupId'],
      text: json['text'],
      mediaList: (json['mediaList'] as List<dynamic>?)?.map((e) => MediaItem.fromJson(e)).toList() ?? [],
    );
  }

  factory CustomElement.fromJsonString(String jsonString) {
    return CustomElement.fromJson(jsonDecode(jsonString));
  }

  Map<String, dynamic> toJson() {
    return {
      'groupId': groupId,
      'text': text,
      'mediaList': mediaList.map((e) => e.toJson()).toList(),
    };
  }

  String toJsonString() {
    return jsonEncode(toJson());
  }
}
enum MediaStatus { success, failed, initializeState}

class MediaItem {
  final MediaType type;
  final String? url;
  final String? localPath;
  final String? thumbnailPath; // For videos only
  final String? thumbnailUrl; // For videos only
  final int? duration; // For videos only
  final MediaStatus status; // 添加状态
  final String? errorMessage; // 添加错误信息

  const MediaItem({
    required this.type,
    this.url,
    required this.localPath,
    this.thumbnailPath,
    this.thumbnailUrl,
    this.duration,
    this.status = MediaStatus.initializeState, // 默认初始化状态
    this.errorMessage,
  });

  factory MediaItem.fromJson(Map<String, dynamic> json) {
    return MediaItem(
      type: MediaType.values[json['type'] as int],
      url: json['url'],
      localPath: json['localPath'],
      thumbnailPath: json['thumbnailPath'],
      thumbnailUrl: json['thumbnailUrl'],
      duration: json['duration'],
      status: json['status'] != null
          ? MediaStatus.values[json['status'] as int]
          : MediaStatus.success,
      errorMessage: json['errorMessage'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.index,
      'url': url,
      'localPath': localPath,
      'thumbnailPath': thumbnailPath,
      'thumbnailUrl': thumbnailUrl,
      'duration': duration,
      'status': status.index,
      'errorMessage': errorMessage,
    };
  }

  bool get isVideo {
    return type == MediaType.video;
  }

  // 添加判断方法
  bool get isSuccess {
    return status == MediaStatus.success;
  }

  bool get isFailed {
    return status == MediaStatus.failed;
  }

  bool get isInitialized {
    return status != MediaStatus.initializeState;
  }

  // 用于重试时创建新的MediaItem
  MediaItem copyWith({
    MediaType? type,
    String? url,
    String? localPath,
    String? thumbnailPath,
    String? thumbnailUrl,
    int? duration,
    MediaStatus? status,
    String? errorMessage,
  }) {
    return MediaItem(
      type: type ?? this.type,
      url: url ?? this.url,
      localPath: localPath ?? this.localPath,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      duration: duration ?? this.duration,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

enum MediaType { image, video }