import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/providers/current_locale_provider.dart';

extension TranslationExtension on String {
  String trr() {
    final locale = serviceLocator<CurrentLocaleProvider>().currentLocale;
    final translations = {
      'message': {'en-US': 'Message', 'zh-CN': '消息', 'zh-TW': '消息'},
      'wait': {'en-US': 'Wait', 'zh-CN': '等待', 'zh-TW': '等待'},
      'dispatch': {'en-US': 'Send ', 'zh-CN': '发送', 'zh-TW': '發送'},
      'camera': {'en-US': 'Camera', 'zh-CN': '拍照', 'zh-TW': '拍照'},
      'gallery': {'en-US': 'Gallery', 'zh-CN': '相册', 'zh-TW': '相冊'},
      'search': {'en-US': 'Search', 'zh-CN': '搜索', 'zh-TW': '搜索'},
      'general': {'en-US': 'General', 'zh-CN': '通用', 'zh-TW': '通用'},
      'financial': {'en-US': 'Financial', 'zh-CN': '财务', 'zh-TW': '財務'},
    };

    return translations[this]?[locale] ?? this;
  }
}
