import 'package:freezed_annotation/freezed_annotation.dart';

part 'assets_records.freezed.dart';
part 'assets_records.g.dart';

@freezed
class AssetsRecords with _$AssetsRecords {
  const factory AssetsRecords({
    int? code,
    AssetsRecordsData? data,
    String? msg,
  }) = _AssetsRecords;

  factory AssetsRecords.fromJson(Map<String, dynamic> json) => _$AssetsRecordsFromJson(json);
}

@freezed
class AssetsRecordsData with _$AssetsRecordsData {
  const factory AssetsRecordsData({
    int? current,
    bool? hasNext,
    List<RecordData>? records,
    int? total,
  }) = _AssetsRecordsData;

  factory AssetsRecordsData.fromJson(Map<String, dynamic> json) => _$AssetsRecordsDataFromJson(json);
}

@freezed
class RecordData with _$RecordData {
  const factory RecordData({
    double? afterNum,
    double? beforeNum,
    String? createTime,
    String? currency,
    int? fromType,
    int? id,
    String? mobile,
    String? realName,
    int? type,
    double? updateNum,
    int? userId,
    String? serialNo,
  }) = _RecordData;

  factory RecordData.fromJson(Map<String, dynamic> json) => _$RecordDataFromJson(json);
}
