<svg width="91" height="90" viewBox="0 0 91 90" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_767_13073)">
<path d="M35.7144 52.1346L17.3069 54.7413C16.7623 54.8184 16.2676 55.0888 15.9304 55.4937L2.71158 71.3673C1.70181 72.5798 2.52314 74.3045 4.16073 74.4103L16.9487 75.2362C17.7874 75.2903 18.4958 75.8028 18.7442 76.535L22.1322 86.5202C22.6296 87.9861 24.7349 88.2919 25.7702 87.0487L38.8976 71.2849C39.2137 70.9052 39.3682 70.4351 39.3313 69.9651L38.0631 53.84C37.9771 52.7462 36.903 51.9663 35.7144 52.1346Z" fill="url(#paint0_linear_767_13073)"/>
</g>
<g filter="url(#filter1_ii_767_13073)">
<path d="M53.4712 50.1688L72.4189 53.4276C72.9379 53.5168 73.4057 53.7819 73.7286 54.1695L87.9657 71.266C88.9927 72.4992 88.1241 74.2502 86.4546 74.3121L73.5109 74.7922C72.6241 74.8251 71.8712 75.3703 71.6354 76.1503L68.5524 86.35C68.0972 87.8561 65.9498 88.1995 64.8965 86.9346L50.762 69.9615C50.46 69.5988 50.3051 69.1531 50.3246 68.703L51.0518 51.925C51.1014 50.7793 52.2352 49.9563 53.4712 50.1688Z" fill="url(#paint1_linear_767_13073)"/>
</g>
<path d="M44.0687 0L61.5645 9.45552L79.3054 18.5244L79.0603 37.0488L79.3054 55.5731L61.5645 64.642L44.0687 74.0975L26.5729 64.642L8.832 55.5731L9.07716 37.0488L8.832 18.5244L26.5729 9.45552L44.0687 0Z" fill="url(#paint2_linear_767_13073)"/>
<g filter="url(#filter2_ii_767_13073)">
<path d="M44.0687 4.51807L59.4308 12.8205L75.0082 20.7834L74.793 37.0487L75.0082 53.314L59.4308 61.2769L44.0687 69.5793L28.7065 61.2769L13.1291 53.314L13.3444 37.0487L13.1291 20.7834L28.7065 12.8205L44.0687 4.51807Z" fill="url(#paint3_linear_767_13073)"/>
</g>
<g filter="url(#filter3_ii_767_13073)">
<path d="M44.2175 9.03613L57.5101 16.22L70.9888 23.1101L70.8026 37.1842L70.9888 51.2582L57.5101 58.1483L44.2175 65.3322L30.925 58.1483L17.4462 51.2582L17.6325 37.1842L17.4462 23.1101L30.925 16.22L44.2175 9.03613Z" fill="url(#paint4_linear_767_13073)"/>
</g>
<g filter="url(#filter4_d_767_13073)">
<path d="M33.7311 37.0489L29.1807 22.5908H32.6487L35.7637 34.4995L38.768 22.5908H42.0817L37.6856 37.0489H33.7311Z" fill="url(#paint5_linear_767_13073)"/>
</g>
<g filter="url(#filter5_d_767_13073)">
<path d="M44.0664 37.0483V22.5908H47.0436V37.0489L44.0664 37.0483Z" fill="url(#paint6_linear_767_13073)"/>
</g>
<g filter="url(#filter6_d_767_13073)">
<path d="M55.1384 22.5908C59.3469 22.5908 61.9291 24.095 61.9291 27.25C61.9291 30.5929 59.2031 32.118 55.4246 32.118H53.5354V37.0489H50.0205V22.5908H55.1384ZM55.1384 29.8617C57.0756 29.8617 58.2945 29.2137 58.2945 27.25C58.2945 25.558 57.1468 24.8052 55.0905 24.8052H53.5361V29.8617H55.1384Z" fill="url(#paint7_linear_767_13073)"/>
</g>
<g filter="url(#filter7_d_767_13073)">
<path d="M39.3906 55.1213V54.7443L42.8865 50.8744C43.3226 50.3981 43.7135 49.9284 44.0594 49.4654C44.4052 48.9891 44.6984 48.5128 44.939 48.0365C45.1795 47.5602 45.36 47.0773 45.4803 46.5878C45.6156 46.085 45.6833 45.5691 45.6833 45.0398C45.6833 44.1666 45.4953 43.4985 45.1194 43.0355C44.7585 42.5592 44.1796 42.321 43.3827 42.321C43.0219 42.321 42.7136 42.3607 42.458 42.4401C42.2024 42.5062 42.0069 42.6055 41.8716 42.7378C41.7363 42.8568 41.6686 42.9958 41.6686 43.1545C41.6686 43.353 41.7287 43.5448 41.849 43.73C41.9092 43.8227 41.9618 43.9219 42.0069 44.0277C42.052 44.1336 42.0746 44.2593 42.0746 44.4048C42.0746 44.7488 41.9468 45.02 41.6912 45.2185C41.4355 45.4037 41.1273 45.4963 40.7664 45.4963C40.3905 45.4963 40.0823 45.3838 39.8417 45.1589C39.6011 44.9208 39.4808 44.6495 39.4808 44.3453C39.4808 43.8822 39.6613 43.439 40.0221 43.0156C40.383 42.5922 40.8942 42.2483 41.5558 41.9836C42.2325 41.7058 43.0143 41.5669 43.9015 41.5669C44.9991 41.5669 45.9163 41.7323 46.6531 42.063C47.4049 42.3806 47.9688 42.8039 48.3447 43.3331C48.7206 43.8491 48.9085 44.4114 48.9085 45.02C48.9085 45.8535 48.683 46.6142 48.2319 47.3022C47.7808 47.9902 47.2094 48.6451 46.5178 49.2669C45.8411 49.8755 45.157 50.4907 44.4653 51.1125L42.6384 52.7597H47.2846C47.6755 52.7597 47.9688 52.6605 48.1642 52.462C48.3597 52.2636 48.5326 51.9791 48.683 51.6087L48.8183 51.2912H49.3145L48.4574 55.1213H39.3906Z" fill="url(#paint8_linear_767_13073)"/>
</g>
<defs>
<filter id="filter0_ii_767_13073" x="2.26953" y="48.1118" width="37.0674" height="43.7124" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13073"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13073" result="effect2_innerShadow_767_13073"/>
</filter>
<filter id="filter1_ii_767_13073" x="50.3232" y="46.1353" width="38.085" height="45.5752" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13073"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13073" result="effect2_innerShadow_767_13073"/>
</filter>
<filter id="filter2_ii_767_13073" x="13.1289" y="0.518066" width="61.8789" height="73.061" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13073"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.835294 0 0 0 0 0.643137 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13073" result="effect2_innerShadow_767_13073"/>
</filter>
<filter id="filter3_ii_767_13073" x="17.4463" y="4.03613" width="53.543" height="66.2959" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.24 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_767_13073"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-5"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.9 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_767_13073" result="effect2_innerShadow_767_13073"/>
</filter>
<filter id="filter4_d_767_13073" x="9.18066" y="12.5908" width="52.9014" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13073"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13073" result="shape"/>
</filter>
<filter id="filter5_d_767_13073" x="24.0664" y="12.5908" width="42.9775" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13073"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13073" result="shape"/>
</filter>
<filter id="filter6_d_767_13073" x="30.0205" y="12.5908" width="51.9082" height="54.458" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13073"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13073" result="shape"/>
</filter>
<filter id="filter7_d_767_13073" x="19.3906" y="31.5669" width="49.9238" height="53.5542" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.7125 0 0 0 0 0 0 0 0 0 0 0 0 0 0.55 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_767_13073"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_767_13073" result="shape"/>
</filter>
<linearGradient id="paint0_linear_767_13073" x1="37.3586" y1="51.4475" x2="14.7989" y2="84.1212" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFBD14"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.5" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFBD14"/>
</linearGradient>
<linearGradient id="paint1_linear_767_13073" x1="51.6912" y1="49.3918" x2="75.7649" y2="84.2583" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint2_linear_767_13073" x1="44.0687" y1="-5.83862e-07" x2="44.0687" y2="74.2088" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE17A"/>
<stop offset="1" stop-color="#FE9902"/>
</linearGradient>
<linearGradient id="paint3_linear_767_13073" x1="44.0687" y1="4.51807" x2="44.0687" y2="69.5793" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC700"/>
<stop offset="0.25" stop-color="#FAC95C"/>
<stop offset="0.335" stop-color="#F5D5A4"/>
<stop offset="0.625" stop-color="#FAC95C"/>
<stop offset="0.75" stop-color="#FFC700"/>
</linearGradient>
<linearGradient id="paint4_linear_767_13073" x1="44.2175" y1="9.03613" x2="44.2175" y2="65.3322" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF866B"/>
<stop offset="1" stop-color="#F66848"/>
</linearGradient>
<linearGradient id="paint5_linear_767_13073" x1="35.6312" y1="22.5908" x2="35.6312" y2="37.0489" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint6_linear_767_13073" x1="45.555" y1="22.5908" x2="45.555" y2="37.0489" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint7_linear_767_13073" x1="55.9748" y1="22.5908" x2="55.9748" y2="37.0489" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
<linearGradient id="paint8_linear_767_13073" x1="44.3526" y1="41.5669" x2="44.3526" y2="55.1213" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEF64"/>
<stop offset="0.5" stop-color="#FFA215"/>
<stop offset="1" stop-color="#FFE456"/>
</linearGradient>
</defs>
</svg>
