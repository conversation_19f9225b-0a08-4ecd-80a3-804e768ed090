#!/bin/bash

# Git pre-commit hook for Flutter projects
# Place this file in .git/hooks/pre-commit and make it executable

# Get current branch name
BRANCH_NAME=$(git symbolic-ref --short HEAD)
TARGET_BRANCH="feat/flavor-setup"

# Only run on feat/flavor-setup branch
if [ "$BRANCH_NAME" != "$TARGET_BRANCH" ]; then
  echo "Not on $TARGET_BRANCH branch, skipping pre-commit hooks"
  exit 0
fi

echo "Running pre-commit hooks for $TARGET_BRANCH branch..."

# Store a list of staged Dart files
# STAGED_DART_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep "\.dart$" || true)

# if [ -z "$STAGED_DART_FILES" ]; then
#   echo "No Dart files staged, skipping auto-fix and format"
#   exit 0
# fi

echo "Running dart fix --apply..."
dart fix --apply .

if [ $? -ne 0 ]; then
  echo "❌ dart fix command failed"
  exit 1
fi

echo "Running flutter format..."
dart format lib/ -l 120

if [ $? -ne 0 ]; then
  echo "❌ flutter format command failed"
  exit 1
fi

# Re-add formatted files to staging
echo "Re-adding fixed and formatted files to git staging area..."
echo "$STAGED_DART_FILES" | xargs git add

echo "✅ Pre-commit hooks completed successfully"
exit 0