import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:url_launcher/url_launcher.dart';

class SystemUtil {
  /// 使用系统浏览器访问url
  static void openUrlOnSystemBrowser({required String url}) async {
    try {
      // 1. URL预处理和检查
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://$url';
      }

      final uri = Uri.tryParse(url);
      if (uri == null) {
        GPEasyLoading.showToast("无效地址");
        return;
      }

      GPEasyLoading.showLoading();

      final canLaunch = await canLaunchUrl(uri);

      if (canLaunch) {
        await launchUrl(uri);
      } else {
        throw '$uri 无法打开';
      }

      GPEasyLoading.dismiss();
    } catch (e) {
      GPEasyLoading.showToast("打开浏览器失败 $e");
      GPEasyLoading.dismiss();
      rethrow; // 使用rethrow而不是throw Exception(e)以保留原始堆栈跟踪
    }
  }
}
