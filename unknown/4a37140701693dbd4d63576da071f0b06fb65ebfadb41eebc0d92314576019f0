import 'package:flutter/material.dart';

/// 图片网格布局代理类
/// 用于处理多图布局的排列和位置计算
class ImageGridLayoutDelegate extends MultiChildLayoutDelegate {
  final int imageCount;
  final double spacing;

  ImageGridLayoutDelegate({
    required this.imageCount,
    this.spacing = 1.2,
  });

  @override
  void performLayout(Size size) {
    if (imageCount <= 0) return;

    // Single image case
    // 单图布局处理
    if (imageCount == 1) {
      if (hasChild('image0')) {
        layoutChild('image0', BoxConstraints.tight(size));
        positionChild('image0', Offset.zero);
      }
      return;
    }

    // Generate optimal layout
    // 生成最优布局方案
    List<Rect> layout = _generateLayout(size);
    
    // Position all children according to layout
    // 根据布局方案放置所有子组件
    for (int i = 0; i < imageCount && i < layout.length; i++) {
      if (hasChild('image$i')) {
        final rect = layout[i];
        layoutChild('image$i', BoxConstraints.tight(rect.size));
        positionChild('image$i', rect.topLeft);
      }
    }
  }

  /// 生成布局方案
  /// 根据图片数量返回对应的矩形区域列表
  List<Rect> _generateLayout(Size size) {
    List<Rect> cells = [];
    
    // Handle special cases with optimized layouts
    // 处理特殊数量的优化布局
    
    // 两图布局：左右均分
    if (imageCount == 2) {
      double halfWidth = (size.width - spacing) / 2;
      cells.add(Rect.fromLTWH(0, 0, halfWidth, size.height));
      cells.add(Rect.fromLTWH(halfWidth + spacing, 0, halfWidth, size.height));
      return cells;
    }
    
    // 三图布局：左侧一张大图，右侧两张小图
    if (imageCount == 3) {
      double leftWidth = size.width * 0.6;
      double rightWidth = size.width - leftWidth - spacing;
      double rightHeight = (size.height - spacing) / 2;
      
      cells.add(Rect.fromLTWH(0, 0, leftWidth, size.height));
      cells.add(Rect.fromLTWH(leftWidth + spacing, 0, rightWidth, rightHeight));
      cells.add(Rect.fromLTWH(leftWidth + spacing, rightHeight + spacing, rightWidth, rightHeight));
      return cells;
    }
    
    // 五图布局：上方两张大图，下方三张小图
    if (imageCount == 5) {
      double topHeight = size.height * 0.6;
      double bottomHeight = size.height - topHeight - spacing;
      double topWidth = (size.width - spacing) / 2;
      double bottomWidth = (size.width - spacing * 2) / 3;
      
      cells.add(Rect.fromLTWH(0, 0, topWidth, topHeight));
      cells.add(Rect.fromLTWH(topWidth + spacing, 0, topWidth, topHeight));
      
      cells.add(Rect.fromLTWH(0, topHeight + spacing, bottomWidth, bottomHeight));
      cells.add(Rect.fromLTWH(bottomWidth + spacing, topHeight + spacing, bottomWidth, bottomHeight));
      cells.add(Rect.fromLTWH((bottomWidth + spacing) * 2, topHeight + spacing, bottomWidth, bottomHeight));
      return cells;
    }
    
    // Use dynamic algorithm for all other cases
    // 其他数量使用动态算法计算布局
    int columns = _calculateOptimalColumns(imageCount);
    return _createBalancedGrid(size, columns);
  }
  
  /// 创建均衡网格布局
  /// 处理最后一行的特殊情况，确保布局美观
  List<Rect> _createBalancedGrid(Size size, int columns) {
    List<Rect> cells = [];
    int rows = (imageCount / columns).ceil();
    
    // Determine if we need a balanced layout for the last row
    // 判断是否需要对最后一行进行均衡处理
    int itemsInLastRow = imageCount % columns;
    bool needsBalancing = itemsInLastRow > 0;
    
    // Calculate regular cell dimensions
    // 计算常规单元格尺寸
    double cellWidth = (size.width - (columns - 1) * spacing) / columns;
    double cellHeight = (size.height - (rows - 1) * spacing) / rows;
    
    for (int i = 0; i < imageCount; i++) {
      int row = i ~/ columns;
      int col = i % columns;
      
      // Calculate position and size
      // 计算位置和尺寸
      double x, y, width, height;
      y = row * (cellHeight + spacing);
      height = cellHeight;
      
      // Handle last row balancing
      // 处理最后一行的均衡布局
      if (needsBalancing && row == rows - 1) {
        width = (size.width - (itemsInLastRow - 1) * spacing) / itemsInLastRow;
        x = col * (width + spacing);
      } else {
        width = cellWidth;
        x = col * (width + spacing);
      }
      
      cells.add(Rect.fromLTWH(x, y, width, height));
    }
    
    return cells;
  }
  
  /// 计算最优列数
  /// 根据图片总数返回最适合的列数
  int _calculateOptimalColumns(int count) {
    // Determine optimal column count based on image count
    if (count <= 2) return count;
    if (count <= 4) return 2;
    if (count <= 9) return 3;
    if (count <= 16) return 4;
    return 5;
  }

  @override
  bool shouldRelayout(covariant ImageGridLayoutDelegate oldDelegate) {
    return oldDelegate.imageCount != imageCount || oldDelegate.spacing != spacing;
  }
}