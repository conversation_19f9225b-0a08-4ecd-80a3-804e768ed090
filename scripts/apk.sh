#!/bin/bash -ilex

# 保证 fvm 可用
export PATH="/opt/homebrew/bin:$PATH"
#设置 flutter sdk 版本
echo "设置 flutter sdk 版本 >>> 3.29.3"
fvm use 3.29.3

#获取Git SHA1
GIT_SHA1=`(git show-ref --head --hash=8 2> /dev/null || echo 00000000) | head -n1`

# 是否为测试环境
if [[ "$environment" == "release" ]]; then
   debugValue=false
else
   debugValue=true
fi

#默认打包类型
DEFAULT_ENVIRONMENT=release
#默认flavor
DEFAULT_FLAVOR=rsyp

#打包类型
if [ ! $environment ]; then
   type=DEFAULT_ENVIRONMENT
else
   type=$environment
fi

#设置flavor
if [ ! $flavor ]; then
   flavor=$DEFAULT_FLAVOR
else
   flavor=$flavor
fi

#执行静态资源切换至指定$flavor
echo "执行静态资源切换至$flavor"
./scripts/switch_flavor.sh $flavor

#获取版本号
versionCode=""
version=""
versionCodeSpe=""
pubspecFile="pubspec.yaml"
if [ -e $pubspecFile ]
  then
      if [ -r $pubspecFile ]
        then
          if [ -w $pubspecFile ]
            then
              #获取版本号
              start_line=`grep -n "version: " $pubspecFile | head -1 | cut -d ":" -f 1`
              version=`sed -n "${start_line}p"  $pubspecFile`
              echo $version
          else
            echo "文件不可写"
          fi
      else
         echo "文件不可读"
      fi
else
   echo "文件不存在"
fi

#对IFS变量 进行替换处理 拆分分号
OLD_IFS="$IFS"
IFS=": "
array=($version)
IFS="$OLD_IFS"
#拆分加号
OLD_IFS="$IFS"
IFS="+"
array=(${array[1]})
IFS="$OLD_IFS"
versionName=${array[0]}
#versionCode=`expr "${versionName}"|sed "s/\.*//g"`
versionCode=${array[1]}

#指定版本号
if [ ! $buildVersionName ]; then
  echo "没有指定版本，默认使用配置文件的版本"
else
  versionName=$buildVersionName
fi

time=$(date "+%m%d")
test=Test

# 保证 fvm 可用
export PATH="/opt/homebrew/bin:$PATH"
#执行打包
fvm flutter build apk --flavor $flavor -t lib/main_$flavor.dart --release --obfuscate --split-debug-info=xx --build-name=$versionName --build-number=$versionCode

ARCHIVE_DIR="$HOME/Desktop/Archive/$flavor/$type"
OLD_APK_DIR="$ARCHIVE_DIR/old_apk/$type"
BUILD_APK_PATH="build/app/outputs/flutter-apk/app-${flavor}-release.apk"
NEW_APK_NAME="${flavor}_${versionName}_b${versionCode}_${GIT_SHA1}_${environment}.apk"

# 检查 Archive 目录是否存在
if [[ -d "$ARCHIVE_DIR" ]]; then
  echo "检查 $ARCHIVE_DIR 目录中的 APK 文件..."
  if ls "$ARCHIVE_DIR"/*.apk 1> /dev/null 2>&1; then
    echo "发现旧的 APK 文件，准备移动到 $OLD_APK_DIR..."
    # 确保 old_apk 目录存在
    if [[ ! -d "$OLD_APK_DIR" ]]; then
      echo "目录 $OLD_APK_DIR 不存在，正在创建..."
      mkdir -p "$OLD_APK_DIR"
      echo "目录 $OLD_APK_DIR 已创建。"
    fi
    mv "$ARCHIVE_DIR"/*.apk "$OLD_APK_DIR/"
    echo "旧的 APK 文件已移动到 $OLD_APK_DIR。"
  else
    echo "没有发现旧的 APK 文件。"
  fi
else
  echo "目录 $ARCHIVE_DIR 不存在，正在创建..."
  mkdir -p "$ARCHIVE_DIR"
fi

# 修改包名称并移动到目标目录
if [[ -f "$BUILD_APK_PATH" ]]; then
  echo "正在重命名并移动 APK 文件..."
  mv "$BUILD_APK_PATH" "$ARCHIVE_DIR/$NEW_APK_NAME"
  echo "APK 文件已重命名为：$NEW_APK_NAME"
else
  echo "错误：未找到 APK 文件：$BUILD_APK_PATH"
  exit 1
fi

echo "执行构建APK完成！"

