#!/bin/bash

echo "📦 开始生成 manifest.plist"

# 参数校验
if [[ -z "$flavor" ]]; then
  echo "❌ 未设置 flavor 参数"
  exit 1
fi

if [[ -z "$environment" ]]; then
  echo "❌ 未设置 environment 参数"
  exit 1
fi

FLAVOR_LOWER=$(echo "$flavor" | tr '[:upper:]' '[:lower:]')
TYPE="$environment"

TARGET_PATH="/usr/local/var/www/${FLAVOR_LOWER}_${TYPE}"
mkdir -p "$TARGET_PATH/assets/"

IPA_PATH=$(find "$HOME/Desktop/Archive/$flavor/$TYPE" -maxdepth 1 -name "*.ipa" | head -n 1)

if [[ -z "$IPA_PATH" || ! -f "$IPA_PATH" ]]; then
  echo "⚠️ 找不到 IPA 文件"
  exit 1
fi

IPA_FILENAME=$(basename "$IPA_PATH")
rm -f "$TARGET_PATH"/*.ipa
cp -f "$IPA_PATH" "$TARGET_PATH"


# 获取本机 IP 地址作为 base_url
IP=$(ipconfig getifaddr en0 || ipconfig getifaddr en1)
echo "本机 IP 地址: $IP"
BASE_URL="https://${IP}/app/${FLAVOR_LOWER}/${TYPE}"
echo "✅ BASE_URL=$BASE_URL"
IPA_URL="$BASE_URL/${IPA_FILENAME}"

TMP_DIR=$(mktemp -d)
unzip -q "$IPA_PATH" -d "$TMP_DIR"

APP_DIR=$(find "$TMP_DIR/Payload" -type d -name "*.app" | head -n 1)
PLIST="$APP_DIR/Info.plist"

BUNDLE_ID=$(/usr/libexec/PlistBuddy -c "Print CFBundleIdentifier" "$PLIST")
VERSION=$(/usr/libexec/PlistBuddy -c "Print CFBundleShortVersionString" "$PLIST")
BUILD=$(/usr/libexec/PlistBuddy -c "Print CFBundleVersion" "$PLIST")
APP_NAME=$(/usr/libexec/PlistBuddy -c "Print CFBundleDisplayName" "$PLIST" 2>/dev/null)

if [[ -z "$APP_NAME" ]]; then
  APP_NAME=$(/usr/libexec/PlistBuddy -c "Print CFBundleName" "$PLIST")
fi

echo "生成 manifest.plist"
cat > "$TARGET_PATH/manifest.plist" <<EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>items</key>
  <array>
    <dict>
      <key>assets</key>
      <array>
        <dict>
          <key>kind</key>
          <string>software-package</string>
          <key>url</key>
          <string>$IPA_URL</string>
        </dict>
      </array>
      <key>metadata</key>
      <dict>
        <key>bundle-identifier</key>
        <string>${BUNDLE_ID}</string>
        <key>bundle-version</key>
        <string>${VERSION}</string>
        <key>kind</key>
        <string>software</string>
        <key>title</key>
        <string>${APP_NAME}</string>
      </dict>
    </dict>
  </array>
</dict>
</plist>
EOF
echo "✅ manifest.plist 已生成，App: ${APP_NAME}, Version: ${VERSION}, IP: ${IP}"
