#!/bin/bash

# Setup script for git hooks
# This script installs the git hooks for the Flutter project

# Create .git/hooks directory if it doesn't exist
mkdir -p .git/hooks

# Copy pre-commit hook
echo "Installing pre-commit hook..."
cp .vscode/git-hooks/pre-commit .githooks/pre-commit

# Make it executable
chmod +x .githooks/pre-commit

# Include additional hooks for linting (optional)
# echo "Creating lint pre-push hook..."
# cat > .githooks/pre-push << 'EOL'
# #!/bin/bash

# # Run Flutter analyzer before push
# echo "Running Flutter analyzer..."
# flutter analyze

# if [ $? -ne 0 ]; then
#   echo "❌ Flutter analyzer found issues. Please fix them before pushing."
#   exit 1
# fi

# echo "✅ Code analysis passed"
# exit 0
# EOL

# chmod +x .githooks/pre-push

# Create directory for .vscode integration
mkdir -p .vscode/git-hooks

# Copy hooks for version control
cp .githooks/pre-commit .vscode/git-hooks/pre-commit
# cp .githooks/pre-push .vscode/git-hooks/pre-push

echo "✅ Git hooks installed successfully!"
echo "Pre-commit hook will run dart fix --apply and flutter format for the feat/flavor-setup branch"
# echo "Pre-push hook will run flutter analyze for all branches"