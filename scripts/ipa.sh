#!/bin/bash -ilex
echo "设置PATH>>>"
export PATH="/opt/homebrew/bin:$PATH"
#设置 flutter sdk 版本
echo "设置 flutter sdk 版本 >>> 3.29.3"
fvm use 3.29.3

# 确保输出中文正常显示
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8

# 是否为测试环境
if [[ "$environment" == "release" ]]; then
   debugValue=false
else
   debugValue=true
fi

#获取Git SHA1
GIT_SHA1=`(git show-ref --head --hash=8 2> /dev/null || echo 00000000) | head -n1`
#默认打包类型
DEFAULT_ENVIRONMENT=release
#默认flavor
DEFAULT_FLAVOR=YL



#打包类型
if [ ! $environment ]; then
   type=DEFAULT_ENVIRONMENT
else
   type=$environment
fi

#设置flavor
if [ ! $flavor ]; then
   flavor=$DEFAULT_FLAVOR
else
   flavor=$flavor
fi

#执行静态资源切换至指定$flavor
echo "执行静态资源切换至$flavor"
./scripts/switch_flavor.sh $flavor

#获取版本号
versionCode=""
version=""
versionCodeSpe=""
pubspecFile="pubspec.yaml"
if [ -e $pubspecFile ]
  then
      if [ -r $pubspecFile ]
        then
          if [ -w $pubspecFile ]
            then
              #获取版本号
              start_line=`grep -n "version: " $pubspecFile | head -1 | cut -d ":" -f 1`
              version=`sed -n "${start_line}p"  $pubspecFile`
              echo $version
          else
            echo "文件不可写"
          fi
      else
         echo "文件不可读"
      fi
else
   echo "文件不存在"
fi

#对IFS变量 进行替换处理 拆分分号
OLD_IFS="$IFS"
IFS=": "
array=($version)
IFS="$OLD_IFS"
#拆分加号
OLD_IFS="$IFS"
IFS="+"
array=(${array[1]})
IFS="$OLD_IFS"
versionName=${array[0]}
#versionCode=`expr "${versionName}"|sed "s/\.*//g"`
versionCode=${array[1]}

#指定版本号
if [ ! $buildVersionName ]; then
  echo "没有指定版本，默认使用配置文件的版本"
else
  versionName=$buildVersionName
fi

# 保证 fvm 可用
export PATH="/opt/homebrew/bin:$PATH"
#执行打包
echo "执行打包 flutter build ios --flavor $flavor --t lib/main_$flavor.dart --release --build-name=$versionName --build-number=$versionCode --no-codesign"
fvm flutter build ios --flavor $flavor -t lib/main_$flavor.dart --release --build-name=$versionName --build-number=$versionCode --no-codesign

#生成 .xcarchive 文件
echo "生成 .xcarchive 文件 xcodebuild -workspace ios/Runner.xcworkspace -scheme $flavor -sdk iphoneos -configuration Release-$flavor -archivePath build/Runner.xcarchive archive"
FLUTTER_BUILD_MODE=release \
xcodebuild -workspace ios/Runner.xcworkspace \
    -scheme $flavor \
    -sdk iphoneos \
    -configuration Release-$flavor \
    -archivePath build/Runner.xcarchive \
    archive

#导出 .ipa 文件
echo "导出 $flavor flavor的 .ipa 文件 xcodebuild -exportArchive -archivePath build/Runner.xcarchive -exportPath build/Runner -exportOptionsPlist ios/$flavor/exportOptions.plist"
xcodebuild -exportArchive \
    -archivePath build/Runner.xcarchive \
    -exportPath build/Runner \
    -exportOptionsPlist ios/CI/exportOptions_$flavor.plist


#检查指定目录
ARCHIVE_DIR="$HOME/Desktop/Archive/$flavor/$type"
OLD_IPA_DIR="$ARCHIVE_DIR/old_ipa/$type"
NEW_IPA_NAME="${flavor}_${versionName}_b${versionCode}_${GIT_SHA1}_${environment}.ipa"

# 检查 Archive 目录是否存在
if [[ -d "$ARCHIVE_DIR" ]]; then
  echo "检查 $ARCHIVE_DIR 目录中的 IPA 文件..."
  if ls "$ARCHIVE_DIR"/*.ipa 1> /dev/null 2>&1; then
    echo "发现旧的 IPA 文件，准备移动到 $OLD_IPA_DIR..."
    # 确保 old_ipa 目录存在
    if [[ ! -d "$OLD_IPA_DIR" ]]; then
      echo "目录 $OLD_IPA_DIR 不存在，正在创建..."
      mkdir -p "$OLD_IPA_DIR"
      echo "目录 $OLD_IPA_DIR 已创建。"
    fi
    mv "$ARCHIVE_DIR"/*.ipa "$OLD_IPA_DIR/"
    echo "旧的 IPA 文件已移动到 $OLD_IPA_DIR。"
  else
    echo "没有发现旧的 IPA 文件。"
  fi
else
  echo "目录 $ARCHIVE_DIR 不存在，正在创建..."
  mkdir -p "$ARCHIVE_DIR"
fi

IPA_FILE=$(find build/Runner -name "*.ipa" | head -n 1)
if [[ -z "$IPA_FILE" ]]; then
  echo "❌ 未找到 .ipa 文件，请确认是否已成功构建"
  exit 1
fi

echo "正在重命名并移动 IPA 文件..."
echo "mv $IPA_FILE $ARCHIVE_DIR/$NEW_IPA_NAME"
mv "$IPA_FILE" "$ARCHIVE_DIR/$NEW_IPA_NAME" || {
  echo "❌ 移动 IPA 文件失败：$IPA_FILE -> $ARCHIVE_DIR/$NEW_IPA_NAME"
  exit 1
}

echo "执行构建IPA完成！"

